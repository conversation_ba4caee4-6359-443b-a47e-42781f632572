/**
 * Document fixtures for E2E testing
 * 
 * Provides mock data for document upload, review, and management tests
 * including different document types, statuses, and validation scenarios.
 */

export interface TestDocument {
  id: string;
  userId: string;
  name: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  expiryDate?: Date;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  reviewedBy?: string;
  reviewedAt?: Date;
  rejectionReason?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface TestDocumentRequirement {
  id: string;
  serviceTypeId: string;
  name: string;
  description: string;
  required: boolean;
  acceptedFormats: string[];
  maxSize: number;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Document requirement fixtures
 */
export const DOCUMENT_REQUIREMENT_FIXTURES: Record<string, TestDocumentRequirement> = {
  passport: {
    id: 'req-passport-1',
    serviceTypeId: 'service-critical-skills-1',
    name: 'Passport',
    description: 'Valid passport with at least 6 months remaining',
    required: true,
    acceptedFormats: ['application/pdf', 'image/jpeg', 'image/png'],
    maxSize: 5 * 1024 * 1024, // 5MB
  },
  
  cv: {
    id: 'req-cv-1',
    serviceTypeId: 'service-critical-skills-1',
    name: 'Curriculum Vitae',
    description: 'Detailed CV showing relevant experience',
    required: true,
    acceptedFormats: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    maxSize: 2 * 1024 * 1024, // 2MB
  },
  
  jobOffer: {
    id: 'req-job-offer-1',
    serviceTypeId: 'service-critical-skills-1',
    name: 'Job Offer Letter',
    description: 'Official job offer from Irish employer',
    required: true,
    acceptedFormats: ['application/pdf'],
    maxSize: 3 * 1024 * 1024, // 3MB
  },
  
  qualifications: {
    id: 'req-qualifications-1',
    serviceTypeId: 'service-critical-skills-1',
    name: 'Educational Qualifications',
    description: 'Degree certificates and transcripts',
    required: true,
    acceptedFormats: ['application/pdf', 'image/jpeg', 'image/png'],
    maxSize: 10 * 1024 * 1024, // 10MB
  },
  
  bankStatement: {
    id: 'req-bank-statement-1',
    serviceTypeId: 'service-student-visa-1',
    name: 'Bank Statement',
    description: 'Recent bank statements showing financial capacity',
    required: true,
    acceptedFormats: ['application/pdf'],
    maxSize: 5 * 1024 * 1024, // 5MB
  },
  
  marriageCertificate: {
    id: 'req-marriage-cert-1',
    serviceTypeId: 'service-dependent-visa-1',
    name: 'Marriage Certificate',
    description: 'Official marriage certificate',
    required: false,
    acceptedFormats: ['application/pdf', 'image/jpeg', 'image/png'],
    maxSize: 3 * 1024 * 1024, // 3MB
  },
};

/**
 * Document fixtures for different scenarios
 */
export const DOCUMENT_FIXTURES: Record<string, TestDocument> = {
  pendingPassport: {
    id: 'doc-passport-1',
    userId: 'user-test-id-1',
    name: 'passport_john_doe.pdf',
    originalName: 'passport.pdf',
    mimeType: 'application/pdf',
    size: 2048576, // 2MB
    url: 'https://test-storage.com/documents/passport_john_doe.pdf',
    expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
    status: 'PENDING',
  },
  
  approvedCV: {
    id: 'doc-cv-1',
    userId: 'user-test-id-1',
    name: 'cv_john_doe.pdf',
    originalName: 'John_Doe_CV.pdf',
    mimeType: 'application/pdf',
    size: 1048576, // 1MB
    url: 'https://test-storage.com/documents/cv_john_doe.pdf',
    status: 'APPROVED',
    reviewedBy: 'agent-test-id-1',
    reviewedAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
  },
  
  rejectedJobOffer: {
    id: 'doc-job-offer-1',
    userId: 'user-test-id-1',
    name: 'job_offer_john_doe.pdf',
    originalName: 'job_offer.pdf',
    mimeType: 'application/pdf',
    size: 512000, // 500KB
    url: 'https://test-storage.com/documents/job_offer_john_doe.pdf',
    status: 'REJECTED',
    reviewedBy: 'agent-test-id-1',
    reviewedAt: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
    rejectionReason: 'Job offer letter is missing salary details and start date',
  },
  
  expiredDocument: {
    id: 'doc-expired-1',
    userId: 'user-test-id-1',
    name: 'old_passport.pdf',
    originalName: 'old_passport.pdf',
    mimeType: 'application/pdf',
    size: 1500000, // 1.5MB
    url: 'https://test-storage.com/documents/old_passport.pdf',
    expiryDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago (expired)
    status: 'PENDING',
  },
  
  largeDocument: {
    id: 'doc-large-1',
    userId: 'user-test-id-1',
    name: 'qualifications_bundle.pdf',
    originalName: 'all_qualifications.pdf',
    mimeType: 'application/pdf',
    size: 8 * 1024 * 1024, // 8MB
    url: 'https://test-storage.com/documents/qualifications_bundle.pdf',
    status: 'PENDING',
  },
};

/**
 * Document upload test scenarios
 */
export const DOCUMENT_UPLOAD_SCENARIOS = {
  validPDF: {
    fieldname: 'file',
    originalname: 'test_document.pdf',
    encoding: '7bit',
    mimetype: 'application/pdf',
    size: 1024000, // 1MB
    buffer: Buffer.from('Mock PDF content'),
  },
  
  validImage: {
    fieldname: 'file',
    originalname: 'passport_photo.jpg',
    encoding: '7bit',
    mimetype: 'image/jpeg',
    size: 512000, // 500KB
    buffer: Buffer.from('Mock JPEG content'),
  },
  
  oversizedFile: {
    fieldname: 'file',
    originalname: 'large_document.pdf',
    encoding: '7bit',
    mimetype: 'application/pdf',
    size: 20 * 1024 * 1024, // 20MB (exceeds limit)
    buffer: Buffer.from('Mock large PDF content'),
  },
  
  invalidFormat: {
    fieldname: 'file',
    originalname: 'document.txt',
    encoding: '7bit',
    mimetype: 'text/plain',
    size: 1000,
    buffer: Buffer.from('Plain text content'),
  },
  
  emptyFile: {
    fieldname: 'file',
    originalname: 'empty.pdf',
    encoding: '7bit',
    mimetype: 'application/pdf',
    size: 0,
    buffer: Buffer.from(''),
  },
};

/**
 * Document upload DTOs
 */
export const DOCUMENT_UPLOAD_DTOS = {
  valid: {
    name: 'Test Document',
    expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year from now
  },
  
  withUserId: {
    name: 'Test Document for User',
    userId: 'user-test-id-2',
    expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
  },
  
  noExpiry: {
    name: 'Document without expiry',
  },
  
  invalidUserId: {
    name: 'Test Document',
    userId: 'invalid-user-id',
  },
  
  missingName: {
    expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
  },
};

/**
 * Document review scenarios
 */
export const DOCUMENT_REVIEW_SCENARIOS = {
  approve: {
    status: 'APPROVED',
    reviewNotes: 'Document is valid and meets all requirements',
  },
  
  reject: {
    status: 'REJECTED',
    reviewNotes: 'Document is unclear and needs to be resubmitted with better quality',
    rejectionReason: 'Poor image quality, text is not readable',
  },
  
  rejectExpired: {
    status: 'REJECTED',
    reviewNotes: 'Document has expired',
    rejectionReason: 'Document expiry date has passed',
  },
  
  rejectIncomplete: {
    status: 'REJECTED',
    reviewNotes: 'Document is missing required information',
    rejectionReason: 'Missing signature and date on the document',
  },
};

/**
 * Mock Prisma document data
 */
export function mockPrismaDocument(document: TestDocument, includeUser = true) {
  const baseData = {
    id: document.id,
    userId: document.userId,
    name: document.name,
    originalName: document.originalName,
    mimeType: document.mimeType,
    size: document.size,
    url: document.url,
    expiryDate: document.expiryDate || null,
    status: document.status,
    reviewedBy: document.reviewedBy || null,
    reviewedAt: document.reviewedAt || null,
    rejectionReason: document.rejectionReason || null,
    createdAt: document.createdAt || new Date(),
    updatedAt: document.updatedAt || new Date(),
  };

  if (!includeUser) {
    return baseData;
  }

  return {
    ...baseData,
    user: {
      id: document.userId,
      name: 'Test User',
      email: '<EMAIL>',
    },
  };
}

/**
 * Generate multiple documents for testing
 */
export function generateDocumentList(count: number, userId: string = 'user-test-id-1'): TestDocument[] {
  const documents: TestDocument[] = [];
  const statuses = ['PENDING', 'APPROVED', 'REJECTED'] as const;
  const mimeTypes = ['application/pdf', 'image/jpeg', 'image/png'];
  
  for (let i = 1; i <= count; i++) {
    documents.push({
      id: `doc-test-${i}`,
      userId,
      name: `test_document_${i}.pdf`,
      originalName: `document_${i}.pdf`,
      mimeType: mimeTypes[i % mimeTypes.length],
      size: 1024000 + (i * 100000), // Varying sizes
      url: `https://test-storage.com/documents/test_document_${i}.pdf`,
      status: statuses[i % statuses.length],
      createdAt: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
      updatedAt: new Date(),
    });
  }
  
  return documents;
}

/**
 * Document filter scenarios
 */
export const DOCUMENT_FILTER_SCENARIOS = {
  byStatus: {
    status: 'PENDING',
  },
  
  byMimeType: {
    mimeType: 'application/pdf',
  },
  
  byUser: {
    userId: 'user-test-id-1',
  },
  
  byDateRange: {
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    endDate: new Date(),
  },
  
  sortByUploadDate: {
    sortBy: 'createdAt',
    sortOrder: 'desc',
  },
  
  sortBySize: {
    sortBy: 'size',
    sortOrder: 'asc',
  },
};
