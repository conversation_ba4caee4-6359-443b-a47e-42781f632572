import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../utils/prisma.service';
import { MailerService } from '../mailer/mailer.service';
import { SecurityEventType, SecuritySeverity } from '@prisma/client';
import * as geoip from 'geoip-lite';

/**
 * Security Monitoring and Alerting Service
 *
 * Monitors security events and triggers alerts for suspicious activities.
 * Implements threat detection and automated response mechanisms.
 *
 * Features:
 * - Real-time security event monitoring
 * - Automated threat detection
 * - Alert generation and notification
 * - Security metrics and reporting
 * - Incident response automation
 */
@Injectable()
export class SecurityMonitoringService {
  private readonly logger = new Logger(SecurityMonitoringService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly mailerService: MailerService,
  ) {}

  /**
   * Create a security event and trigger alerts if necessary
   */
  async createSecurityEvent(params: {
    eventType: SecurityEventType;
    severity: SecuritySeverity;
    title: string;
    description: string;
    userId?: string;
    ipAddress?: string;
    userAgent?: string;
    metadata?: any;
  }): Promise<void> {
    try {
      const {
        eventType,
        severity,
        title,
        description,
        userId,
        ipAddress,
        userAgent,
        metadata,
      } = params;

      // Get geographic location from IP
      const location = ipAddress ? this.getLocationFromIP(ipAddress) : null;

      // Create the security event
      const securityEvent = await this.prisma.security_event.create({
        data: {
          eventType,
          severity,
          title,
          description,
          userId,
          ipAddress,
          userAgent,
          location,
          metadata: metadata ? JSON.parse(JSON.stringify(metadata)) : null,
        },
      });

      this.logger.warn(`Security event created: ${title} (${severity})`);

      // Trigger alerts for high severity events
      if (
        severity === SecuritySeverity.HIGH ||
        severity === SecuritySeverity.CRITICAL
      ) {
        await this.sendSecurityAlert(securityEvent);
      }

      // Check for patterns that require immediate attention
      await this.checkSecurityPatterns(eventType, userId, ipAddress);
    } catch (error) {
      this.logger.error(
        `Failed to create security event: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Monitor failed authentication attempts
   */
  async monitorFailedAuthentication(params: {
    email?: string;
    ipAddress: string;
    userAgent?: string;
    failureReason: string;
  }): Promise<void> {
    const { email, ipAddress, userAgent, failureReason } = params;

    // Check for multiple failed attempts from same IP
    const recentFailures = await this.prisma.failed_auth_attempt.count({
      where: {
        ipAddress,
        attemptedAt: {
          gte: new Date(Date.now() - 15 * 60 * 1000), // Last 15 minutes
        },
      },
    });

    if (recentFailures >= 5) {
      await this.createSecurityEvent({
        eventType: SecurityEventType.MULTIPLE_FAILED_LOGINS,
        severity: SecuritySeverity.HIGH,
        title: 'Multiple Failed Login Attempts',
        description: `${recentFailures} failed login attempts from IP ${ipAddress} in the last 15 minutes`,
        ipAddress,
        userAgent,
        metadata: {
          failureCount: recentFailures,
          email,
          failureReason,
          timeWindow: '15 minutes',
        },
      });
    }

    // Check for account-specific attacks
    if (email) {
      const accountFailures = await this.prisma.failed_auth_attempt.count({
        where: {
          email,
          attemptedAt: {
            gte: new Date(Date.now() - 60 * 60 * 1000), // Last hour
          },
        },
      });

      if (accountFailures >= 10) {
        await this.createSecurityEvent({
          eventType: SecurityEventType.ACCOUNT_LOCKOUT,
          severity: SecuritySeverity.MEDIUM,
          title: 'Account Under Attack',
          description: `${accountFailures} failed login attempts for account ${email} in the last hour`,
          ipAddress,
          userAgent,
          metadata: {
            email,
            failureCount: accountFailures,
            timeWindow: '1 hour',
          },
        });
      }
    }
  }

  /**
   * Monitor suspicious activity patterns
   */
  async monitorSuspiciousActivity(params: {
    userId: string;
    action: string;
    ipAddress?: string;
    userAgent?: string;
    metadata?: any;
  }): Promise<void> {
    const { userId, action, ipAddress, userAgent, metadata } = params;

    // Check for unusual location access
    if (ipAddress) {
      await this.checkUnusualLocation(userId, ipAddress, userAgent, metadata);
    }

    // Check for unusual time access
    await this.checkUnusualTime(userId, action, metadata);

    // Check for rapid successive actions
    await this.checkRapidActions(userId, action, metadata);
  }

  /**
   * Monitor document access violations
   */
  async monitorDocumentAccess(params: {
    userId: string;
    documentId: string;
    action: string;
    ipAddress?: string;
    userAgent?: string;
    authorized: boolean;
  }): Promise<void> {
    const { userId, documentId, action, ipAddress, userAgent, authorized } =
      params;

    if (!authorized) {
      await this.createSecurityEvent({
        eventType: SecurityEventType.DOCUMENT_ACCESS_VIOLATION,
        severity: SecuritySeverity.HIGH,
        title: 'Unauthorized Document Access Attempt',
        description: `User ${userId} attempted unauthorized ${action} on document ${documentId}`,
        userId,
        ipAddress,
        userAgent,
        metadata: {
          documentId,
          action,
          authorized,
        },
      });
    }
  }

  /**
   * Monitor API abuse patterns
   */
  async monitorAPIAbuse(params: {
    userId?: string;
    ipAddress: string;
    endpoint: string;
    requestCount: number;
    timeWindow: string;
  }): Promise<void> {
    const { userId, ipAddress, endpoint, requestCount, timeWindow } = params;

    if (requestCount > 100) {
      // Configurable threshold
      await this.createSecurityEvent({
        eventType: SecurityEventType.API_ABUSE,
        severity: SecuritySeverity.MEDIUM,
        title: 'API Rate Limit Exceeded',
        description: `${requestCount} requests to ${endpoint} from IP ${ipAddress} in ${timeWindow}`,
        userId,
        ipAddress,
        metadata: {
          endpoint,
          requestCount,
          timeWindow,
        },
      });
    }
  }

  /**
   * Get security events with filtering
   */
  async getSecurityEvents(params: {
    eventType?: SecurityEventType;
    severity?: SecuritySeverity;
    userId?: string;
    ipAddress?: string;
    resolved?: boolean;
    startDate?: Date;
    endDate?: Date;
    page?: number;
    limit?: number;
  }) {
    const {
      eventType,
      severity,
      userId,
      ipAddress,
      resolved,
      startDate,
      endDate,
      page = 1,
      limit = 50,
    } = params;

    const where: any = {};

    if (eventType) where.eventType = eventType;
    if (severity) where.severity = severity;
    if (userId) where.userId = userId;
    if (ipAddress) where.ipAddress = ipAddress;
    if (resolved !== undefined) where.resolved = resolved;

    if (startDate || endDate) {
      where.timestamp = {};
      if (startDate) where.timestamp.gte = startDate;
      if (endDate) where.timestamp.lte = endDate;
    }

    const skip = (page - 1) * limit;

    const [events, total] = await Promise.all([
      this.prisma.security_event.findMany({
        where,
        skip,
        take: limit,
        orderBy: { timestamp: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      }),
      this.prisma.security_event.count({ where }),
    ]);

    return {
      data: events,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Resolve a security event
   */
  async resolveSecurityEvent(
    eventId: string,
    resolvedBy: string,
    resolution?: string,
  ): Promise<void> {
    await this.prisma.security_event.update({
      where: { id: eventId },
      data: {
        resolved: true,
        resolvedBy,
        resolvedAt: new Date(),
        metadata: {
          resolution,
        },
      },
    });

    this.logger.log(`Security event ${eventId} resolved by ${resolvedBy}`);
  }

  /**
   * Get security metrics and statistics
   */
  async getSecurityMetrics(params: { startDate?: Date; endDate?: Date }) {
    const { startDate, endDate } = params;

    const where: any = {};
    if (startDate || endDate) {
      where.timestamp = {};
      if (startDate) where.timestamp.gte = startDate;
      if (endDate) where.timestamp.lte = endDate;
    }

    const [
      totalEvents,
      criticalEvents,
      highEvents,
      unresolvedEvents,
      eventsByType,
      eventsBySeverity,
      recentFailedLogins,
    ] = await Promise.all([
      this.prisma.security_event.count({ where }),
      this.prisma.security_event.count({
        where: { ...where, severity: SecuritySeverity.CRITICAL },
      }),
      this.prisma.security_event.count({
        where: { ...where, severity: SecuritySeverity.HIGH },
      }),
      this.prisma.security_event.count({
        where: { ...where, resolved: false },
      }),
      this.prisma.security_event.groupBy({
        by: ['eventType'],
        where,
        _count: { eventType: true },
        orderBy: { _count: { eventType: 'desc' } },
      }),
      this.prisma.security_event.groupBy({
        by: ['severity'],
        where,
        _count: { severity: true },
        orderBy: { _count: { severity: 'desc' } },
      }),
      this.prisma.failed_auth_attempt.count({
        where: {
          attemptedAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          },
        },
      }),
    ]);

    return {
      totalEvents,
      criticalEvents,
      highEvents,
      unresolvedEvents,
      recentFailedLogins,
      resolutionRate:
        totalEvents > 0
          ? (((totalEvents - unresolvedEvents) / totalEvents) * 100).toFixed(2)
          : '100.00',
      eventsByType: eventsByType.map((item) => ({
        type: item.eventType,
        count: item._count.eventType,
      })),
      eventsBySeverity: eventsBySeverity.map((item) => ({
        severity: item.severity,
        count: item._count.severity,
      })),
    };
  }

  /**
   * Send security alert notification
   */
  private async sendSecurityAlert(securityEvent: any): Promise<void> {
    try {
      // In a real implementation, you would send this to security team
      // For now, we'll log it and mark as alert sent
      this.logger.error(
        `SECURITY ALERT: ${securityEvent.title} - ${securityEvent.description}`,
      );

      await this.prisma.security_event.update({
        where: { id: securityEvent.id },
        data: {
          alertSent: true,
          alertSentAt: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send security alert: ${error.message}`);
    }
  }

  /**
   * Check for security patterns that require attention
   */
  private async checkSecurityPatterns(
    eventType: SecurityEventType,
    userId?: string,
    ipAddress?: string,
  ): Promise<void> {
    // Check for escalating security events from same source
    if (ipAddress) {
      const recentEvents = await this.prisma.security_event.count({
        where: {
          ipAddress,
          timestamp: {
            gte: new Date(Date.now() - 60 * 60 * 1000), // Last hour
          },
        },
      });

      if (recentEvents >= 5) {
        await this.createSecurityEvent({
          eventType: SecurityEventType.SECURITY_SCAN_DETECTED,
          severity: SecuritySeverity.HIGH,
          title: 'Potential Security Scan Detected',
          description: `${recentEvents} security events from IP ${ipAddress} in the last hour`,
          ipAddress,
          metadata: {
            eventCount: recentEvents,
            timeWindow: '1 hour',
          },
        });
      }
    }
  }

  /**
   * Check for unusual location access
   */
  private async checkUnusualLocation(
    userId: string,
    ipAddress: string,
    userAgent?: string,
    metadata?: any,
  ): Promise<void> {
    const location = this.getLocationFromIP(ipAddress);
    if (
      !location ||
      location.includes('Local') ||
      location.includes('Private')
    ) {
      return;
    }

    // Get user's recent locations
    const recentLogs = await this.prisma.audit_log.findMany({
      where: {
        userId,
        timestamp: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        },
        location: { not: null },
      },
      select: { location: true },
      distinct: ['location'],
    });

    const recentLocations = recentLogs.map((log) => log.location);

    if (!recentLocations.includes(location)) {
      await this.createSecurityEvent({
        eventType: SecurityEventType.UNUSUAL_LOCATION,
        severity: SecuritySeverity.MEDIUM,
        title: 'Access from Unusual Location',
        description: `User ${userId} accessed from new location: ${location}`,
        userId,
        ipAddress,
        userAgent,
        metadata: {
          ...metadata,
          newLocation: location,
          recentLocations,
        },
      });
    }
  }

  /**
   * Check for unusual time access
   */
  private async checkUnusualTime(
    userId: string,
    action: string,
    metadata?: any,
  ): Promise<void> {
    const currentHour = new Date().getHours();

    // Consider 11 PM to 5 AM as unusual hours
    if (currentHour >= 23 || currentHour <= 5) {
      await this.createSecurityEvent({
        eventType: SecurityEventType.UNUSUAL_TIME,
        severity: SecuritySeverity.LOW,
        title: 'Access During Unusual Hours',
        description: `User ${userId} performed ${action} at ${currentHour}:00`,
        userId,
        metadata: {
          ...metadata,
          hour: currentHour,
          action,
        },
      });
    }
  }

  /**
   * Check for rapid successive actions
   */
  private async checkRapidActions(
    userId: string,
    action: string,
    metadata?: any,
  ): Promise<void> {
    const recentActions = await this.prisma.audit_log.count({
      where: {
        userId,
        action,
        timestamp: {
          gte: new Date(Date.now() - 5 * 60 * 1000), // Last 5 minutes
        },
      },
    });

    if (recentActions > 20) {
      // Configurable threshold
      await this.createSecurityEvent({
        eventType: SecurityEventType.SUSPICIOUS_ACTIVITY,
        severity: SecuritySeverity.MEDIUM,
        title: 'Rapid Successive Actions Detected',
        description: `User ${userId} performed ${action} ${recentActions} times in 5 minutes`,
        userId,
        metadata: {
          ...metadata,
          action,
          actionCount: recentActions,
          timeWindow: '5 minutes',
        },
      });
    }
  }

  /**
   * Get geographic location from IP address
   */
  private getLocationFromIP(ipAddress: string): string | null {
    try {
      if (
        ipAddress === '127.0.0.1' ||
        ipAddress === '::1' ||
        ipAddress.startsWith('192.168.') ||
        ipAddress.startsWith('10.')
      ) {
        return 'Local/Private Network';
      }

      const geo = geoip.lookup(ipAddress);
      if (geo) {
        return `${geo.city || 'Unknown City'}, ${geo.country || 'Unknown Country'}`;
      }
      return null;
    } catch (error) {
      return null;
    }
  }
}
