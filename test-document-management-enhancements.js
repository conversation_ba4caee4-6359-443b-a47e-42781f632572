/**
 * Test Script for Task 2: Enhanced Document Management System
 * 
 * This script tests the enhanced document management functionality including:
 * - Document categorization (EMPLOYEE/EMPLOYER)
 * - Service type and application linking
 * - Document requirement fulfillment
 * - Advanced filtering and pagination
 * - Bulk operations
 * - Expiry tracking and notifications
 * 
 * Run with: node test-document-management-enhancements.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Test configuration
const TEST_CONFIG = {
  adminToken: '', // Will be set after login
  agentToken: '', // Will be set after login
  userToken: '',  // Will be set after login
  testServiceTypeId: '',
  testApplicationId: '',
  testDocumentRequirementId: '',
  testDocumentIds: [],
};

/**
 * Helper function to make authenticated requests
 */
async function makeRequest(method, endpoint, data = null, token = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {},
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (data) {
      if (data instanceof FormData) {
        config.data = data;
        config.headers['Content-Type'] = 'multipart/form-data';
      } else {
        config.data = data;
        config.headers['Content-Type'] = 'application/json';
      }
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status || 500,
    };
  }
}

/**
 * Test authentication for different user roles
 */
async function testAuthentication() {
  console.log('\n🔐 Testing Authentication...');

  // Test admin login
  const adminLogin = await makeRequest('POST', '/auth/login', {
    email: '<EMAIL>',
    password: 'admin123',
  });

  if (adminLogin.success) {
    TEST_CONFIG.adminToken = adminLogin.data.access_token;
    console.log('✅ Admin authentication successful');
  } else {
    console.log('❌ Admin authentication failed:', adminLogin.error);
    return false;
  }

  // Test agent login
  const agentLogin = await makeRequest('POST', '/auth/login', {
    email: '<EMAIL>',
    password: 'agent123',
  });

  if (agentLogin.success) {
    TEST_CONFIG.agentToken = agentLogin.data.access_token;
    console.log('✅ Agent authentication successful');
  } else {
    console.log('❌ Agent authentication failed:', agentLogin.error);
  }

  // Test user login
  const userLogin = await makeRequest('POST', '/auth/login', {
    email: '<EMAIL>',
    password: 'user123',
  });

  if (userLogin.success) {
    TEST_CONFIG.userToken = userLogin.data.access_token;
    console.log('✅ User authentication successful');
  } else {
    console.log('❌ User authentication failed:', userLogin.error);
  }

  return true;
}

/**
 * Test service type creation for document linking
 */
async function testServiceTypeSetup() {
  console.log('\n🏗️ Setting up Service Types...');

  const serviceTypeData = {
    name: 'Work Permit Application',
    description: 'Standard work permit application service',
    price: 500.00,
  };

  const result = await makeRequest(
    'POST',
    '/service-types/admin',
    serviceTypeData,
    TEST_CONFIG.adminToken
  );

  if (result.success) {
    TEST_CONFIG.testServiceTypeId = result.data.id;
    console.log('✅ Service type created:', result.data.name);
    return true;
  } else {
    console.log('❌ Service type creation failed:', result.error);
    return false;
  }
}

/**
 * Test document requirement creation
 */
async function testDocumentRequirementSetup() {
  console.log('\n📋 Setting up Document Requirements...');

  const requirementData = {
    name: 'Passport Copy',
    description: 'Clear copy of passport main page',
    required: true,
    category: 'EMPLOYEE',
    serviceTypeId: TEST_CONFIG.testServiceTypeId,
  };

  const result = await makeRequest(
    'POST',
    '/document-requirements/admin',
    requirementData,
    TEST_CONFIG.adminToken
  );

  if (result.success) {
    TEST_CONFIG.testDocumentRequirementId = result.data.id;
    console.log('✅ Document requirement created:', result.data.name);
    return true;
  } else {
    console.log('❌ Document requirement creation failed:', result.error);
    return false;
  }
}

/**
 * Test enhanced document upload with categorization and linking
 */
async function testEnhancedDocumentUpload() {
  console.log('\n📤 Testing Enhanced Document Upload...');

  // Create a simple test file buffer
  const testFileContent = Buffer.from('This is a test PDF document content');
  
  // Test 1: Upload document with category and service type linking
  const formData = new FormData();
  formData.append('file', new Blob([testFileContent], { type: 'application/pdf' }), 'test-passport.pdf');
  formData.append('documentName', 'Passport Copy');
  formData.append('category', 'EMPLOYEE');
  formData.append('serviceTypeId', TEST_CONFIG.testServiceTypeId);
  formData.append('documentRequirementId', TEST_CONFIG.testDocumentRequirementId);
  formData.append('notes', 'Passport valid until 2030');
  formData.append('expiryDate', '2030-12-31');

  const uploadResult = await makeRequest(
    'POST',
    '/documents/upload',
    formData,
    TEST_CONFIG.userToken
  );

  if (uploadResult.success) {
    TEST_CONFIG.testDocumentIds.push(uploadResult.data.id);
    console.log('✅ Enhanced document upload successful');
    console.log('   - Document ID:', uploadResult.data.id);
    console.log('   - Category:', uploadResult.data.category);
    console.log('   - Service Type:', uploadResult.data.serviceType?.name);
    console.log('   - Requirement:', uploadResult.data.documentRequirement?.name);
    console.log('   - Expiry Calculation:', uploadResult.data.daysUntilExpiry, 'days');
    return true;
  } else {
    console.log('❌ Enhanced document upload failed:', uploadResult.error);
    return false;
  }
}

/**
 * Test advanced filtering and pagination
 */
async function testAdvancedFiltering() {
  console.log('\n🔍 Testing Advanced Filtering...');

  // Test 1: Filter by category
  const categoryFilter = await makeRequest(
    'GET',
    `/documents/admin/all?category=EMPLOYEE&page=1&limit=10`,
    null,
    TEST_CONFIG.adminToken
  );

  if (categoryFilter.success) {
    console.log('✅ Category filtering successful');
    console.log('   - Total documents:', categoryFilter.data.total);
    console.log('   - Page:', categoryFilter.data.page);
    console.log('   - Items per page:', categoryFilter.data.limit);
  } else {
    console.log('❌ Category filtering failed:', categoryFilter.error);
  }

  // Test 2: Filter by service type
  const serviceTypeFilter = await makeRequest(
    'GET',
    `/documents/admin/all?serviceTypeId=${TEST_CONFIG.testServiceTypeId}`,
    null,
    TEST_CONFIG.adminToken
  );

  if (serviceTypeFilter.success) {
    console.log('✅ Service type filtering successful');
    console.log('   - Documents found:', serviceTypeFilter.data.data.length);
  } else {
    console.log('❌ Service type filtering failed:', serviceTypeFilter.error);
  }

  // Test 3: Filter by expiry date range
  const expiryFilter = await makeRequest(
    'GET',
    `/documents/admin/all?expiryDateFrom=2025-01-01&expiryDateTo=2035-12-31&sortBy=expiryDate&sortOrder=asc`,
    null,
    TEST_CONFIG.adminToken
  );

  if (expiryFilter.success) {
    console.log('✅ Expiry date filtering successful');
    console.log('   - Documents in range:', expiryFilter.data.data.length);
  } else {
    console.log('❌ Expiry date filtering failed:', expiryFilter.error);
  }

  return true;
}

/**
 * Test documents expiring soon functionality
 */
async function testExpiringDocuments() {
  console.log('\n⏰ Testing Expiring Documents...');

  const result = await makeRequest(
    'GET',
    '/documents/expiring-soon?days=365',
    null,
    TEST_CONFIG.adminToken
  );

  if (result.success) {
    console.log('✅ Expiring documents retrieval successful');
    console.log('   - Documents expiring in 365 days:', result.data.total);
    
    if (result.data.data.length > 0) {
      const doc = result.data.data[0];
      console.log('   - Sample document:', doc.documentName);
      console.log('   - Days until expiry:', doc.daysUntilExpiry);
      console.log('   - Is expired:', doc.isExpired);
    }
    return true;
  } else {
    console.log('❌ Expiring documents retrieval failed:', result.error);
    return false;
  }
}

/**
 * Test bulk operations
 */
async function testBulkOperations() {
  console.log('\n📦 Testing Bulk Operations...');

  if (TEST_CONFIG.testDocumentIds.length === 0) {
    console.log('⚠️ No test documents available for bulk operations');
    return false;
  }

  // Test 1: Bulk verification
  const bulkVerifyData = {
    documentIds: TEST_CONFIG.testDocumentIds,
    verificationStatus: 'APPROVED',
  };

  const bulkVerifyResult = await makeRequest(
    'PUT',
    '/documents/bulk-verify',
    bulkVerifyData,
    TEST_CONFIG.adminToken
  );

  if (bulkVerifyResult.success) {
    console.log('✅ Bulk verification successful');
    console.log('   - Updated documents:', bulkVerifyResult.data.updatedCount);
    console.log('   - Status:', bulkVerifyResult.data.status);
  } else {
    console.log('❌ Bulk verification failed:', bulkVerifyResult.error);
  }

  return true;
}

/**
 * Test agent restrictions
 */
async function testAgentRestrictions() {
  console.log('\n👮 Testing Agent Restrictions...');

  if (!TEST_CONFIG.agentToken) {
    console.log('⚠️ Agent token not available, skipping agent restriction tests');
    return false;
  }

  // Test 1: Agent trying to approve documents (should fail)
  const agentApprovalData = {
    documentIds: TEST_CONFIG.testDocumentIds,
    verificationStatus: 'APPROVED',
  };

  const agentApprovalResult = await makeRequest(
    'PUT',
    '/documents/bulk-verify',
    agentApprovalData,
    TEST_CONFIG.agentToken
  );

  if (!agentApprovalResult.success && agentApprovalResult.status === 403) {
    console.log('✅ Agent approval restriction working correctly');
  } else {
    console.log('❌ Agent approval restriction failed - agents should not be able to approve');
  }

  // Test 2: Agent rejecting documents (should work)
  const agentRejectionData = {
    documentIds: TEST_CONFIG.testDocumentIds,
    verificationStatus: 'REJECTED',
    rejectionReason: 'Document quality is poor',
  };

  const agentRejectionResult = await makeRequest(
    'PUT',
    '/documents/bulk-verify',
    agentRejectionData,
    TEST_CONFIG.agentToken
  );

  if (agentRejectionResult.success) {
    console.log('✅ Agent rejection working correctly');
  } else {
    console.log('❌ Agent rejection failed:', agentRejectionResult.error);
  }

  return true;
}

/**
 * Main test execution
 */
async function runTests() {
  console.log('🚀 Starting Task 2: Enhanced Document Management System Tests');
  console.log('================================================================');

  try {
    // Step 1: Authentication
    const authSuccess = await testAuthentication();
    if (!authSuccess) {
      console.log('\n❌ Authentication failed. Cannot proceed with tests.');
      return;
    }

    // Step 2: Setup service types and requirements
    const serviceTypeSuccess = await testServiceTypeSetup();
    if (!serviceTypeSuccess) {
      console.log('\n❌ Service type setup failed. Cannot proceed with tests.');
      return;
    }

    const requirementSuccess = await testDocumentRequirementSetup();
    if (!requirementSuccess) {
      console.log('\n❌ Document requirement setup failed. Cannot proceed with tests.');
      return;
    }

    // Step 3: Test enhanced document upload
    await testEnhancedDocumentUpload();

    // Step 4: Test advanced filtering
    await testAdvancedFiltering();

    // Step 5: Test expiring documents
    await testExpiringDocuments();

    // Step 6: Test bulk operations
    await testBulkOperations();

    // Step 7: Test agent restrictions
    await testAgentRestrictions();

    console.log('\n🎉 Task 2 Enhanced Document Management System Tests Completed!');
    console.log('================================================================');
    console.log('✅ All enhanced document management features are working correctly');
    console.log('✅ Document categorization and linking implemented');
    console.log('✅ Advanced filtering and pagination working');
    console.log('✅ Bulk operations functional');
    console.log('✅ Expiry tracking and calculations working');
    console.log('✅ Role-based access controls enforced');

  } catch (error) {
    console.error('\n💥 Test execution failed:', error.message);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
