# End-to-End (E2E) Testing Suite

This directory contains comprehensive end-to-end tests for the Career Ireland Immigration SaaS platform. The tests simulate real-world HTTP requests using available API endpoints to validate the complete workflow from different user perspectives.

## 📁 Directory Structure

```
e2e/
├── admin/                    # Admin role test suites
│   ├── create-packages.spec.ts
│   └── prioritize-applications.spec.ts
├── user/                     # User role test suites
│   ├── register-purchase-package.spec.ts
│   └── upload-documents.spec.ts
├── agent/                    # Agent role test suites
│   └── review-documents.spec.ts
├── utils/                    # Shared utilities and helpers
│   ├── auth-helpers.ts       # Authentication and JWT utilities
│   ├── test-results.ts       # Test result collection and reporting
│   └── fixtures/             # Test data fixtures
│       ├── packages.ts
│       ├── applications.ts
│       └── documents.ts
├── output/                   # Generated test results and reports
│   ├── result-summary.json   # Latest test run summary
│   ├── test-report.html      # HTML test report
│   └── logs/                 # Detailed test logs
└── README.md                 # This file
```

## 🎯 Test Scenarios

### Admin Tests
- **Package Creation**: Validate package creation, field validation, and authorization
- **Application Prioritization**: Test priority assignment, queue management, and workflow advancement

### User Tests
- **Registration & Purchase**: Complete user journey from registration to package purchase
- **Document Upload**: File upload validation, document requirements, and status tracking

### Agent Tests
- **Document Review**: Document approval/rejection workflow with audit trails

## 🚀 Running Tests

### Run All E2E Tests
```bash
npm run test:e2e
```

### Run Tests by Role
```bash
# Admin tests only
npm run test:e2e:admin

# User tests only
npm run test:e2e:user

# Agent tests only
npm run test:e2e:agent
```

### Run Specific Test Suite
```bash
# Run specific test file
npm run test:e2e -- --testPathPattern="create-packages"

# Run with verbose output
npm run test:e2e -- --verbose

# Run with coverage
npm run test:e2e -- --coverage
```

## 📊 Test Results

### JSON Output
Test results are automatically saved in structured JSON format:
- `e2e/output/result-summary.json` - Latest test run summary
- `e2e/output/logs/` - Detailed logs for each test suite

### HTML Reports
Visual HTML reports are generated for easy review:
- `e2e/output/test-report.html` - Latest HTML report with pass/fail status

### CI/CD Integration
The JSON output format is designed for CI/CD pipeline integration:
```json
{
  "runId": "run-1640995200000",
  "environment": "test",
  "totalTests": 25,
  "passedTests": 23,
  "failedTests": 2,
  "duration": 45000,
  "suites": [...]
}
```

## 🔧 Test Configuration

### Authentication
Tests use JWT tokens for different roles:
- Admin: Full system access
- User: Standard user permissions
- Agent: Document review permissions

### Mock Data
Comprehensive fixtures provide realistic test data:
- Package configurations
- Application scenarios
- Document types and requirements

### HTTP Request Logging
All HTTP requests are logged with:
- Request method and URL
- Request/response bodies
- Response status codes
- Request duration
- Timestamps

## 🛠️ Development Guidelines

### Adding New Tests
1. Create test files in the appropriate role directory
2. Use the provided auth helpers for authentication
3. Import relevant fixtures for test data
4. Follow the existing test structure and naming conventions

### Test Structure
```typescript
describe('Test Suite Name (E2E)', () => {
  beforeAll(async () => {
    // Setup test application
    testResultCollector.clear();
  });

  afterAll(async () => {
    // Generate and save test results
    const suiteResults = testResultCollector.getSuiteResults('Suite Name');
    testResultReporter.saveResults(summary);
  });

  it('should test specific functionality', async () => {
    testResultCollector.startTest('Suite Name', 'Test Name');
    
    // Test implementation
    const response = await request(app.getHttpServer())
      .get('/endpoint')
      .set('Authorization', credentials.bearerToken);
    
    if (response.status === 200) {
      testResultCollector.endTest('PASS');
    } else {
      testResultCollector.endTest('FAIL', 'Error message', response.body);
    }
  });
});
```

### Best Practices
1. **Use Real HTTP Requests**: Tests make actual HTTP calls to endpoints
2. **Mock External Services**: Database and external APIs are mocked
3. **Test Authorization**: Verify proper access control for each endpoint
4. **Validate Responses**: Check both success and error scenarios
5. **Log Requests**: Use the test result collector for audit trails

## 🔍 Debugging Tests

### Verbose Output
```bash
npm run test:e2e -- --verbose --detectOpenHandles
```

### Debug Specific Test
```bash
npm run test:e2e -- --testNamePattern="should create valid package"
```

### View HTTP Logs
Check the generated logs in `e2e/output/logs/` for detailed HTTP request/response information.

## 📋 Test Coverage

The E2E tests cover the following workflows:

### Complete User Journey
1. User registration and email verification
2. User login and authentication
3. Browse available packages
4. Purchase package via Stripe
5. Upload required documents
6. Track application status

### Admin Operations
1. Create and manage packages
2. Set application priorities
3. Monitor system statistics
4. Manage user accounts

### Agent Workflows
1. Review pending documents
2. Approve/reject with reasons
3. Track review audit trails
4. Manage document queues

## 🚨 Troubleshooting

### Common Issues
1. **Authentication Failures**: Check JWT token generation in auth-helpers.ts
2. **Mock Data Issues**: Verify fixture data matches expected schema
3. **Timeout Errors**: Increase test timeout for slow operations
4. **Port Conflicts**: Ensure test server port is available

### Environment Variables
Required environment variables for testing:
```bash
NODE_ENV=test
JWT_SECRET=test-jwt-secret
SUPABASE_URL=test-supabase-url
SUPABASE_KEY=test-supabase-key
STRIPE_SECRET_KEY=test-stripe-key
```

## 📈 Metrics and Monitoring

### Test Metrics
- Total test execution time
- Pass/fail rates by test suite
- HTTP request performance
- Error categorization

### Performance Monitoring
- Average response times
- Slowest endpoints
- Resource usage during tests

## 🔄 Continuous Integration

### GitHub Actions Integration
```yaml
- name: Run E2E Tests
  run: |
    npm run test:e2e
    
- name: Upload Test Results
  uses: actions/upload-artifact@v2
  with:
    name: e2e-test-results
    path: e2e/output/
```

### Test Result Analysis
The structured JSON output enables automated analysis of test results in CI/CD pipelines, including:
- Trend analysis over time
- Regression detection
- Performance monitoring
- Failure categorization
