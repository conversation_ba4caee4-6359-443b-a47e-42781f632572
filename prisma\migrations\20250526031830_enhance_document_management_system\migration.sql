-- AlterTable
ALTER TABLE "user_document" ADD COLUMN     "applicationId" TEXT,
ADD COLUMN     "category" "DocCategory",
ADD COLUMN     "documentRequirementId" TEXT,
ADD COLUMN     "notes" TEXT,
ADD COLUMN     "serviceTypeId" TEXT;

-- AddF<PERSON>ignKey
ALTER TABLE "user_document" ADD CONSTRAINT "user_document_serviceTypeId_fkey" FOREIGN KEY ("serviceTypeId") REFERENCES "service_type"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_document" ADD CONSTRAINT "user_document_applicationId_fkey" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeign<PERSON>ey
ALTER TABLE "user_document" ADD CONSTRAINT "user_document_documentRequirementId_fkey" FOREI<PERSON>N KEY ("documentRequirementId") REFERENCES "document_requirement"("id") ON DELETE SET NULL ON UPDATE CASCADE;
