import { VerificationStatus } from '@prisma/client';

export class MockPrismaService {
  // Mock data storage
  private documents: any[] = [];
  private users: any[] = [];
  private admins: any[] = [];

  constructor() {
    // Initialize with empty arrays
    this.documents = [];
    this.users = [];
    this.admins = [];
  }

  // Method to seed test data
  seedTestData() {
    // Seed users
    this.users = [
      {
        id: 'user-1',
        email: '<EMAIL>',
        name: 'Test User',
        password: 'hashed-password',
        role: 'USER',
      },
      {
        id: 'user-2',
        email: '<EMAIL>',
        name: 'Another User',
        password: 'hashed-password',
        role: 'USER',
      },
    ];

    // Seed admins
    this.admins = [
      {
        id: 'admin-1',
        email: '<EMAIL>',
        name: 'Admin User',
        password: 'hashed-password',
        role: 'ADMIN',
      },
    ];

    // Seed documents
    this.documents = [
      {
        id: 'doc-1',
        documentName: 'Passport',
        fileUrl: 'https://example.com/documents/passport.pdf',
        verificationStatus: VerificationStatus.PENDING,
        userId: 'user-1',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'doc-2',
        documentName: 'Visa',
        fileUrl: 'https://example.com/documents/visa.pdf',
        verificationStatus: VerificationStatus.APPROVED,
        userId: 'user-1',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'doc-3',
        documentName: 'Work Permit',
        fileUrl: 'https://example.com/documents/work-permit.pdf',
        verificationStatus: VerificationStatus.REJECTED,
        rejectionReason: 'Document expired',
        userId: 'user-2',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];
  }

  // Mock Prisma client methods
  user_document = {
    findUnique: jest.fn((params) => {
      const { where } = params;
      return Promise.resolve(this.documents.find((doc) => doc.id === where.id));
    }),
    findFirst: jest.fn((params) => {
      const { where } = params;
      return Promise.resolve(
        this.documents.find(
          (doc) =>
            (where.id ? doc.id === where.id : true) &&
            (where.userId ? doc.userId === where.userId : true),
        ),
      );
    }),
    findMany: jest.fn((params) => {
      const { where, orderBy } = params;
      let results = [...this.documents];

      if (where) {
        if (where.userId) {
          results = results.filter((doc) => doc.userId === where.userId);
        }
        if (where.verificationStatus) {
          results = results.filter(
            (doc) => doc.verificationStatus === where.verificationStatus,
          );
        }
        if (where.documentName) {
          results = results.filter((doc) =>
            doc.documentName.includes(where.documentName),
          );
        }
      }

      return Promise.resolve(results);
    }),
    create: jest.fn((params) => {
      const { data } = params;
      const newDoc = {
        id: `doc-${this.documents.length + 1}`,
        ...data,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      this.documents.push(newDoc);
      return Promise.resolve(newDoc);
    }),
    update: jest.fn((params) => {
      const { where, data } = params;
      const index = this.documents.findIndex((doc) => doc.id === where.id);
      if (index === -1) {
        return Promise.resolve(null);
      }

      const updatedDoc = {
        ...this.documents[index],
        ...data,
        updatedAt: new Date(),
      };
      this.documents[index] = updatedDoc;
      return Promise.resolve(updatedDoc);
    }),
    delete: jest.fn((params) => {
      const { where } = params;
      const index = this.documents.findIndex((doc) => doc.id === where.id);
      if (index === -1) {
        return Promise.resolve(null);
      }

      const deletedDoc = this.documents[index];
      this.documents.splice(index, 1);
      return Promise.resolve(deletedDoc);
    }),
  };

  user = {
    findUnique: jest.fn((params) => {
      const { where } = params;
      return Promise.resolve(
        this.users.find(
          (user) =>
            (where.id ? user.id === where.id : false) ||
            (where.email ? user.email === where.email : false),
        ),
      );
    }),
    findFirst: jest.fn((params) => {
      const { where } = params;
      return Promise.resolve(
        this.users.find(
          (user) =>
            (where.id ? user.id === where.id : true) &&
            (where.email ? user.email === where.email : true),
        ),
      );
    }),
    findMany: jest.fn(() => {
      return Promise.resolve(this.users);
    }),
  };

  admin = {
    findUnique: jest.fn((params) => {
      const { where } = params;
      return Promise.resolve(
        this.admins.find(
          (admin) =>
            (where.id ? admin.id === where.id : false) ||
            (where.email ? admin.email === where.email : false),
        ),
      );
    }),
    findFirst: jest.fn((params) => {
      const { where } = params;
      return Promise.resolve(
        this.admins.find(
          (admin) =>
            (where.id ? admin.id === where.id : true) &&
            (where.email ? admin.email === where.email : true),
        ),
      );
    }),
    findMany: jest.fn(() => {
      return Promise.resolve(this.admins);
    }),
  };
}
