import { CanActivate, ExecutionContext } from '@nestjs/common';
import { Observable } from 'rxjs';

/**
 * Mock JWT Guard for testing
 *
 * This guard always returns true and sets the user in the request based on the token
 */
export class MockJwtGuard implements CanActivate {
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    // If no token, deny access for endpoints that require authentication
    if (!token) {
      // Check if this is a test for unauthenticated access
      const path = request.url;
      if (path.includes('/documents')) {
        return false; // Return 401 for document endpoints without token
      }
      return true; // Allow access to public endpoints
    }

    // Extract user ID from mock token format
    const userId = token.replace('mock-jwt-token-', '');

    // Set user in request based on token
    if (userId === 'user-1') {
      request.user = { id: 'user-1', email: '<EMAIL>' };
    } else if (userId === 'admin-1') {
      request.user = {
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'ADMIN',
      };
      request.userRole = 'admin';
    } else if (userId === 'different-user') {
      request.user = { id: 'different-user', email: '<EMAIL>' };
    }

    return true;
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}

/**
 * Mock JWT Admin Guard for testing
 */
export class MockJwtAdminGuard implements CanActivate {
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    // If no token or not admin token, deny access
    if (!token || !token.includes('admin-1')) {
      return false;
    }

    // Set admin user in request
    request.user = { id: 'admin-1', email: '<EMAIL>', role: 'ADMIN' };
    request.userRole = 'admin';

    return true;
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}

/**
 * Mock JWT Admin-Agent Guard for testing
 */
export class MockJwtAdminAgentGuard implements CanActivate {
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    // If no token, deny access
    if (!token) {
      return false;
    }

    // Extract user ID from mock token format
    const userId = token.replace('mock-jwt-token-', '');

    // Set user in request based on token
    if (userId === 'admin-1') {
      request.user = {
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'ADMIN',
      };
      request.userRole = 'admin';
      return true;
    } else if (userId === 'agent-1') {
      request.user = {
        id: 'agent-1',
        email: '<EMAIL>',
        role: 'AGENT',
      };
      request.userRole = 'agent';
      return true;
    }

    return false;
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
