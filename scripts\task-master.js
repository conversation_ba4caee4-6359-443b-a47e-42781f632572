#!/usr/bin/env node

/**
 * Task Master - Task Management Automation Script
 * 
 * This script helps automate task management processes for the Career Ireland project.
 * It provides commands to list, start, update, and complete tasks.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

class TaskMaster {
  constructor() {
    this.tasksJsonPath = path.join(__dirname, '../tasks/tasks.json');
    this.tasksDir = path.join(__dirname, '../tasks');
  }

  // Load tasks from JSON file
  loadTasks() {
    try {
      const tasksData = fs.readFileSync(this.tasksJsonPath, 'utf8');
      return JSON.parse(tasksData);
    } catch (error) {
      console.error(`${colors.red}Error loading tasks.json:${colors.reset}`, error.message);
      process.exit(1);
    }
  }

  // Load individual task file
  loadTaskFile(taskId) {
    const taskFilePath = path.join(this.tasksDir, `task_${taskId.toString().padStart(3, '0')}.txt`);
    try {
      return fs.readFileSync(taskFilePath, 'utf8');
    } catch (error) {
      console.error(`${colors.red}Error loading task file:${colors.reset}`, error.message);
      return null;
    }
  }

  // Update task file status
  updateTaskStatus(taskId, newStatus) {
    const taskFilePath = path.join(this.tasksDir, `task_${taskId.toString().padStart(3, '0')}.txt`);
    try {
      let content = fs.readFileSync(taskFilePath, 'utf8');
      content = content.replace(/# Status: \w+/, `# Status: ${newStatus}`);
      fs.writeFileSync(taskFilePath, content);
      console.log(`${colors.green}✅ Updated task ${taskId} status to: ${newStatus}${colors.reset}`);
    } catch (error) {
      console.error(`${colors.red}Error updating task status:${colors.reset}`, error.message);
    }
  }

  // List all tasks
  listTasks(filter = 'all') {
    const tasksData = this.loadTasks();
    
    console.log(`\n${colors.bright}${colors.blue}📋 Task List${colors.reset}\n`);
    
    tasksData.tasks.forEach(task => {
      const taskFile = this.loadTaskFile(task.id);
      let status = 'pending';
      
      if (taskFile) {
        const statusMatch = taskFile.match(/# Status: (\w+)/);
        if (statusMatch) {
          status = statusMatch[1].toLowerCase();
        }
      }
      
      // Apply filter
      if (filter !== 'all' && status !== filter.toLowerCase()) {
        return;
      }
      
      const statusColor = this.getStatusColor(status);
      const priorityColor = this.getPriorityColor(task.priority);
      
      console.log(`${colors.bright}Task ${task.id}:${colors.reset} ${task.title}`);
      console.log(`  Status: ${statusColor}${status.toUpperCase()}${colors.reset}`);
      console.log(`  Priority: ${priorityColor}${task.priority.toUpperCase()}${colors.reset}`);
      console.log(`  Dependencies: ${task.dependencies.length > 0 ? task.dependencies.join(', ') : 'None'}`);
      console.log('');
    });
  }

  // Show task details
  showTask(taskId) {
    const tasksData = this.loadTasks();
    const task = tasksData.tasks.find(t => t.id === parseInt(taskId));
    
    if (!task) {
      console.error(`${colors.red}Task ${taskId} not found${colors.reset}`);
      return;
    }
    
    const taskFile = this.loadTaskFile(taskId);
    
    console.log(`\n${colors.bright}${colors.blue}📋 Task ${taskId} Details${colors.reset}\n`);
    console.log(`${colors.bright}Title:${colors.reset} ${task.title}`);
    console.log(`${colors.bright}Priority:${colors.reset} ${this.getPriorityColor(task.priority)}${task.priority.toUpperCase()}${colors.reset}`);
    console.log(`${colors.bright}Dependencies:${colors.reset} ${task.dependencies.length > 0 ? task.dependencies.join(', ') : 'None'}`);
    
    if (taskFile) {
      const statusMatch = taskFile.match(/# Status: (\w+)/);
      const status = statusMatch ? statusMatch[1] : 'pending';
      console.log(`${colors.bright}Status:${colors.reset} ${this.getStatusColor(status)}${status.toUpperCase()}${colors.reset}`);
    }
    
    console.log(`\n${colors.bright}Description:${colors.reset}`);
    console.log(task.description);
    
    if (taskFile) {
      console.log(`\n${colors.bright}Detailed Information:${colors.reset}`);
      console.log(taskFile);
    }
  }

  // Start a task
  startTask(taskId) {
    const tasksData = this.loadTasks();
    const task = tasksData.tasks.find(t => t.id === parseInt(taskId));
    
    if (!task) {
      console.error(`${colors.red}Task ${taskId} not found${colors.reset}`);
      return;
    }
    
    // Check dependencies
    const incompleteDeps = this.checkDependencies(task.dependencies);
    if (incompleteDeps.length > 0) {
      console.error(`${colors.red}Cannot start task ${taskId}. Incomplete dependencies: ${incompleteDeps.join(', ')}${colors.reset}`);
      return;
    }
    
    // Update status
    this.updateTaskStatus(taskId, 'in-progress');
    
    // Create feature branch
    const branchName = `feature/task-${taskId.toString().padStart(3, '0')}-${task.title.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')}`;
    
    try {
      execSync(`git checkout -b ${branchName}`, { stdio: 'inherit' });
      console.log(`${colors.green}✅ Created feature branch: ${branchName}${colors.reset}`);
    } catch (error) {
      console.log(`${colors.yellow}⚠️  Could not create git branch (may already exist or git not available)${colors.reset}`);
    }
    
    console.log(`\n${colors.bright}${colors.green}🚀 Task ${taskId} started!${colors.reset}`);
    console.log(`\n${colors.bright}Next steps:${colors.reset}`);
    console.log(`1. Review task details: ${colors.cyan}node scripts/task-master.js show ${taskId}${colors.reset}`);
    console.log(`2. Follow implementation guide: ${colors.cyan}docs/TASK_IMPLEMENTATION_GUIDE.md${colors.reset}`);
    console.log(`3. Update status when complete: ${colors.cyan}node scripts/task-master.js complete ${taskId}${colors.reset}`);
  }

  // Complete a task
  completeTask(taskId) {
    this.updateTaskStatus(taskId, 'Completed');
    
    const completionSummary = `
# COMPLETION STATUS: ✅ COMPLETED (100%)
# Completion Date: ${new Date().toISOString().split('T')[0]}
# Implementation Summary:
- ✅ Task implementation completed
- ✅ All requirements fulfilled
- ✅ Tests implemented and passing
- ✅ Documentation updated
`;
    
    const taskFilePath = path.join(this.tasksDir, `task_${taskId.toString().padStart(3, '0')}.txt`);
    try {
      fs.appendFileSync(taskFilePath, completionSummary);
      console.log(`${colors.green}✅ Task ${taskId} marked as completed!${colors.reset}`);
      console.log(`\n${colors.bright}Don't forget to:${colors.reset}`);
      console.log(`1. Run all tests: ${colors.cyan}npm test && npm run test:e2e${colors.reset}`);
      console.log(`2. Update CHANGELOG.md`);
      console.log(`3. Create pull request`);
      console.log(`4. Update task summary in the completion section`);
    } catch (error) {
      console.error(`${colors.red}Error updating task completion:${colors.reset}`, error.message);
    }
  }

  // Check task dependencies
  checkDependencies(dependencies) {
    const incompleteDeps = [];
    
    dependencies.forEach(depId => {
      const taskFile = this.loadTaskFile(depId);
      if (taskFile) {
        const statusMatch = taskFile.match(/# Status: (\w+)/);
        const status = statusMatch ? statusMatch[1].toLowerCase() : 'pending';
        if (status !== 'completed') {
          incompleteDeps.push(depId);
        }
      } else {
        incompleteDeps.push(depId);
      }
    });
    
    return incompleteDeps;
  }

  // Get color for status
  getStatusColor(status) {
    switch (status.toLowerCase()) {
      case 'completed': return colors.green;
      case 'in-progress': return colors.yellow;
      case 'pending': return colors.red;
      default: return colors.reset;
    }
  }

  // Get color for priority
  getPriorityColor(priority) {
    switch (priority.toLowerCase()) {
      case 'high': return colors.red;
      case 'medium': return colors.yellow;
      case 'low': return colors.green;
      default: return colors.reset;
    }
  }

  // Show help
  showHelp() {
    console.log(`\n${colors.bright}${colors.blue}🎯 Task Master - Career Ireland Task Management${colors.reset}\n`);
    console.log(`${colors.bright}Usage:${colors.reset} node scripts/task-master.js <command> [options]\n`);
    console.log(`${colors.bright}Commands:${colors.reset}`);
    console.log(`  ${colors.cyan}list [filter]${colors.reset}     List all tasks (filter: all, pending, in-progress, completed)`);
    console.log(`  ${colors.cyan}show <id>${colors.reset}         Show detailed information for a specific task`);
    console.log(`  ${colors.cyan}start <id>${colors.reset}        Start working on a task (checks dependencies)`);
    console.log(`  ${colors.cyan}complete <id>${colors.reset}     Mark a task as completed`);
    console.log(`  ${colors.cyan}status <id> <status>${colors.reset} Update task status (pending, in-progress, completed)`);
    console.log(`  ${colors.cyan}help${colors.reset}             Show this help message\n`);
    console.log(`${colors.bright}Examples:${colors.reset}`);
    console.log(`  ${colors.yellow}node scripts/task-master.js list pending${colors.reset}`);
    console.log(`  ${colors.yellow}node scripts/task-master.js show 4${colors.reset}`);
    console.log(`  ${colors.yellow}node scripts/task-master.js start 4${colors.reset}`);
    console.log(`  ${colors.yellow}node scripts/task-master.js complete 4${colors.reset}\n`);
  }

  // Main command handler
  run() {
    const args = process.argv.slice(2);
    const command = args[0];
    
    switch (command) {
      case 'list':
        this.listTasks(args[1] || 'all');
        break;
      case 'show':
        if (!args[1]) {
          console.error(`${colors.red}Please provide a task ID${colors.reset}`);
          return;
        }
        this.showTask(args[1]);
        break;
      case 'start':
        if (!args[1]) {
          console.error(`${colors.red}Please provide a task ID${colors.reset}`);
          return;
        }
        this.startTask(args[1]);
        break;
      case 'complete':
        if (!args[1]) {
          console.error(`${colors.red}Please provide a task ID${colors.reset}`);
          return;
        }
        this.completeTask(args[1]);
        break;
      case 'status':
        if (!args[1] || !args[2]) {
          console.error(`${colors.red}Please provide task ID and new status${colors.reset}`);
          return;
        }
        this.updateTaskStatus(args[1], args[2]);
        break;
      case 'help':
      default:
        this.showHelp();
        break;
    }
  }
}

// Run the task master
const taskMaster = new TaskMaster();
taskMaster.run();
