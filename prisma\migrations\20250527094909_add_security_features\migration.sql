-- Create<PERSON><PERSON>
CREATE TYPE "SecurityEventType" AS ENUM ('FAILED_LOGIN', 'M<PERSON><PERSON><PERSON><PERSON>_FAILED_LOGINS', 'SUSPICIOUS_ACTIVITY', 'DAT<PERSON>_BREACH_ATTEMPT', 'UNAUTHORIZED_ACCESS', 'PRI<PERSON><PERSON><PERSON>_ESCALATION', 'UNUSUAL_LOCATION', 'UNUSUAL_TIME', 'MALICIOUS_REQUEST', 'RATE_LIMIT_EXCEEDED', 'ACCOUNT_LOCKOUT', 'PASSWORD_RESET_ABUSE', 'DOCUMENT_ACCESS_VIOLATION', 'API_ABUSE', 'SECURITY_SCAN_DETECTED');

-- C<PERSON><PERSON><PERSON>
CREATE TYPE "SecuritySeverity" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');

-- CreateEnum
CREATE TYPE "ConsentType" AS ENUM ('DATA_PROCESSING', 'MARKETING', 'ANALYTICS', 'COOKIES', 'THIRD_PARTY_SHARING', 'AUTOMATED_DECISION_MAKING');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "DataExportType" AS ENUM ('FULL', 'PERSONAL_DATA_ONLY', 'DOCUMENTS_ONLY', 'APPLICATIONS_ONLY', 'COMMUNICATIONS_ONLY');

-- CreateEnum
CREATE TYPE "ExportStatus" AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'EXPIRED');

-- CreateEnum
CREATE TYPE "DataDeletionType" AS ENUM ('FULL', 'PERSONAL_DATA_ONLY', 'DOCUMENTS_ONLY', 'APPLICATIONS_ONLY', 'COMMUNICATIONS_ONLY');

-- CreateEnum
CREATE TYPE "DeletionStatus" AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'PARTIALLY_COMPLETED');

-- CreateTable
CREATE TABLE "audit_log" (
    "id" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "entityType" TEXT NOT NULL,
    "entityId" TEXT,
    "userId" TEXT,
    "userRole" TEXT,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "location" TEXT,
    "oldValues" JSONB,
    "newValues" JSONB,
    "metadata" JSONB,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "sessionId" TEXT,
    "requestId" TEXT,
    "success" BOOLEAN NOT NULL DEFAULT true,
    "errorMessage" TEXT,
    "riskScore" INTEGER,

    CONSTRAINT "audit_log_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "security_event" (
    "id" TEXT NOT NULL,
    "eventType" "SecurityEventType" NOT NULL,
    "severity" "SecuritySeverity" NOT NULL DEFAULT 'LOW',
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "userId" TEXT,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "location" TEXT,
    "metadata" JSONB,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "resolved" BOOLEAN NOT NULL DEFAULT false,
    "resolvedBy" TEXT,
    "resolvedAt" TIMESTAMP(3),
    "alertSent" BOOLEAN NOT NULL DEFAULT false,
    "alertSentAt" TIMESTAMP(3),

    CONSTRAINT "security_event_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "gdpr_consent" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "consentType" "ConsentType" NOT NULL,
    "granted" BOOLEAN NOT NULL DEFAULT false,
    "grantedAt" TIMESTAMP(3),
    "revokedAt" TIMESTAMP(3),
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "version" TEXT NOT NULL DEFAULT '1.0',
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "gdpr_consent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "data_export_request" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "requestType" "DataExportType" NOT NULL DEFAULT 'FULL',
    "status" "ExportStatus" NOT NULL DEFAULT 'PENDING',
    "requestedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "processedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "downloadUrl" TEXT,
    "expiresAt" TIMESTAMP(3),
    "fileSize" INTEGER,
    "format" TEXT NOT NULL DEFAULT 'JSON',
    "metadata" JSONB,

    CONSTRAINT "data_export_request_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "data_deletion_request" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "requestType" "DataDeletionType" NOT NULL DEFAULT 'FULL',
    "status" "DeletionStatus" NOT NULL DEFAULT 'PENDING',
    "reason" TEXT,
    "requestedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "processedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "deletedData" JSONB,
    "retainedData" JSONB,
    "metadata" JSONB,

    CONSTRAINT "data_deletion_request_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "failed_auth_attempt" (
    "id" TEXT NOT NULL,
    "email" TEXT,
    "ipAddress" TEXT NOT NULL,
    "userAgent" TEXT,
    "location" TEXT,
    "attemptedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "failureReason" TEXT NOT NULL,
    "metadata" JSONB,

    CONSTRAINT "failed_auth_attempt_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "encrypted_field" (
    "id" TEXT NOT NULL,
    "entityType" TEXT NOT NULL,
    "entityId" TEXT NOT NULL,
    "fieldName" TEXT NOT NULL,
    "encryptedValue" TEXT NOT NULL,
    "keyVersion" TEXT NOT NULL DEFAULT '1',
    "algorithm" TEXT NOT NULL DEFAULT 'AES-256-GCM',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "encrypted_field_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "audit_log_userId_idx" ON "audit_log"("userId");

-- CreateIndex
CREATE INDEX "audit_log_entityType_idx" ON "audit_log"("entityType");

-- CreateIndex
CREATE INDEX "audit_log_action_idx" ON "audit_log"("action");

-- CreateIndex
CREATE INDEX "audit_log_timestamp_idx" ON "audit_log"("timestamp");

-- CreateIndex
CREATE INDEX "audit_log_ipAddress_idx" ON "audit_log"("ipAddress");

-- CreateIndex
CREATE INDEX "audit_log_riskScore_idx" ON "audit_log"("riskScore");

-- CreateIndex
CREATE INDEX "security_event_eventType_idx" ON "security_event"("eventType");

-- CreateIndex
CREATE INDEX "security_event_severity_idx" ON "security_event"("severity");

-- CreateIndex
CREATE INDEX "security_event_timestamp_idx" ON "security_event"("timestamp");

-- CreateIndex
CREATE INDEX "security_event_resolved_idx" ON "security_event"("resolved");

-- CreateIndex
CREATE INDEX "security_event_userId_idx" ON "security_event"("userId");

-- CreateIndex
CREATE INDEX "security_event_ipAddress_idx" ON "security_event"("ipAddress");

-- CreateIndex
CREATE INDEX "gdpr_consent_userId_idx" ON "gdpr_consent"("userId");

-- CreateIndex
CREATE INDEX "gdpr_consent_consentType_idx" ON "gdpr_consent"("consentType");

-- CreateIndex
CREATE INDEX "gdpr_consent_granted_idx" ON "gdpr_consent"("granted");

-- CreateIndex
CREATE UNIQUE INDEX "gdpr_consent_userId_consentType_key" ON "gdpr_consent"("userId", "consentType");

-- CreateIndex
CREATE INDEX "data_export_request_userId_idx" ON "data_export_request"("userId");

-- CreateIndex
CREATE INDEX "data_export_request_status_idx" ON "data_export_request"("status");

-- CreateIndex
CREATE INDEX "data_export_request_requestedAt_idx" ON "data_export_request"("requestedAt");

-- CreateIndex
CREATE INDEX "data_deletion_request_userId_idx" ON "data_deletion_request"("userId");

-- CreateIndex
CREATE INDEX "data_deletion_request_status_idx" ON "data_deletion_request"("status");

-- CreateIndex
CREATE INDEX "data_deletion_request_requestedAt_idx" ON "data_deletion_request"("requestedAt");

-- CreateIndex
CREATE INDEX "failed_auth_attempt_email_idx" ON "failed_auth_attempt"("email");

-- CreateIndex
CREATE INDEX "failed_auth_attempt_ipAddress_idx" ON "failed_auth_attempt"("ipAddress");

-- CreateIndex
CREATE INDEX "failed_auth_attempt_attemptedAt_idx" ON "failed_auth_attempt"("attemptedAt");

-- CreateIndex
CREATE INDEX "encrypted_field_entityType_idx" ON "encrypted_field"("entityType");

-- CreateIndex
CREATE INDEX "encrypted_field_entityId_idx" ON "encrypted_field"("entityId");

-- CreateIndex
CREATE INDEX "encrypted_field_keyVersion_idx" ON "encrypted_field"("keyVersion");

-- CreateIndex
CREATE UNIQUE INDEX "encrypted_field_entityType_entityId_fieldName_key" ON "encrypted_field"("entityType", "entityId", "fieldName");

-- AddForeignKey
ALTER TABLE "audit_log" ADD CONSTRAINT "audit_log_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "security_event" ADD CONSTRAINT "security_event_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "gdpr_consent" ADD CONSTRAINT "gdpr_consent_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "data_export_request" ADD CONSTRAINT "data_export_request_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "data_deletion_request" ADD CONSTRAINT "data_deletion_request_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
