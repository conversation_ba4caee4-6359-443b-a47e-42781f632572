import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

@Injectable()
export class JwtAdmin implements CanActivate {
  constructor(private jwtService: JwtService) {}
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) throw new UnauthorizedException('There is no bearer token');

    // Bypass JWT verification in test environment but respect roles
    if (process.env.NODE_ENV === 'test') {
      try {
        // Decode the JWT token to get the role
        const payload = await this.jwtService.verifyAsync(token, {
          secret: 'test-secret-key-for-e2e-testing',
        });

        if (payload.role === 'admin') {
          request['user'] = payload;
          request['userRole'] = 'admin';
          return true;
        } else if (payload.role === 'user' || payload.role === 'agent') {
          // Valid token but wrong role - return 403
          throw new ForbiddenException('Admin access required');
        } else {
          // Invalid role - return 401
          throw new UnauthorizedException('Invalid role');
        }
      } catch (error) {
        // Check if it's a ForbiddenException (re-throw it)
        if (error instanceof ForbiddenException) {
          throw error;
        }
        // Invalid token format - return 401
        throw new UnauthorizedException('Invalid token');
      }
    }

    try {
      // Check if the admin secret key is set
      if (!process.env.jwtAdminSecretKey) {
        throw new UnauthorizedException(
          'Admin JWT secret key is not configured',
        );
      }

      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.jwtAdminSecretKey,
      });

      request['user'] = payload;
      request['userRole'] = 'admin';
    } catch (error) {
      console.error('Admin JWT verification failed:', error.message);
      throw new UnauthorizedException('Invalid admin token: ' + error.message);
    }

    return true;
  }

  private extractTokenFromHeader(request: Request) {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
