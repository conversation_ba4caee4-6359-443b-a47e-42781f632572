// Security and Audit Models for Task 8 Implementation

// 1. Audit Log Model - Comprehensive audit logging system
model audit_log {
  id            String    @id @default(cuid())
  action        String    // CREATE, READ, UPDATE, DELETE, LOGIN, LOGOUT, etc.
  entityType    String    // user, document, application, etc.
  entityId      String?   // ID of the affected entity
  userId        String?   // User who performed the action
  userRole      String?   // Role of the user (admin, agent, user)
  ipAddress     String?   // IP address of the request
  userAgent     String?   // User agent string
  location      String?   // Geographic location (city, country)
  oldValues     Json?     // Previous values (for updates)
  newValues     Json?     // New values (for creates/updates)
  metadata      Json?     // Additional context data
  timestamp     DateTime  @default(now())
  sessionId     String?   // Session identifier
  requestId     String?   // Request correlation ID
  success       Boolean   @default(true)
  errorMessage  String?   // Error message if action failed
  riskScore     Int?      // Security risk score (0-100)
  
  // Relationships
  user          user?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  
  @@index([userId])
  @@index([entityType])
  @@index([action])
  @@index([timestamp])
  @@index([ipAddress])
  @@index([riskScore])
}

// 2. Security Event Model - Security monitoring and alerting
model security_event {
  id            String              @id @default(cuid())
  eventType     SecurityEventType
  severity      SecuritySeverity    @default(LOW)
  title         String
  description   String
  userId        String?
  ipAddress     String?
  userAgent     String?
  location      String?
  metadata      Json?
  timestamp     DateTime            @default(now())
  resolved      Boolean             @default(false)
  resolvedBy    String?
  resolvedAt    DateTime?
  alertSent     Boolean             @default(false)
  alertSentAt   DateTime?
  
  // Relationships
  user          user?               @relation(fields: [userId], references: [id], onDelete: SetNull)
  
  @@index([eventType])
  @@index([severity])
  @@index([timestamp])
  @@index([resolved])
  @@index([userId])
  @@index([ipAddress])
}

// 3. GDPR Consent Model - GDPR compliance features
model gdpr_consent {
  id            String      @id @default(cuid())
  userId        String
  consentType   ConsentType
  granted       Boolean     @default(false)
  grantedAt     DateTime?
  revokedAt     DateTime?
  ipAddress     String?
  userAgent     String?
  version       String      @default("1.0") // Privacy policy version
  metadata      Json?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  
  // Relationships
  user          user        @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([userId, consentType])
  @@index([userId])
  @@index([consentType])
  @@index([granted])
}

// 4. Data Export Request Model - GDPR Right to Access
model data_export_request {
  id            String              @id @default(cuid())
  userId        String
  requestType   DataExportType      @default(FULL)
  status        ExportStatus        @default(PENDING)
  requestedAt   DateTime            @default(now())
  processedAt   DateTime?
  completedAt   DateTime?
  downloadUrl   String?
  expiresAt     DateTime?
  fileSize      Int?                // Size in bytes
  format        String              @default("JSON") // JSON, CSV, PDF
  metadata      Json?
  
  // Relationships
  user          user                @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([status])
  @@index([requestedAt])
}

// 5. Data Deletion Request Model - GDPR Right to be Forgotten
model data_deletion_request {
  id            String              @id @default(cuid())
  userId        String
  requestType   DataDeletionType    @default(FULL)
  status        DeletionStatus      @default(PENDING)
  reason        String?
  requestedAt   DateTime            @default(now())
  processedAt   DateTime?
  completedAt   DateTime?
  deletedData   Json?               // Summary of deleted data
  retainedData  Json?               // Summary of data that must be retained
  metadata      Json?
  
  // Relationships
  user          user                @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([status])
  @@index([requestedAt])
}

// 6. Failed Authentication Attempts - Security monitoring
model failed_auth_attempt {
  id            String    @id @default(cuid())
  email         String?
  ipAddress     String
  userAgent     String?
  location      String?
  attemptedAt   DateTime  @default(now())
  failureReason String    // INVALID_CREDENTIALS, ACCOUNT_LOCKED, etc.
  metadata      Json?
  
  @@index([email])
  @@index([ipAddress])
  @@index([attemptedAt])
}

// 7. Encrypted Field Model - Field-level encryption tracking
model encrypted_field {
  id            String    @id @default(cuid())
  entityType    String    // user, document, application
  entityId      String
  fieldName     String
  encryptedValue String   // The encrypted data
  keyVersion    String    @default("1") // Encryption key version
  algorithm     String    @default("AES-256-GCM")
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  @@unique([entityType, entityId, fieldName])
  @@index([entityType])
  @@index([entityId])
  @@index([keyVersion])
}

// Enums for Security Models
enum SecurityEventType {
  FAILED_LOGIN
  MULTIPLE_FAILED_LOGINS
  SUSPICIOUS_ACTIVITY
  DATA_BREACH_ATTEMPT
  UNAUTHORIZED_ACCESS
  PRIVILEGE_ESCALATION
  UNUSUAL_LOCATION
  UNUSUAL_TIME
  MALICIOUS_REQUEST
  RATE_LIMIT_EXCEEDED
  ACCOUNT_LOCKOUT
  PASSWORD_RESET_ABUSE
  DOCUMENT_ACCESS_VIOLATION
  API_ABUSE
  SECURITY_SCAN_DETECTED
}

enum SecuritySeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum ConsentType {
  DATA_PROCESSING
  MARKETING
  ANALYTICS
  COOKIES
  THIRD_PARTY_SHARING
  AUTOMATED_DECISION_MAKING
}

enum DataExportType {
  FULL
  PERSONAL_DATA_ONLY
  DOCUMENTS_ONLY
  APPLICATIONS_ONLY
  COMMUNICATIONS_ONLY
}

enum ExportStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  EXPIRED
}

enum DataDeletionType {
  FULL
  PERSONAL_DATA_ONLY
  DOCUMENTS_ONLY
  APPLICATIONS_ONLY
  COMMUNICATIONS_ONLY
}

enum DeletionStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  PARTIALLY_COMPLETED
}
