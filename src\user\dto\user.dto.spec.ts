import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { CreateUserDto, LoginDto, UpdateUserDto } from './user.dto';

describe('UserDto', () => {
  describe('CreateUserDto', () => {
    it('should validate a valid CreateUserDto', async () => {
      const dto = plainToInstance(CreateUserDto, {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123!',
      });

      const errors = await validate(dto);
      expect(errors.length).toBe(0);
    });

    it('should fail validation with invalid email', async () => {
      const dto = plainToInstance(CreateUserDto, {
        name: 'Test User',
        email: 'invalid-email',
        password: 'Password123!',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('email');
    });

    it('should fail validation with missing required fields', async () => {
      const dto = plainToInstance(CreateUserDto, {
        name: 'Test User',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
    });
  });

  describe('LoginDto', () => {
    it('should validate a valid LoginDto', async () => {
      const dto = plainToInstance(LoginDto, {
        email: '<EMAIL>',
        password: 'Password123!',
      });

      const errors = await validate(dto);
      expect(errors.length).toBe(0);
    });

    it('should fail validation with invalid email', async () => {
      const dto = plainToInstance(LoginDto, {
        email: 'invalid-email',
        password: 'Password123!',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('email');
    });

    it('should fail validation with missing required fields', async () => {
      const dto = plainToInstance(LoginDto, {
        email: '<EMAIL>',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
    });
  });

  describe('UpdateUserDto', () => {
    it('should validate a valid UpdateUserDto', async () => {
      const dto = plainToInstance(UpdateUserDto, {
        name: 'Updated Name',
      });

      const errors = await validate(dto);
      expect(errors.length).toBe(0);
    });
  });
});
