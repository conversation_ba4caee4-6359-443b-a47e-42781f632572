import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { PrismaService } from '../src/utils/prisma.service';
import { MockPrismaService } from '../src/utils/prisma.service.mock';
import { TestAuthAppModule } from './test-auth-app.module';
import { JwtService } from '@nestjs/jwt';
import { DocCategory } from '@prisma/client';

/**
 * Authentication tests for DocumentRequirement endpoints
 *
 * These tests verify that all /document-requirements/admin endpoints properly enforce
 * admin-only authentication using the JwtAdmin guard.
 */
describe('DocumentRequirementController Authentication (e2e)', () => {
  let app: INestApplication;
  let prismaService: MockPrismaService;
  let jwtService: JwtService;

  // JWT secret for testing (matches the guard expectation)
  const JWT_SECRET = 'test-secret-key-for-e2e-testing';

  // Helper function to generate test tokens
  const generateTestToken = (role: string, userId: string = 'test-id') => {
    const payload = {
      id: userId,
      email: `${role}@test.com`,
      name: `Test ${role}`,
      role: role,
      sub: userId,
      iat: Math.floor(Date.now() / 1000),
    };
    return jwtService.sign(payload, {
      secret: JWT_SECRET,
      expiresIn: '1h',
    });
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [TestAuthAppModule],
    })
      .overrideProvider(PrismaService)
      .useClass(MockPrismaService)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ whitelist: true }));

    prismaService = moduleFixture.get<MockPrismaService>(PrismaService);
    jwtService = moduleFixture.get<JwtService>(JwtService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Authentication Tests - No Token', () => {
    it('POST /document-requirements/admin should return 401 without token', () => {
      return request(app.getHttpServer())
        .post('/document-requirements/admin')
        .send({
          name: 'Test Document',
          description: 'Test description',
          required: true,
          category: DocCategory.EMPLOYEE,
          serviceTypeId: 'test-service-type-id',
        })
        .expect(401);
    });

    it('GET /document-requirements/admin/:id should return 401 without token', () => {
      return request(app.getHttpServer())
        .get('/document-requirements/admin/test-id')
        .expect(401);
    });

    it('PUT /document-requirements/admin/:id should return 401 without token', () => {
      return request(app.getHttpServer())
        .put('/document-requirements/admin/test-id')
        .send({
          name: 'Updated Document',
        })
        .expect(401);
    });

    it('DELETE /document-requirements/admin/:id should return 401 without token', () => {
      return request(app.getHttpServer())
        .delete('/document-requirements/admin/test-id')
        .expect(401);
    });
  });

  describe('Authentication Tests - Invalid Token', () => {
    const invalidToken = 'Bearer invalid-token';

    it('POST /document-requirements/admin should return 401 with invalid token', () => {
      return request(app.getHttpServer())
        .post('/document-requirements/admin')
        .set('Authorization', invalidToken)
        .send({
          name: 'Test Document',
          description: 'Test description',
          required: true,
          category: DocCategory.EMPLOYEE,
          serviceTypeId: 'test-service-type-id',
        })
        .expect(401);
    });

    it('GET /document-requirements/admin/:id should return 401 with invalid token', () => {
      return request(app.getHttpServer())
        .get('/document-requirements/admin/test-id')
        .set('Authorization', invalidToken)
        .expect(401);
    });

    it('PUT /document-requirements/admin/:id should return 401 with invalid token', () => {
      return request(app.getHttpServer())
        .put('/document-requirements/admin/test-id')
        .set('Authorization', invalidToken)
        .send({
          name: 'Updated Document',
        })
        .expect(401);
    });

    it('DELETE /document-requirements/admin/:id should return 401 with invalid token', () => {
      return request(app.getHttpServer())
        .delete('/document-requirements/admin/test-id')
        .set('Authorization', invalidToken)
        .expect(401);
    });
  });

  describe('Authentication Tests - User Token (Non-Admin)', () => {
    let userToken: string;

    beforeAll(() => {
      userToken = `Bearer ${generateTestToken('user', 'user-test-id-1')}`;
    });

    it('POST /document-requirements/admin should return 403 with user token', () => {
      return request(app.getHttpServer())
        .post('/document-requirements/admin')
        .set('Authorization', userToken)
        .send({
          name: 'Test Document',
          description: 'Test description',
          required: true,
          category: DocCategory.EMPLOYEE,
          serviceTypeId: 'test-service-type-id',
        })
        .expect(403);
    });

    it('GET /document-requirements/admin/:id should return 403 with user token', () => {
      return request(app.getHttpServer())
        .get('/document-requirements/admin/test-id')
        .set('Authorization', userToken)
        .expect(403);
    });

    it('PUT /document-requirements/admin/:id should return 403 with user token', () => {
      return request(app.getHttpServer())
        .put('/document-requirements/admin/test-id')
        .set('Authorization', userToken)
        .send({
          name: 'Updated Document',
        })
        .expect(403);
    });

    it('DELETE /document-requirements/admin/:id should return 403 with user token', () => {
      return request(app.getHttpServer())
        .delete('/document-requirements/admin/test-id')
        .set('Authorization', userToken)
        .expect(403);
    });
  });

  describe('Authentication Tests - Valid Admin Token', () => {
    let adminToken: string;

    beforeAll(() => {
      adminToken = `Bearer ${generateTestToken('admin', 'admin-test-id-1')}`;
    });

    beforeEach(() => {
      // Setup mock data for successful operations
      const mockServiceType = {
        id: 'test-service-type-id',
        name: 'Test Service Type',
        description: 'Test description',
        price: 1000,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockDocumentRequirement = {
        id: 'test-id',
        name: 'Test Document',
        description: 'Test description',
        required: true,
        category: DocCategory.EMPLOYEE,
        serviceTypeId: 'test-service-type-id',
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: mockServiceType,
      };

      prismaService.service_type.findUnique.mockResolvedValue(mockServiceType);
      prismaService.document_requirement.findUnique.mockResolvedValue(
        mockDocumentRequirement,
      );
      prismaService.document_requirement.create.mockResolvedValue(
        mockDocumentRequirement,
      );
      prismaService.document_requirement.update.mockResolvedValue(
        mockDocumentRequirement,
      );
      prismaService.document_requirement.delete.mockResolvedValue(
        mockDocumentRequirement,
      );
    });

    it('POST /document-requirements/admin should return 201 with admin token', () => {
      return request(app.getHttpServer())
        .post('/document-requirements/admin')
        .set('Authorization', adminToken)
        .send({
          name: 'Test Document',
          description: 'Test description',
          required: true,
          category: DocCategory.EMPLOYEE,
          serviceTypeId: 'test-service-type-id',
        })
        .expect(201);
    });

    it('GET /document-requirements/admin/:id should return 200 with admin token', () => {
      return request(app.getHttpServer())
        .get('/document-requirements/admin/test-id')
        .set('Authorization', adminToken)
        .expect(200);
    });

    it('PUT /document-requirements/admin/:id should return 200 with admin token', () => {
      return request(app.getHttpServer())
        .put('/document-requirements/admin/test-id')
        .set('Authorization', adminToken)
        .send({
          name: 'Updated Document',
        })
        .expect(200);
    });

    it('DELETE /document-requirements/admin/:id should return 200 with admin token', () => {
      return request(app.getHttpServer())
        .delete('/document-requirements/admin/test-id')
        .set('Authorization', adminToken)
        .expect(200);
    });
  });
});
