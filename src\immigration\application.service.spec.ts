import { Test, TestingModule } from '@nestjs/testing';
import { ApplicationService } from './application.service';
import { PrismaService } from '../utils/prisma.service';
import { MockPrismaService } from '../utils/prisma.service.mock';
import {
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { ApplicationStatus, WorkflowStatus, QueryStatus } from '@prisma/client';

/**
 * Unit tests for ApplicationService
 * 
 * Tests all CRUD operations, workflow management, access control,
 * and business logic validation.
 */
describe('ApplicationService', () => {
  let service: ApplicationService;
  let prisma: MockPrismaService;

  const mockUser = {
    id: 'user-1',
    name: 'Test User',
    email: '<EMAIL>',
  };

  const mockServiceType = {
    id: 'service-type-1',
    name: 'Critical Skills Employment Permit',
    description: 'Test service type',
    price: 1000,
    createdAt: new Date(),
    updatedAt: new Date(),
    documentRequirements: [],
    workflowSteps: [
      {
        id: 'step-1',
        name: 'Document Review',
        description: 'Review submitted documents',
        order: 1,
        estimatedDuration: 5,
        serviceTypeId: 'service-type-1',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'step-2',
        name: 'Application Processing',
        description: 'Process the application',
        order: 2,
        estimatedDuration: 10,
        serviceTypeId: 'service-type-1',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ],
  };

  const mockApplication = {
    id: 'app-1',
    userId: 'user-1',
    serviceTypeId: 'service-type-1',
    status: ApplicationStatus.DRAFT,
    submissionDate: null,
    completionDate: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    serviceType: mockServiceType,
    user: mockUser,
    workflow: null,
    checkpointCalls: [],
    applicationQuery: [],
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApplicationService,
        {
          provide: PrismaService,
          useClass: MockPrismaService,
        },
      ],
    }).compile();

    service = module.get<ApplicationService>(ApplicationService);
    prisma = module.get<MockPrismaService>(PrismaService);

    // Clear test data before each test
    prisma.clearTestData();
  });

  describe('create', () => {
    it('should create a new application', async () => {
      // Setup
      prisma.service_type.findUnique.mockResolvedValue(mockServiceType);
      prisma.application.create.mockResolvedValue(mockApplication);

      // Execute
      const result = await service.create('user-1', {
        serviceTypeId: 'service-type-1',
      });

      // Assert
      expect(result).toEqual(mockApplication);
      expect(prisma.service_type.findUnique).toHaveBeenCalledWith({
        where: { id: 'service-type-1' },
      });
      expect(prisma.application.create).toHaveBeenCalledWith({
        data: {
          userId: 'user-1',
          serviceTypeId: 'service-type-1',
          status: ApplicationStatus.DRAFT,
        },
        include: {
          serviceType: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });
    });

    it('should throw NotFoundException if service type does not exist', async () => {
      // Setup
      prisma.service_type.findUnique.mockResolvedValue(null);

      // Execute & Assert
      await expect(
        service.create('user-1', { serviceTypeId: 'invalid-id' }),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('findAll', () => {
    it('should return paginated applications for admin', async () => {
      // Setup
      const applications = [mockApplication];
      prisma.application.findMany.mockResolvedValue(applications);
      prisma.application.count.mockResolvedValue(1);

      // Execute
      const result = await service.findAll(
        { page: 1, limit: 10 },
        'ADMIN',
        'admin-1',
      );

      // Assert
      expect(result).toEqual({
        data: applications,
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      });
    });

    it('should filter applications by user for non-admin users', async () => {
      // Setup
      const applications = [mockApplication];
      prisma.application.findMany.mockResolvedValue(applications);
      prisma.application.count.mockResolvedValue(1);

      // Execute
      await service.findAll({ page: 1, limit: 10 }, 'USER', 'user-1');

      // Assert
      expect(prisma.application.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            userId: 'user-1',
          }),
        }),
      );
    });

    it('should apply status filter when provided', async () => {
      // Setup
      prisma.application.findMany.mockResolvedValue([]);
      prisma.application.count.mockResolvedValue(0);

      // Execute
      await service.findAll(
        { status: ApplicationStatus.SUBMITTED },
        'ADMIN',
        'admin-1',
      );

      // Assert
      expect(prisma.application.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            status: ApplicationStatus.SUBMITTED,
          }),
        }),
      );
    });
  });

  describe('findOne', () => {
    it('should return application for admin', async () => {
      // Setup
      prisma.application.findUnique.mockResolvedValue(mockApplication);

      // Execute
      const result = await service.findOne('app-1', 'ADMIN', 'admin-1');

      // Assert
      expect(result).toEqual(mockApplication);
    });

    it('should return application for owner', async () => {
      // Setup
      prisma.application.findUnique.mockResolvedValue(mockApplication);

      // Execute
      const result = await service.findOne('app-1', 'USER', 'user-1');

      // Assert
      expect(result).toEqual(mockApplication);
    });

    it('should throw ForbiddenException for non-owner user', async () => {
      // Setup
      prisma.application.findUnique.mockResolvedValue(mockApplication);

      // Execute & Assert
      await expect(
        service.findOne('app-1', 'USER', 'other-user'),
      ).rejects.toThrow(ForbiddenException);
    });

    it('should throw NotFoundException if application does not exist', async () => {
      // Setup
      prisma.application.findUnique.mockResolvedValue(null);

      // Execute & Assert
      await expect(
        service.findOne('invalid-id', 'ADMIN', 'admin-1'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update application and create workflow when submitting', async () => {
      // Setup
      const draftApplication = { ...mockApplication, status: ApplicationStatus.DRAFT };
      const submittedApplication = { ...mockApplication, status: ApplicationStatus.SUBMITTED };
      
      prisma.application.findUnique.mockResolvedValue(draftApplication);
      prisma.application.update.mockResolvedValue(submittedApplication);
      prisma.application_workflow.create.mockResolvedValue({
        id: 'workflow-1',
        applicationId: 'app-1',
        currentStep: 1,
        status: WorkflowStatus.IN_PROGRESS,
        startDate: new Date(),
        lastUpdated: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Execute
      const result = await service.update(
        'app-1',
        { status: ApplicationStatus.SUBMITTED },
        'USER',
        'user-1',
      );

      // Assert
      expect(result).toEqual(submittedApplication);
      expect(prisma.application_workflow.create).toHaveBeenCalled();
    });

    it('should update application without creating workflow for other status changes', async () => {
      // Setup
      prisma.application.findUnique.mockResolvedValue(mockApplication);
      prisma.application.update.mockResolvedValue(mockApplication);

      // Execute
      await service.update('app-1', { notes: 'Updated notes' }, 'USER', 'user-1');

      // Assert
      expect(prisma.application_workflow.create).not.toHaveBeenCalled();
    });
  });

  describe('remove', () => {
    it('should delete application in DRAFT status', async () => {
      // Setup
      const draftApplication = { ...mockApplication, status: ApplicationStatus.DRAFT };
      prisma.application.findUnique.mockResolvedValue(draftApplication);
      prisma.application.delete.mockResolvedValue(draftApplication);

      // Execute
      const result = await service.remove('app-1', 'USER', 'user-1');

      // Assert
      expect(result).toEqual(draftApplication);
      expect(prisma.application.delete).toHaveBeenCalledWith({
        where: { id: 'app-1' },
      });
    });

    it('should throw BadRequestException for non-draft applications', async () => {
      // Setup
      const submittedApplication = { ...mockApplication, status: ApplicationStatus.SUBMITTED };
      prisma.application.findUnique.mockResolvedValue(submittedApplication);

      // Execute & Assert
      await expect(
        service.remove('app-1', 'USER', 'user-1'),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('getProgress', () => {
    it('should return progress for application with workflow', async () => {
      // Setup
      const applicationWithWorkflow = {
        ...mockApplication,
        workflow: {
          id: 'workflow-1',
          currentStep: 1,
          status: WorkflowStatus.IN_PROGRESS,
          startDate: new Date(),
          lastUpdated: new Date(),
          applicationId: 'app-1',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      };
      prisma.application.findUnique.mockResolvedValue(applicationWithWorkflow);

      // Execute
      const result = await service.getProgress('app-1', 'USER', 'user-1');

      // Assert
      expect(result.applicationId).toBe('app-1');
      expect(result.currentStep).toBe(1);
      expect(result.totalSteps).toBe(2);
      expect(result.progressPercentage).toBe(50);
      expect(result.currentStepName).toBe('Document Review');
      expect(result.status).toBe(WorkflowStatus.IN_PROGRESS);
    });

    it('should return not started progress for application without workflow', async () => {
      // Setup
      prisma.application.findUnique.mockResolvedValue(mockApplication);

      // Execute
      const result = await service.getProgress('app-1', 'USER', 'user-1');

      // Assert
      expect(result.currentStep).toBe(0);
      expect(result.progressPercentage).toBe(0);
      expect(result.currentStepName).toBe('Not Started');
      expect(result.status).toBe(WorkflowStatus.NOT_STARTED);
    });
  });

  describe('createWorkflowStep', () => {
    it('should create workflow step for valid service type', async () => {
      // Setup
      const workflowStep = {
        id: 'step-1',
        name: 'Test Step',
        description: 'Test description',
        order: 1,
        estimatedDuration: 5,
        serviceTypeId: 'service-type-1',
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: mockServiceType,
      };

      prisma.service_type.findUnique.mockResolvedValue(mockServiceType);
      prisma.workflow_step.create.mockResolvedValue(workflowStep);

      // Execute
      const result = await service.createWorkflowStep({
        name: 'Test Step',
        description: 'Test description',
        order: 1,
        estimatedDuration: 5,
        serviceTypeId: 'service-type-1',
      });

      // Assert
      expect(result).toEqual(workflowStep);
    });

    it('should throw NotFoundException for invalid service type', async () => {
      // Setup
      prisma.service_type.findUnique.mockResolvedValue(null);

      // Execute & Assert
      await expect(
        service.createWorkflowStep({
          name: 'Test Step',
          description: 'Test description',
          order: 1,
          estimatedDuration: 5,
          serviceTypeId: 'invalid-id',
        }),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('respondToQuery', () => {
    it('should respond to query and update status', async () => {
      // Setup
      const query = {
        id: 'query-1',
        queryText: 'Test question',
        responseText: null,
        queryDate: new Date(),
        responseDate: null,
        status: QueryStatus.PENDING,
        applicationId: 'app-1',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const respondedQuery = {
        ...query,
        responseText: 'Test response',
        responseDate: new Date(),
        status: QueryStatus.RESPONDED,
        application: mockApplication,
      };

      prisma.application_query.findUnique.mockResolvedValue(query);
      prisma.application_query.update.mockResolvedValue(respondedQuery);

      // Execute
      const result = await service.respondToQuery('query-1', {
        responseText: 'Test response',
      });

      // Assert
      expect(result.responseText).toBe('Test response');
      expect(result.status).toBe(QueryStatus.RESPONDED);
      expect(prisma.application_query.update).toHaveBeenCalledWith({
        where: { id: 'query-1' },
        data: {
          responseText: 'Test response',
          responseDate: expect.any(Date),
          status: QueryStatus.RESPONDED,
        },
        include: expect.any(Object),
      });
    });

    it('should throw NotFoundException for invalid query', async () => {
      // Setup
      prisma.application_query.findUnique.mockResolvedValue(null);

      // Execute & Assert
      await expect(
        service.respondToQuery('invalid-id', { responseText: 'Test response' }),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('getStatistics', () => {
    it('should return application statistics', async () => {
      // Setup
      prisma.application.count
        .mockResolvedValueOnce(10) // total
        .mockResolvedValueOnce(2)  // draft
        .mockResolvedValueOnce(3)  // submitted
        .mockResolvedValueOnce(3)  // in progress
        .mockResolvedValueOnce(1)  // completed
        .mockResolvedValueOnce(1); // rejected

      // Execute
      const result = await service.getStatistics();

      // Assert
      expect(result).toEqual({
        total: 10,
        byStatus: {
          draft: 2,
          submitted: 3,
          inProgress: 3,
          completed: 1,
          rejected: 1,
        },
      });
    });
  });
});
