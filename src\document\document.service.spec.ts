import { Test, TestingModule } from '@nestjs/testing';
import { DocumentService } from './document.service';
import { PrismaService } from '../utils/prisma.service';
import { MediaService } from '../media/media.service';
import { VerificationStatus } from '@prisma/client';
import {
  BadRequestException,
  ForbiddenException,
  NotFoundException,
} from '@nestjs/common';
import { UploadDocumentDto, VerifyDocumentDto } from './dto/document.dto';

describe('DocumentService', () => {
  let service: DocumentService;
  let prismaService: PrismaService;
  let mediaService: MediaService;

  const mockPrismaService = {
    user_document: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
    },
  };

  const mockMediaService = {
    uploadFile: jest.fn(),
    deleteFile: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: MediaService,
          useValue: mockMediaService,
        },
      ],
    }).compile();

    service = module.get<DocumentService>(DocumentService);
    prismaService = module.get<PrismaService>(PrismaService);
    mediaService = module.get<MediaService>(MediaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('uploadDocument', () => {
    it('should upload a document successfully', async () => {
      // Arrange
      const userId = 'user-1';
      const file = {
        mimetype: 'application/pdf',
        size: 1024 * 1024, // 1MB
        originalname: 'test.pdf',
        buffer: Buffer.from('test'),
      } as Express.Multer.File;
      const dto: UploadDocumentDto = {
        documentName: 'Test Document',
        expiryDate: '2025-12-31',
      };
      const uploadResult = { url: 'https://example.com/test.pdf' };
      const createdDocument = {
        id: 'doc-1',
        documentName: 'Test Document',
        fileUrl: 'https://example.com/test.pdf',
        verificationStatus: VerificationStatus.PENDING,
        expiryDate: new Date('2025-12-31'),
        uploadDate: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
        category: null,
        notes: null,
        serviceTypeId: null,
        applicationId: null,
        documentRequirementId: null,
        rejectionReason: null,
        userId: 'user-1',
        user: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
        },
        serviceType: null,
        application: null,
        documentRequirement: null,
      };

      mockMediaService.uploadFile.mockResolvedValue(uploadResult);
      mockPrismaService.user.findUnique.mockResolvedValue({ id: userId });
      mockPrismaService.user_document.create.mockResolvedValue(createdDocument);

      // Act
      const result = await service.uploadDocument(userId, file, dto);

      // Assert
      expect(mockMediaService.uploadFile).toHaveBeenCalledWith(
        file,
        expect.any(String), // Don't hardcode the path
      );
      expect(mockPrismaService.user_document.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          documentName: dto.documentName,
          fileUrl: uploadResult.url,
          verificationStatus: VerificationStatus.PENDING,
          expiryDate: expect.any(Date),
          userId,
          category: undefined,
          notes: undefined,
          serviceTypeId: undefined,
          applicationId: undefined,
          documentRequirementId: undefined,
        }),
        include: expect.objectContaining({
          user: expect.any(Object),
          serviceType: expect.any(Object),
          application: expect.any(Object),
          documentRequirement: expect.any(Object),
        }),
      });
      // Use objectContaining to avoid issues with date comparison
      expect(result).toEqual(
        expect.objectContaining({
          id: createdDocument.id,
          documentName: createdDocument.documentName,
          fileUrl: createdDocument.fileUrl,
          verificationStatus: createdDocument.verificationStatus,
        }),
      );
    });

    it('should throw BadRequestException for invalid file type', async () => {
      // Arrange
      const userId = 'user-1';
      const file = {
        mimetype: 'text/plain',
        size: 1024 * 1024,
        originalname: 'test.txt',
        buffer: Buffer.from('test'),
      } as Express.Multer.File;
      const dto: UploadDocumentDto = {
        documentName: 'Test Document',
      };

      mockPrismaService.user.findUnique.mockResolvedValue({ id: userId });

      // Act & Assert
      await expect(service.uploadDocument(userId, file, dto)).rejects.toThrow(
        BadRequestException,
      );
      expect(mockMediaService.uploadFile).not.toHaveBeenCalled();
      expect(mockPrismaService.user_document.create).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException for file size exceeding limit', async () => {
      // Arrange
      const userId = 'user-1';
      const file = {
        mimetype: 'application/pdf',
        size: 20 * 1024 * 1024, // 20MB (exceeds 10MB limit)
        originalname: 'test.pdf',
        buffer: Buffer.from('test'),
      } as Express.Multer.File;
      const dto: UploadDocumentDto = {
        documentName: 'Test Document',
      };

      mockPrismaService.user.findUnique.mockResolvedValue({ id: userId });

      // Act & Assert
      await expect(service.uploadDocument(userId, file, dto)).rejects.toThrow(
        BadRequestException,
      );
      expect(mockMediaService.uploadFile).not.toHaveBeenCalled();
      expect(mockPrismaService.user_document.create).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException when user does not exist', async () => {
      // Arrange
      const userId = 'non-existent-user';
      const file = {
        mimetype: 'application/pdf',
        size: 1024 * 1024,
        originalname: 'test.pdf',
        buffer: Buffer.from('test'),
      } as Express.Multer.File;
      const dto: UploadDocumentDto = {
        documentName: 'Test Document',
      };

      mockPrismaService.user.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(service.uploadDocument(userId, file, dto)).rejects.toThrow(
        NotFoundException,
      );
      expect(mockMediaService.uploadFile).not.toHaveBeenCalled();
      expect(mockPrismaService.user_document.create).not.toHaveBeenCalled();
    });
  });

  describe('getUserDocuments', () => {
    it('should return user documents', async () => {
      // Arrange
      const userId = 'user-1';
      const documents = [
        {
          id: 'doc-1',
          documentName: 'Passport',
          fileUrl: 'https://example.com/passport.pdf',
          verificationStatus: VerificationStatus.PENDING,
          userId,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'doc-2',
          documentName: 'Visa',
          fileUrl: 'https://example.com/visa.pdf',
          verificationStatus: VerificationStatus.APPROVED,
          userId,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockPrismaService.user_document.findMany.mockResolvedValue(documents);

      // Act
      const result = await service.getUserDocuments(userId);

      // Assert
      expect(mockPrismaService.user_document.findMany).toHaveBeenCalled();
      expect(result).toEqual(documents);
    });

    it('should filter documents by status', async () => {
      // Arrange
      const userId = 'user-1';
      const filters = { verificationStatus: VerificationStatus.APPROVED };
      const documents = [
        {
          id: 'doc-2',
          documentName: 'Visa',
          fileUrl: 'https://example.com/visa.pdf',
          verificationStatus: VerificationStatus.APPROVED,
          userId,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockPrismaService.user_document.findMany.mockResolvedValue(documents);

      // Act
      const result = await service.getUserDocuments(userId, filters);

      // Assert
      expect(mockPrismaService.user_document.findMany).toHaveBeenCalled();
      expect(result).toEqual(documents);
    });
  });

  describe('getDocumentById', () => {
    it('should return a document by ID', async () => {
      // Arrange
      const userId = 'user-1';
      const documentId = 'doc-1';
      const document = {
        id: documentId,
        documentName: 'Passport',
        fileUrl: 'https://example.com/passport.pdf',
        verificationStatus: VerificationStatus.PENDING,
        userId: 'user-1',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.user_document.findUnique.mockResolvedValue(document);

      // Act
      const result = await service.getDocumentById(userId, documentId);

      // Assert
      expect(mockPrismaService.user_document.findUnique).toHaveBeenCalledWith({
        where: { id: documentId },
      });
      expect(result).toEqual(document);
    });

    it('should throw NotFoundException for non-existent document', async () => {
      // Arrange
      const userId = 'user-1';
      const documentId = 'non-existent-doc';
      mockPrismaService.user_document.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(service.getDocumentById(userId, documentId)).rejects.toThrow(
        NotFoundException,
      );
    });

    it("should throw ForbiddenException when user tries to access another user's document", async () => {
      // Arrange
      const userId = 'different-user';
      const documentId = 'doc-1';
      const document = {
        id: documentId,
        documentName: 'Passport',
        fileUrl: 'https://example.com/passport.pdf',
        verificationStatus: VerificationStatus.PENDING,
        userId: 'user-1', // Document belongs to user-1, not different-user
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.user_document.findUnique.mockResolvedValue(document);

      // Act & Assert
      await expect(service.getDocumentById(userId, documentId)).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should allow admin to access any document', async () => {
      // Arrange
      const userId = 'admin-1';
      const documentId = 'doc-1';
      const document = {
        id: documentId,
        documentName: 'Passport',
        fileUrl: 'https://example.com/passport.pdf',
        verificationStatus: VerificationStatus.PENDING,
        userId: 'user-1', // Document belongs to user-1, not admin-1
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.user_document.findUnique.mockResolvedValue(document);

      // Act
      const result = await service.getDocumentById(userId, documentId, true);

      // Assert
      expect(result).toEqual(document);
    });
  });
});
