import {
  Body,
  Button,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';

import * as React from 'react';

interface ReminderEmailProps {
  userName?: string;
  message: string;
  dueDate: string;
  isOverdue?: boolean;
  applicationName?: string;
  documentName?: string;
  actionUrl?: string;
  actionText?: string;
}

export default function ReminderEmail({
  userName = '',
  message = 'You have a reminder',
  dueDate,
  isOverdue = false,
  applicationName,
  documentName,
  actionUrl,
  actionText = 'Take Action',
}: ReminderEmailProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const urgencyColor = isOverdue ? '#ef4444' : '#f59e0b';
  const urgencyIcon = isOverdue ? '🚨' : '⏰';
  const urgencyText = isOverdue ? 'OVERDUE' : 'REMINDER';

  return (
    <Html>
      <Head />
      <Preview>{isOverdue ? 'Overdue: ' : 'Reminder: '}{message}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Container style={logoSection}>
            <Link href={process.env.WEBSITE} style={logoLink}>
              <Img
                src="https://supabase.careerireland.com/storage/v1/object/public/careerireland-prod/logo/logo-careerireland.webp"
                width="80"
                height="80"
                alt="Logo"
                style={logoImage}
              />
            </Link>
          </Container>

          <Section style={{
            ...urgencyIndicator,
            backgroundColor: urgencyColor + '20',
            borderLeft: `4px solid ${urgencyColor}`,
          }}>
            <Text style={{
              ...urgencyTypeText,
              color: urgencyColor,
            }}>
              {urgencyIcon} {urgencyText}
            </Text>
          </Section>

          <Heading style={h1}>
            {isOverdue ? 'Overdue Reminder' : 'Upcoming Reminder'}
          </Heading>

          <Text style={text}>{userName ? `Hi ${userName},` : 'Hello,'}</Text>

          <Section style={reminderContainer}>
            <Text style={reminderMessage}>{message}</Text>
            
            <Section style={detailsSection}>
              <Text style={detailLabel}>Due Date:</Text>
              <Text style={{
                ...detailValue,
                color: isOverdue ? '#ef4444' : '#1f2937',
                fontWeight: isOverdue ? '600' : '500',
              }}>
                {formatDate(dueDate)}
                {isOverdue && ' (Overdue)'}
              </Text>

              {applicationName && (
                <>
                  <Text style={detailLabel}>Related Application:</Text>
                  <Text style={detailValue}>{applicationName}</Text>
                </>
              )}

              {documentName && (
                <>
                  <Text style={detailLabel}>Related Document:</Text>
                  <Text style={detailValue}>{documentName}</Text>
                </>
              )}
            </Section>
          </Section>

          {actionUrl && (
            <Section style={buttonContainer}>
              <Button 
                style={{
                  ...button,
                  backgroundColor: urgencyColor,
                }} 
                href={actionUrl}
              >
                {actionText}
              </Button>
            </Section>
          )}

          <Text style={text}>
            {isOverdue 
              ? 'Please take action as soon as possible to avoid any delays in your application process.'
              : 'We recommend completing this task before the due date to stay on track.'
            }
          </Text>

          <Text style={text}>
            You can manage your reminders and notification preferences in your account dashboard.
          </Text>

          <Text style={text}>
            Thanks,
            <br />
            The Career Ireland Team
          </Text>
        </Container>
      </Body>
    </Html>
  );
}

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  padding: '0 48px',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '24px auto',
  padding: '48px 24px',
  borderRadius: '8px',
  maxWidth: '500px',
};

const logoLink = {
  display: 'inline-block',
  textDecoration: 'none',
};

const logoSection = {
  textAlign: 'center' as const,
  marginBottom: '32px',
  display: 'flex',
  flexDirection: 'column' as const,
  alignItems: 'center',
  maxWidth: '600px',
};

const logoImage = {
  margin: '0 auto',
  borderRadius: '12px',
  padding: '12px',
  backgroundColor: '#f8fafc',
  objectFit: 'contain' as const,
  transition: 'all 0.2s ease',
};

const h1 = {
  color: '#1f2937',
  fontSize: '24px',
  fontWeight: '600',
  lineHeight: '1.4',
  margin: '0 0 24px',
  textAlign: 'center' as const,
};

const text = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '24px 0',
};

const urgencyIndicator = {
  padding: '16px 20px',
  borderRadius: '8px',
  margin: '24px 0',
};

const urgencyTypeText = {
  fontSize: '14px',
  fontWeight: '600',
  margin: '0',
  textTransform: 'uppercase' as const,
  letterSpacing: '0.05em',
};

const reminderContainer = {
  background: '#f8fafc',
  borderRadius: '8px',
  padding: '24px',
  margin: '24px 0',
  border: '1px solid #e5e7eb',
};

const reminderMessage = {
  color: '#1f2937',
  fontSize: '18px',
  fontWeight: '600',
  lineHeight: '24px',
  margin: '0 0 20px 0',
};

const detailsSection = {
  borderTop: '1px solid #e5e7eb',
  paddingTop: '16px',
  marginTop: '16px',
};

const detailLabel = {
  color: '#6b7280',
  fontSize: '14px',
  fontWeight: '500',
  margin: '8px 0 4px 0',
  textTransform: 'uppercase' as const,
  letterSpacing: '0.05em',
};

const detailValue = {
  color: '#1f2937',
  fontSize: '16px',
  fontWeight: '500',
  margin: '0 0 12px 0',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  borderRadius: '6px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
  margin: '0 auto',
};
