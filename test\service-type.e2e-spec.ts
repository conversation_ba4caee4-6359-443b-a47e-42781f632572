import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { PrismaService } from '../src/utils/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { MockPrismaService } from '../src/utils/prisma.service.mock';
import { TestAppModule } from './test-app.module';

describe('ServiceTypeController (e2e)', () => {
  let app: INestApplication;
  let prismaService: MockPrismaService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [TestAppModule],
    })
      .overrideProvider(PrismaService)
      .useClass(MockPrismaService)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ whitelist: true }));

    prismaService = moduleFixture.get<MockPrismaService>(PrismaService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('GET /admin/service-types', () => {
    it('should return all service types', () => {
      // Mock data
      const serviceTypes = [
        {
          id: 'service-type-1',
          name: 'Critical Skills Employment Permit',
          description: 'Permit for highly skilled professionals',
          price: 1000,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'service-type-2',
          name: 'General Employment Permit',
          description: 'Permit for general employment',
          price: 800,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      // Setup mock
      prismaService.service_type.findMany.mockResolvedValue(serviceTypes);
      prismaService.service_type.count.mockResolvedValue(serviceTypes.length);

      return request(app.getHttpServer())
        .get('/admin/service-types')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveLength(2);
          expect(res.body.total).toBe(2);
          expect(res.body.page).toBe(1);
          expect(res.body.limit).toBe(10);
          expect(res.body.totalPages).toBe(1);
        });
    });

    it('should filter service types by search term', () => {
      // Mock data
      const serviceTypes = [
        {
          id: 'service-type-1',
          name: 'Critical Skills Employment Permit',
          description: 'Permit for highly skilled professionals',
          price: 1000,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      // Setup mock
      prismaService.service_type.findMany.mockResolvedValue(serviceTypes);
      prismaService.service_type.count.mockResolvedValue(serviceTypes.length);

      return request(app.getHttpServer())
        .get('/admin/service-types?search=critical')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveLength(1);
          expect(res.body.total).toBe(1);
        });
    });
  });

  describe('GET /admin/service-types/:id', () => {
    it('should return a service type by id', () => {
      // Mock data
      const serviceType = {
        id: 'service-type-1',
        name: 'Critical Skills Employment Permit',
        description: 'Permit for highly skilled professionals',
        price: 1000,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Setup mock
      prismaService.service_type.findUnique.mockResolvedValue(serviceType);

      return request(app.getHttpServer())
        .get('/admin/service-types/service-type-1')
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe('service-type-1');
          expect(res.body.name).toBe('Critical Skills Employment Permit');
        });
    });

    it('should return 404 if service type not found', () => {
      // Setup mock
      prismaService.service_type.findUnique.mockResolvedValue(null);

      return request(app.getHttpServer())
        .get('/admin/service-types/non-existent-id')
        .expect(404);
    });
  });

  describe('POST /admin/service-types', () => {
    it('should create a new service type', () => {
      // Mock data
      const createDto = {
        name: 'New Service Type',
        description: 'New service type description',
        price: 1200,
      };

      const createdServiceType = {
        id: 'new-service-type',
        ...createDto,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Setup mock
      prismaService.service_type.create.mockResolvedValue(createdServiceType);

      return request(app.getHttpServer())
        .post('/admin/service-types')
        .send(createDto)
        .expect(201)
        .expect((res) => {
          expect(res.body.id).toBe('new-service-type');
          expect(res.body.name).toBe('New Service Type');
        });
    });

    it('should return 400 if validation fails', () => {
      return request(app.getHttpServer())
        .post('/admin/service-types')
        .send({
          name: 'New Service Type',
          description: 'New service type description',
          price: -100, // Invalid price
        })
        .expect(400);
    });
  });

  describe('PUT /admin/service-types/:id', () => {
    it('should update a service type', () => {
      // Mock data
      const updateDto = {
        name: 'Updated Service Type',
      };

      const serviceType = {
        id: 'service-type-1',
        name: 'Critical Skills Employment Permit',
        description: 'Permit for highly skilled professionals',
        price: 1000,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const updatedServiceType = {
        ...serviceType,
        name: 'Updated Service Type',
      };

      // Setup mock
      prismaService.service_type.findUnique.mockResolvedValue(serviceType);
      prismaService.service_type.update.mockResolvedValue(updatedServiceType);

      return request(app.getHttpServer())
        .put('/admin/service-types/service-type-1')
        .send(updateDto)
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe('service-type-1');
          expect(res.body.name).toBe('Updated Service Type');
        });
    });
  });

  describe('DELETE /admin/service-types/:id', () => {
    it('should delete a service type', () => {
      // Mock data
      const serviceType = {
        id: 'service-type-1',
        name: 'Critical Skills Employment Permit',
        description: 'Permit for highly skilled professionals',
        price: 1000,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Setup mock
      prismaService.service_type.findUnique.mockResolvedValue(serviceType);
      prismaService.application.count.mockResolvedValue(0);
      prismaService.service_type.delete.mockResolvedValue(serviceType);

      return request(app.getHttpServer())
        .delete('/admin/service-types/service-type-1')
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe('service-type-1');
        });
    });

    it('should return 400 if service type has applications', () => {
      // Mock data
      const serviceType = {
        id: 'service-type-1',
        name: 'Critical Skills Employment Permit',
        description: 'Permit for highly skilled professionals',
        price: 1000,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Setup mock
      prismaService.service_type.findUnique.mockResolvedValue(serviceType);
      prismaService.application.count.mockResolvedValue(2);

      return request(app.getHttpServer())
        .delete('/admin/service-types/service-type-1')
        .expect(400);
    });
  });

  describe('GET /admin/service-types/:id/document-requirements', () => {
    it('should return document requirements for a service type', () => {
      // Mock data
      const serviceType = {
        id: 'service-type-1',
        name: 'Critical Skills Employment Permit',
        description: 'Permit for highly skilled professionals',
        price: 1000,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const documentRequirements = [
        {
          id: 'doc-req-1',
          name: 'Passport',
          description: 'Valid passport',
          required: true,
          category: 'EMPLOYEE',
          serviceTypeId: 'service-type-1',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'doc-req-2',
          name: 'Resume',
          description: 'Updated resume',
          required: true,
          category: 'EMPLOYEE',
          serviceTypeId: 'service-type-1',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      // Setup mock
      prismaService.service_type.findUnique.mockResolvedValue(serviceType);
      prismaService.document_requirement.findMany.mockResolvedValue(
        documentRequirements,
      );

      return request(app.getHttpServer())
        .get('/admin/service-types/service-type-1/document-requirements')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveLength(2);
          expect(res.body[0].name).toBe('Passport');
          expect(res.body[1].name).toBe('Resume');
        });
    });

    it('should return 404 if service type not found', () => {
      // Setup mock
      prismaService.service_type.findUnique.mockResolvedValue(null);

      return request(app.getHttpServer())
        .get('/admin/service-types/non-existent-id/document-requirements')
        .expect(404);
    });
  });
});
