import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { PrismaService } from '../../src/utils/prisma.service';
import { MockPrismaService } from '../../src/utils/prisma.service.mock';
import { MinimalTestAppModule } from '../../test/minimal-test-app.module';
import { getTestCredentials, AuthTestUtils } from '../utils/auth-helpers';
import {
  PACKAGE_FIXTURES,
  PACKAGE_CREATE_DTOS,
  INVALID_PACKAGE_DATA,
  mockPrismaPackage,
  generatePackageList,
} from '../utils/fixtures/packages';
import { testResultCollector, testResultReporter } from '../utils/test-results';

/**
 * E2E Test Suite: Admin Creating Packages
 *
 * Tests the complete workflow of package creation by admin users,
 * including validation, field requirements, and error handling.
 *
 * Scenarios covered:
 * 1. Create valid packages with different configurations
 * 2. Validate field requirements and constraints
 * 3. Test authorization and authentication
 * 4. Handle duplicate package names
 * 5. Test package ordering and service arrays
 */
describe('Admin Package Creation (E2E)', () => {
  let app: INestApplication;
  let prismaService: MockPrismaService;

  const adminCredentials = getTestCredentials('admin');
  const userCredentials = getTestCredentials('user');
  const agentCredentials = getTestCredentials('agent');

  beforeAll(async () => {
    testResultCollector.clear();

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [MinimalTestAppModule],
    })
      .overrideProvider(PrismaService)
      .useClass(MockPrismaService)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      }),
    );

    prismaService = moduleFixture.get<MockPrismaService>(PrismaService);
    await app.init();
  });

  afterAll(async () => {
    const suiteResults = testResultCollector.getSuiteResults(
      'Admin Package Creation',
    );
    const summary = testResultReporter.generateSummary([suiteResults]);
    testResultReporter.saveResults(summary);
    testResultReporter.saveLogs(summary);
    testResultReporter.generateHtmlReport(summary);

    await app.close();
  });

  beforeEach(() => {
    prismaService.clearTestData();
    jest.clearAllMocks();
  });

  describe('POST /packages - Create Package', () => {
    it('should create a valid package successfully', async () => {
      testResultCollector.startTest(
        'Admin Package Creation',
        'Create valid package',
      );

      const packageData = PACKAGE_CREATE_DTOS.valid;
      const mockCreatedPackage = mockPrismaPackage({
        id: 'pkg-new-1',
        ...packageData,
      });

      prismaService.packages.create.mockResolvedValue(mockCreatedPackage);

      const startTime = Date.now();
      const response = await request(app.getHttpServer())
        .post('/packages')
        .set('Authorization', adminCredentials.bearerToken)
        .send(packageData);

      const duration = Date.now() - startTime;

      testResultCollector.logHttpRequest({
        method: 'POST',
        url: '/packages',
        statusCode: response.status,
        requestBody: packageData,
        responseBody: response.body,
        duration,
        timestamp: new Date(),
      });

      if (response.status === 201) {
        expect(response.body.name).toBe(packageData.name);
        expect(response.body.amount).toBe(packageData.amount);
        expect(response.body.service).toEqual(packageData.service);
        expect(prismaService.packages.create).toHaveBeenCalledWith({
          data: packageData,
        });
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 201, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should create a premium package with multiple services', async () => {
      testResultCollector.startTest(
        'Admin Package Creation',
        'Create premium package',
      );

      const packageData = PACKAGE_CREATE_DTOS.premium;
      const mockCreatedPackage = mockPrismaPackage({
        id: 'pkg-premium-1',
        ...packageData,
      });

      prismaService.packages.create.mockResolvedValue(mockCreatedPackage);

      const response = await request(app.getHttpServer())
        .post('/packages')
        .set('Authorization', adminCredentials.bearerToken)
        .send(packageData);

      if (response.status === 201) {
        expect(response.body.service).toHaveLength(6);
        expect(response.body.amount).toBe(3000);
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 201, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should create a minimal package', async () => {
      testResultCollector.startTest(
        'Admin Package Creation',
        'Create minimal package',
      );

      const packageData = PACKAGE_CREATE_DTOS.minimal;
      const mockCreatedPackage = mockPrismaPackage({
        id: 'pkg-minimal-1',
        ...packageData,
      });

      prismaService.packages.create.mockResolvedValue(mockCreatedPackage);

      const response = await request(app.getHttpServer())
        .post('/packages')
        .set('Authorization', adminCredentials.bearerToken)
        .send(packageData);

      if (response.status === 201) {
        expect(response.body.service).toHaveLength(1);
        expect(response.body.amount).toBe(500);
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 201, got ${response.status}`,
          response.body,
        );
      }
    });
  });

  describe('Package Validation', () => {
    it('should reject package without name', async () => {
      testResultCollector.startTest(
        'Admin Package Creation',
        'Reject package without name',
      );

      const response = await request(app.getHttpServer())
        .post('/packages')
        .set('Authorization', adminCredentials.bearerToken)
        .send(INVALID_PACKAGE_DATA.missingName);

      if (response.status === 400) {
        // Check if message is array or string and contains 'name'
        const message = Array.isArray(response.body.message)
          ? response.body.message.join(' ')
          : response.body.message;
        expect(message).toContain('name');
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 400, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should reject package without amount', async () => {
      testResultCollector.startTest(
        'Admin Package Creation',
        'Reject package without amount',
      );

      const response = await request(app.getHttpServer())
        .post('/packages')
        .set('Authorization', adminCredentials.bearerToken)
        .send(INVALID_PACKAGE_DATA.missingAmount);

      if (response.status === 400) {
        // Check if message is array or string and contains 'amount'
        const message = Array.isArray(response.body.message)
          ? response.body.message.join(' ')
          : response.body.message;
        expect(message).toContain('amount');
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 400, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should reject package with negative amount', async () => {
      testResultCollector.startTest(
        'Admin Package Creation',
        'Reject negative amount',
      );

      const response = await request(app.getHttpServer())
        .post('/packages')
        .set('Authorization', adminCredentials.bearerToken)
        .send(INVALID_PACKAGE_DATA.negativeAmount);

      if (response.status === 400) {
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 400, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should reject package with empty services array', async () => {
      testResultCollector.startTest(
        'Admin Package Creation',
        'Reject empty services',
      );

      const response = await request(app.getHttpServer())
        .post('/packages')
        .set('Authorization', adminCredentials.bearerToken)
        .send(INVALID_PACKAGE_DATA.emptyServices);

      if (response.status === 400) {
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 400, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should reject package with invalid service type', async () => {
      testResultCollector.startTest(
        'Admin Package Creation',
        'Reject invalid service type',
      );

      const response = await request(app.getHttpServer())
        .post('/packages')
        .set('Authorization', adminCredentials.bearerToken)
        .send(INVALID_PACKAGE_DATA.invalidServiceType);

      if (response.status === 400) {
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 400, got ${response.status}`,
          response.body,
        );
      }
    });
  });

  describe('Authorization Tests', () => {
    it('should reject package creation without authentication', async () => {
      testResultCollector.startTest(
        'Admin Package Creation',
        'Reject unauthenticated request',
      );

      const response = await request(app.getHttpServer())
        .post('/packages')
        .send(PACKAGE_CREATE_DTOS.valid);

      AuthTestUtils.expectUnauthorized(response);
      testResultCollector.endTest('PASS');
    });

    it('should reject package creation by regular user', async () => {
      testResultCollector.startTest(
        'Admin Package Creation',
        'Reject user access',
      );

      const response = await request(app.getHttpServer())
        .post('/packages')
        .set('Authorization', userCredentials.bearerToken)
        .send(PACKAGE_CREATE_DTOS.valid);

      AuthTestUtils.expectForbidden(response);
      testResultCollector.endTest('PASS');
    });

    it('should reject package creation by agent', async () => {
      testResultCollector.startTest(
        'Admin Package Creation',
        'Reject agent access',
      );

      const response = await request(app.getHttpServer())
        .post('/packages')
        .set('Authorization', agentCredentials.bearerToken)
        .send(PACKAGE_CREATE_DTOS.valid);

      AuthTestUtils.expectForbidden(response);
      testResultCollector.endTest('PASS');
    });
  });

  describe('Package Management', () => {
    it('should handle duplicate package names gracefully', async () => {
      testResultCollector.startTest(
        'Admin Package Creation',
        'Handle duplicate names',
      );

      // Mock a duplicate name error
      prismaService.packages.create.mockRejectedValue(
        new Error('Unique constraint failed on the fields: (`name`)'),
      );

      const response = await request(app.getHttpServer())
        .post('/packages')
        .set('Authorization', adminCredentials.bearerToken)
        .send(PACKAGE_CREATE_DTOS.valid);

      if (response.status >= 400) {
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          'Should have failed with duplicate name',
          response.body,
        );
      }
    });

    it('should create packages with proper ordering', async () => {
      testResultCollector.startTest(
        'Admin Package Creation',
        'Create with ordering',
      );

      const packageData = {
        ...PACKAGE_CREATE_DTOS.valid,
        order: 5,
      };

      const mockCreatedPackage = mockPrismaPackage({
        id: 'pkg-ordered-1',
        ...packageData,
      });

      prismaService.packages.create.mockResolvedValue(mockCreatedPackage);

      const response = await request(app.getHttpServer())
        .post('/packages')
        .set('Authorization', adminCredentials.bearerToken)
        .send(packageData);

      if (response.status === 201) {
        expect(response.body.order).toBe(5);
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 201, got ${response.status}`,
          response.body,
        );
      }
    });
  });
});
