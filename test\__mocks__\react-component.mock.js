/**
 * Mock for React TSX components in Jest tests
 * 
 * This mock prevents <PERSON><PERSON> from trying to parse React TSX files
 * during E2E tests where we don't need the actual React components.
 */

module.exports = {
  __esModule: true,
  default: () => 'MockedReactComponent',
  NotificationEmail: () => 'MockedNotificationEmail',
  ReminderEmail: () => 'MockedReminderEmail',
  OverdueReminderEmail: () => 'MockedOverdueReminderEmail',
};
