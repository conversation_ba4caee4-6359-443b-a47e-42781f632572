import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsDateString,
  IsUUID,
  IsNumber,
  IsBoolean,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  ApplicationStatus,
  WorkflowStatus,
  CallStatus,
  QueryStatus,
} from '@prisma/client';

/**
 * DTO for creating a new application
 */
export class CreateApplicationDto {
  @ApiProperty({
    description: 'ID of the service type for this application',
    example: 'clx1234567890',
  })
  @IsString()
  @IsNotEmpty()
  serviceTypeId: string;

  @ApiPropertyOptional({
    description: 'Additional notes or comments for the application',
    example: 'Urgent processing required for job start date',
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

/**
 * DTO for updating an existing application
 */
export class UpdateApplicationDto {
  @ApiPropertyOptional({
    description: 'Application status',
    enum: ApplicationStatus,
    example: ApplicationStatus.SUBMITTED,
  })
  @IsOptional()
  @IsEnum(ApplicationStatus)
  status?: ApplicationStatus;

  @ApiPropertyOptional({
    description: 'Date when application was submitted',
    example: '2025-01-25T10:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  submissionDate?: string;

  @ApiPropertyOptional({
    description: 'Date when application was completed',
    example: '2025-02-25T10:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  completionDate?: string;

  @ApiPropertyOptional({
    description: 'Additional notes or comments',
    example: 'Updated with additional documentation',
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

/**
 * DTO for filtering applications
 */
export class FilterApplicationDto {
  @ApiPropertyOptional({
    description: 'Filter by application status',
    enum: ApplicationStatus,
  })
  @IsOptional()
  @IsEnum(ApplicationStatus)
  status?: ApplicationStatus;

  @ApiPropertyOptional({
    description: 'Filter by service type ID',
    example: 'clx1234567890',
  })
  @IsOptional()
  @IsString()
  serviceTypeId?: string;

  @ApiPropertyOptional({
    description: 'Filter by user ID',
    example: 'clx0987654321',
  })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    default: 1,
  })
  @IsOptional()
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    default: 10,
  })
  @IsOptional()
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Sort field',
    example: 'createdAt',
    enum: ['createdAt', 'submissionDate', 'status', 'completionDate'],
  })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiPropertyOptional({
    description: 'Sort order',
    example: 'desc',
    enum: ['asc', 'desc'],
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';
}

/**
 * DTO for creating workflow steps
 */
export class CreateWorkflowStepDto {
  @ApiProperty({
    description: 'Name of the workflow step',
    example: 'Document Review',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Description of what happens in this step',
    example: 'Review and validate all submitted documents',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Order of this step in the workflow',
    example: 1,
  })
  @IsNotEmpty()
  order: number;

  @ApiProperty({
    description: 'Estimated duration in days',
    example: 5,
  })
  @IsNotEmpty()
  estimatedDuration: number;

  @ApiProperty({
    description: 'Service type this step belongs to',
    example: 'clx1234567890',
  })
  @IsString()
  @IsNotEmpty()
  serviceTypeId: string;
}

/**
 * DTO for updating workflow steps
 */
export class UpdateWorkflowStepDto {
  @ApiPropertyOptional({
    description: 'Name of the workflow step',
    example: 'Document Review',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiPropertyOptional({
    description: 'Description of what happens in this step',
    example: 'Review and validate all submitted documents',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  description?: string;

  @ApiPropertyOptional({
    description: 'Order of this step in the workflow',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  order?: number;

  @ApiPropertyOptional({
    description: 'Estimated duration in days',
    example: 5,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(365)
  @Type(() => Number)
  estimatedDuration?: number;
}

/**
 * DTO for workflow step response
 */
export class WorkflowStepResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the workflow step',
    example: 'clx1234567890abcdef',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the workflow step',
    example: 'Document Review',
  })
  name: string;

  @ApiProperty({
    description: 'Description of what happens in this step',
    example: 'Review and validate all submitted documents',
  })
  description: string;

  @ApiProperty({
    description: 'Order of this step in the workflow',
    example: 1,
  })
  order: number;

  @ApiProperty({
    description: 'Estimated duration in days',
    example: 5,
  })
  estimatedDuration: number;

  @ApiProperty({
    description: 'Service type this step belongs to',
    example: 'clx1234567890abcdef',
  })
  serviceTypeId: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Service type information',
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      description: { type: 'string' },
    },
  })
  serviceType?: any;
}

/**
 * DTO for updating workflow status
 */
export class UpdateWorkflowDto {
  @ApiPropertyOptional({
    description: 'Current step number',
    example: 2,
  })
  @IsOptional()
  currentStep?: number;

  @ApiPropertyOptional({
    description: 'Workflow status',
    enum: WorkflowStatus,
    example: WorkflowStatus.IN_PROGRESS,
  })
  @IsOptional()
  @IsEnum(WorkflowStatus)
  status?: WorkflowStatus;
}

/**
 * DTO for creating checkpoint calls
 */
export class CreateCheckpointCallDto {
  @ApiProperty({
    description: 'Scheduled date for the call',
    example: '2025-01-30T14:00:00.000Z',
  })
  @IsDateString()
  scheduledDate: string;

  @ApiPropertyOptional({
    description: 'Notes about the call',
    example: 'Discuss document requirements and next steps',
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    description: 'Application ID this call is for',
    example: 'clx1234567890',
  })
  @IsString()
  @IsNotEmpty()
  applicationId: string;
}

/**
 * DTO for updating checkpoint calls
 */
export class UpdateCheckpointCallDto {
  @ApiPropertyOptional({
    description: 'Date when call was completed',
    example: '2025-01-30T14:30:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  completedDate?: string;

  @ApiPropertyOptional({
    description: 'Notes from the call',
    example: 'Discussed next steps, client will submit additional documents',
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Call status',
    enum: CallStatus,
    example: CallStatus.COMPLETED,
  })
  @IsOptional()
  @IsEnum(CallStatus)
  status?: CallStatus;
}

/**
 * DTO for creating application queries
 */
export class CreateApplicationQueryDto {
  @ApiProperty({
    description: 'The query text from the client',
    example: 'When will my application be processed?',
  })
  @IsString()
  @IsNotEmpty()
  queryText: string;

  @ApiProperty({
    description: 'Application ID this query is for',
    example: 'clx1234567890',
  })
  @IsString()
  @IsNotEmpty()
  applicationId: string;
}

/**
 * DTO for responding to application queries
 */
export class RespondToQueryDto {
  @ApiProperty({
    description: 'Response text to the query',
    example:
      'Your application is currently in review and will be processed within 5-7 business days.',
  })
  @IsString()
  @IsNotEmpty()
  responseText: string;
}

/**
 * DTO for application progress response
 */
export class ApplicationProgressDto {
  @ApiProperty({ description: 'Application ID' })
  applicationId: string;

  @ApiProperty({ description: 'Current workflow step' })
  currentStep: number;

  @ApiProperty({ description: 'Total workflow steps' })
  totalSteps: number;

  @ApiProperty({ description: 'Progress percentage' })
  progressPercentage: number;

  @ApiProperty({ description: 'Current step name' })
  currentStepName: string;

  @ApiProperty({ description: 'Estimated completion date' })
  estimatedCompletion: Date;

  @ApiProperty({ description: 'Workflow status' })
  status: WorkflowStatus;
}

/**
 * DTO for enhanced application progress tracking
 */
export class EnhancedApplicationProgressDto {
  @ApiProperty({
    description: 'Current step number',
    example: 2,
  })
  currentStep: number;

  @ApiProperty({
    description: 'Total number of steps',
    example: 5,
  })
  totalSteps: number;

  @ApiProperty({
    description: 'Progress percentage (0-100)',
    example: 40,
    minimum: 0,
    maximum: 100,
  })
  progressPercentage: number;

  @ApiProperty({
    description: 'Current step name',
    example: 'Document Review',
  })
  currentStepName: string;

  @ApiProperty({
    description: 'Current step description',
    example: 'Review and validate all submitted documents',
  })
  currentStepDescription: string;

  @ApiProperty({
    description: 'Estimated completion date',
    example: '2024-02-15T00:00:00.000Z',
  })
  estimatedCompletion: Date;

  @ApiProperty({
    description: 'Days elapsed since submission',
    example: 5,
  })
  daysElapsed: number;

  @ApiProperty({
    description: 'Estimated days remaining',
    example: 10,
  })
  estimatedDaysRemaining: number;

  @ApiProperty({
    description: 'Workflow status',
    enum: WorkflowStatus,
    example: WorkflowStatus.IN_PROGRESS,
  })
  workflowStatus: WorkflowStatus;

  @ApiProperty({
    description: 'Application status',
    enum: ApplicationStatus,
    example: ApplicationStatus.IN_PROGRESS,
  })
  applicationStatus: ApplicationStatus;

  @ApiProperty({
    description: 'Whether the application is overdue',
    example: false,
  })
  isOverdue: boolean;

  @ApiProperty({
    description: 'Days overdue (if applicable)',
    example: 0,
  })
  daysOverdue: number;

  @ApiProperty({
    description: 'List of completed steps',
    type: [Object],
    example: [
      {
        step: 1,
        name: 'Initial Review',
        completedDate: '2024-01-10T00:00:00.000Z',
      },
      {
        step: 2,
        name: 'Document Verification',
        completedDate: '2024-01-15T00:00:00.000Z',
      },
    ],
  })
  completedSteps: Array<{
    step: number;
    name: string;
    description: string;
    completedDate?: Date;
    estimatedDuration: number;
    actualDuration?: number;
  }>;

  @ApiProperty({
    description: 'List of remaining steps',
    type: [Object],
    example: [
      { step: 3, name: 'Background Check', estimatedDuration: 7 },
      { step: 4, name: 'Final Review', estimatedDuration: 3 },
    ],
  })
  remainingSteps: Array<{
    step: number;
    name: string;
    description: string;
    estimatedDuration: number;
  }>;
}

/**
 * DTO for comprehensive application response
 */
export class ApplicationResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the application',
    example: 'clx1234567890abcdef',
  })
  id: string;

  @ApiProperty({
    description: 'Application status',
    enum: ApplicationStatus,
    example: ApplicationStatus.IN_PROGRESS,
  })
  status: ApplicationStatus;

  @ApiProperty({
    description: 'Submission date',
    example: '2024-01-15T10:30:00.000Z',
  })
  submissionDate?: Date;

  @ApiProperty({
    description: 'Completion date',
    example: '2024-02-15T10:30:00.000Z',
  })
  completionDate?: Date;

  @ApiProperty({
    description: 'User ID',
    example: 'clx1234567890abcdef',
  })
  userId: string;

  @ApiProperty({
    description: 'Service type ID',
    example: 'clx1234567890abcdef',
  })
  serviceTypeId: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'User information',
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      email: { type: 'string' },
    },
  })
  user?: any;

  @ApiPropertyOptional({
    description: 'Service type information',
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      description: { type: 'string' },
      price: { type: 'number' },
    },
  })
  serviceType?: any;

  @ApiPropertyOptional({
    description: 'Workflow information',
    type: 'object',
    properties: {
      id: { type: 'string' },
      currentStep: { type: 'number' },
      status: { type: 'string' },
      startDate: { type: 'string' },
      lastUpdated: { type: 'string' },
    },
  })
  workflow?: any;

  @ApiPropertyOptional({
    description: 'Application progress details',
    type: EnhancedApplicationProgressDto,
  })
  progress?: EnhancedApplicationProgressDto;

  @ApiPropertyOptional({
    description: 'Checkpoint calls',
    type: [Object],
  })
  checkpointCalls?: any[];

  @ApiPropertyOptional({
    description: 'Application queries',
    type: [Object],
  })
  applicationQuery?: any[];

  @ApiPropertyOptional({
    description: 'Associated documents',
    type: [Object],
  })
  documents?: any[];
}

/**
 * DTO for paginated application response
 */
export class PaginatedApplicationResponseDto {
  @ApiProperty({
    description: 'Array of applications',
    type: [ApplicationResponseDto],
  })
  data: ApplicationResponseDto[];

  @ApiProperty({
    description: 'Total number of applications',
    example: 25,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 3,
  })
  totalPages: number;
}
