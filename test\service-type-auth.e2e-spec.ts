import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { PrismaService } from '../src/utils/prisma.service';
import { MockPrismaService } from '../src/utils/prisma.service.mock';
import { TestAuthAppModule } from './test-auth-app.module';
import { JwtService } from '@nestjs/jwt';

/**
 * Authentication tests for ServiceType endpoints
 *
 * These tests verify that all /service-types/admin endpoints properly enforce
 * admin-only authentication using the JwtAdmin guard.
 */
describe('ServiceTypeController Authentication (e2e)', () => {
  let app: INestApplication;
  let prismaService: MockPrismaService;
  let jwtService: JwtService;

  // JWT secret for testing (matches the guard expectation)
  const JWT_SECRET = 'test-secret-key-for-e2e-testing';

  // Helper function to generate test tokens
  const generateTestToken = (role: string, userId: string = 'test-id') => {
    const payload = {
      id: userId,
      email: `${role}@test.com`,
      name: `Test ${role}`,
      role: role,
      sub: userId,
      iat: Math.floor(Date.now() / 1000),
    };
    return jwtService.sign(payload, {
      secret: JWT_SECRET,
      expiresIn: '1h',
    });
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [TestAuthAppModule],
    })
      .overrideProvider(PrismaService)
      .useClass(MockPrismaService)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ whitelist: true }));

    prismaService = moduleFixture.get<MockPrismaService>(PrismaService);
    jwtService = moduleFixture.get<JwtService>(JwtService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Authentication Tests - No Token', () => {
    it('GET /service-types/admin should return 401 without token', () => {
      return request(app.getHttpServer())
        .get('/service-types/admin')
        .expect(401);
    });

    it('GET /service-types/admin/:id should return 401 without token', () => {
      return request(app.getHttpServer())
        .get('/service-types/admin/test-id')
        .expect(401);
    });

    it('POST /service-types/admin should return 401 without token', () => {
      return request(app.getHttpServer())
        .post('/service-types/admin')
        .send({
          name: 'Test Service Type',
          description: 'Test description',
          price: 1000,
        })
        .expect(401);
    });

    it('PUT /service-types/admin/:id should return 401 without token', () => {
      return request(app.getHttpServer())
        .put('/service-types/admin/test-id')
        .send({
          name: 'Updated Service Type',
        })
        .expect(401);
    });

    it('DELETE /service-types/admin/:id should return 401 without token', () => {
      return request(app.getHttpServer())
        .delete('/service-types/admin/test-id')
        .expect(401);
    });

    it('GET /service-types/admin/:id/document-requirements should return 401 without token', () => {
      return request(app.getHttpServer())
        .get('/service-types/admin/test-id/document-requirements')
        .expect(401);
    });
  });

  describe('Authentication Tests - Invalid Token', () => {
    const invalidToken = 'Bearer invalid-token';

    it('GET /service-types/admin should return 401 with invalid token', () => {
      return request(app.getHttpServer())
        .get('/service-types/admin')
        .set('Authorization', invalidToken)
        .expect(401);
    });

    it('GET /service-types/admin/:id should return 401 with invalid token', () => {
      return request(app.getHttpServer())
        .get('/service-types/admin/test-id')
        .set('Authorization', invalidToken)
        .expect(401);
    });

    it('POST /service-types/admin should return 401 with invalid token', () => {
      return request(app.getHttpServer())
        .post('/service-types/admin')
        .set('Authorization', invalidToken)
        .send({
          name: 'Test Service Type',
          description: 'Test description',
          price: 1000,
        })
        .expect(401);
    });

    it('PUT /service-types/admin/:id should return 401 with invalid token', () => {
      return request(app.getHttpServer())
        .put('/service-types/admin/test-id')
        .set('Authorization', invalidToken)
        .send({
          name: 'Updated Service Type',
        })
        .expect(401);
    });

    it('DELETE /service-types/admin/:id should return 401 with invalid token', () => {
      return request(app.getHttpServer())
        .delete('/service-types/admin/test-id')
        .set('Authorization', invalidToken)
        .expect(401);
    });

    it('GET /service-types/admin/:id/document-requirements should return 401 with invalid token', () => {
      return request(app.getHttpServer())
        .get('/service-types/admin/test-id/document-requirements')
        .set('Authorization', invalidToken)
        .expect(401);
    });
  });

  describe('Authentication Tests - User Token (Non-Admin)', () => {
    let userToken: string;

    beforeAll(() => {
      userToken = `Bearer ${generateTestToken('user', 'user-test-id-1')}`;
    });

    it('GET /service-types/admin should return 403 with user token', () => {
      return request(app.getHttpServer())
        .get('/service-types/admin')
        .set('Authorization', userToken)
        .expect(403);
    });

    it('GET /service-types/admin/:id should return 403 with user token', () => {
      return request(app.getHttpServer())
        .get('/service-types/admin/test-id')
        .set('Authorization', userToken)
        .expect(403);
    });

    it('POST /service-types/admin should return 403 with user token', () => {
      return request(app.getHttpServer())
        .post('/service-types/admin')
        .set('Authorization', userToken)
        .send({
          name: 'Test Service Type',
          description: 'Test description',
          price: 1000,
        })
        .expect(403);
    });

    it('PUT /service-types/admin/:id should return 403 with user token', () => {
      return request(app.getHttpServer())
        .put('/service-types/admin/test-id')
        .set('Authorization', userToken)
        .send({
          name: 'Updated Service Type',
        })
        .expect(403);
    });

    it('DELETE /service-types/admin/:id should return 403 with user token', () => {
      return request(app.getHttpServer())
        .delete('/service-types/admin/test-id')
        .set('Authorization', userToken)
        .expect(403);
    });

    it('GET /service-types/admin/:id/document-requirements should return 403 with user token', () => {
      return request(app.getHttpServer())
        .get('/service-types/admin/test-id/document-requirements')
        .set('Authorization', userToken)
        .expect(403);
    });
  });

  describe('Authentication Tests - Valid Admin Token', () => {
    let adminToken: string;

    beforeAll(() => {
      adminToken = `Bearer ${generateTestToken('admin', 'admin-test-id-1')}`;
    });

    beforeEach(() => {
      // Setup mock data for successful operations
      const mockServiceType = {
        id: 'test-id',
        name: 'Test Service Type',
        description: 'Test description',
        price: 1000,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaService.service_type.findMany.mockResolvedValue([mockServiceType]);
      prismaService.service_type.count.mockResolvedValue(1);
      prismaService.service_type.findUnique.mockResolvedValue(mockServiceType);
      prismaService.service_type.create.mockResolvedValue(mockServiceType);
      prismaService.service_type.update.mockResolvedValue(mockServiceType);
      prismaService.service_type.delete.mockResolvedValue(mockServiceType);
      prismaService.application.count.mockResolvedValue(0);
      prismaService.document_requirement.findMany.mockResolvedValue([]);
    });

    it('GET /service-types/admin should return 200 with admin token', () => {
      return request(app.getHttpServer())
        .get('/service-types/admin')
        .set('Authorization', adminToken)
        .expect(200);
    });

    it('GET /service-types/admin/:id should return 200 with admin token', () => {
      return request(app.getHttpServer())
        .get('/service-types/admin/test-id')
        .set('Authorization', adminToken)
        .expect(200);
    });

    it('POST /service-types/admin should return 201 with admin token', () => {
      return request(app.getHttpServer())
        .post('/service-types/admin')
        .set('Authorization', adminToken)
        .send({
          name: 'Test Service Type',
          description: 'Test description',
          price: 1000,
        })
        .expect(201);
    });

    it('PUT /service-types/admin/:id should return 200 with admin token', () => {
      return request(app.getHttpServer())
        .put('/service-types/admin/test-id')
        .set('Authorization', adminToken)
        .send({
          name: 'Updated Service Type',
        })
        .expect(200);
    });

    it('DELETE /service-types/admin/:id should return 200 with admin token', () => {
      return request(app.getHttpServer())
        .delete('/service-types/admin/test-id')
        .set('Authorization', adminToken)
        .expect(200);
    });

    it('GET /service-types/admin/:id/document-requirements should return 200 with admin token', () => {
      return request(app.getHttpServer())
        .get('/service-types/admin/test-id/document-requirements')
        .set('Authorization', adminToken)
        .expect(200);
    });
  });
});
