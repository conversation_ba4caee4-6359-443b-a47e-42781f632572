import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { PrismaService } from '../src/utils/prisma.service';
import { MockPrismaService } from '../src/utils/prisma.service.mock';
import { TestAppModule } from './test-app.module';
import { ApplicationStatus, WorkflowStatus, QueryStatus } from '@prisma/client';
import { JwtService } from '@nestjs/jwt';

/**
 * E2E tests for Application endpoints
 *
 * Tests all application-related API endpoints including:
 * - Application CRUD operations
 * - Workflow management
 * - Progress tracking
 * - Checkpoint calls
 * - Application queries
 */
describe('ApplicationController (e2e)', () => {
  let app: INestApplication;
  let prismaService: MockPrismaService;
  let jwtService: JwtService;

  let adminToken: string;
  let userToken: string;
  let agentToken: string;

  // Helper function to generate test JWT tokens
  const generateTestToken = (role: string, userId: string) => {
    return jwtService.sign(
      {
        id: userId,
        email: `${role}@test.com`,
        role: role,
      },
      {
        secret: 'test-secret-key-for-e2e-testing',
        expiresIn: '1h',
      },
    );
  };

  const mockServiceType = {
    id: 'cld123abc456def789',
    name: 'Critical Skills Employment Permit',
    description: 'Test service type',
    price: 1000,
    createdAt: new Date(),
    updatedAt: new Date(),
    documentRequirements: [],
    workflowSteps: [
      {
        id: 'step-1',
        name: 'Document Review',
        description: 'Review submitted documents',
        order: 1,
        estimatedDuration: 5,
        serviceTypeId: 'cld123abc456def789',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ],
  };

  const mockUser = {
    id: 'user-1',
    name: 'Test User',
    email: '<EMAIL>',
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [TestAppModule],
    })
      .overrideProvider(PrismaService)
      .useClass(MockPrismaService)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ whitelist: true }));

    prismaService = moduleFixture.get<MockPrismaService>(PrismaService);
    jwtService = moduleFixture.get<JwtService>(JwtService);

    // Generate real JWT tokens for testing
    adminToken = `Bearer ${generateTestToken('admin', 'admin-1')}`;
    userToken = `Bearer ${generateTestToken('user', 'user-1')}`;
    agentToken = `Bearer ${generateTestToken('agent', 'agent-1')}`;

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(() => {
    prismaService.clearTestData();
    jest.clearAllMocks();
  });

  describe('POST /applications', () => {
    it('should create a new application', () => {
      const mockApplication = {
        id: 'cld456def789abc123',
        userId: 'user-1',
        serviceTypeId: 'cld123abc456def789',
        status: ApplicationStatus.DRAFT,
        submissionDate: null,
        completionDate: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: mockServiceType,
        user: mockUser,
      };

      prismaService.service_type.findUnique.mockResolvedValue(mockServiceType);
      prismaService.application.create.mockResolvedValue(mockApplication);

      return request(app.getHttpServer())
        .post('/applications')
        .set('Authorization', userToken)
        .send({
          serviceTypeId: 'cld123abc456def789',
        })
        .expect(201)
        .expect((res) => {
          expect(res.body.id).toBe('cld456def789abc123');
          expect(res.body.status).toBe(ApplicationStatus.DRAFT);
          expect(res.body.serviceType.name).toBe(
            'Critical Skills Employment Permit',
          );
        });
    });

    it('should return 400 for invalid service type', () => {
      prismaService.service_type.findUnique.mockResolvedValue(null);

      return request(app.getHttpServer())
        .post('/applications')
        .set('Authorization', userToken)
        .send({
          serviceTypeId: 'cld789abc123def456',
        })
        .expect(404);
    });

    it('should return 401 without authentication', () => {
      return request(app.getHttpServer())
        .post('/applications')
        .send({
          serviceTypeId: 'cld123abc456def789',
        })
        .expect(401);
    });
  });

  describe('GET /applications', () => {
    it('should return applications for admin', () => {
      const mockApplications = [
        {
          id: 'cld456def789abc123',
          userId: 'user-1',
          serviceTypeId: 'cld123abc456def789',
          status: ApplicationStatus.DRAFT,
          submissionDate: null,
          completionDate: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          serviceType: mockServiceType,
          user: mockUser,
          workflow: null,
        },
      ];

      prismaService.application.findMany.mockResolvedValue(mockApplications);
      prismaService.application.count.mockResolvedValue(1);

      return request(app.getHttpServer())
        .get('/applications')
        .set('Authorization', adminToken)
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveLength(1);
          expect(res.body.total).toBe(1);
          expect(res.body.page).toBe(1);
          expect(res.body.totalPages).toBe(1);
        });
    });

    it('should return applications for agent', () => {
      const mockApplications = [
        {
          id: 'cld456def789abc123',
          userId: 'user-1',
          serviceTypeId: 'cld123abc456def789',
          status: ApplicationStatus.DRAFT,
          submissionDate: null,
          completionDate: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          serviceType: mockServiceType,
          user: mockUser,
          workflow: null,
        },
      ];

      prismaService.application.findMany.mockResolvedValue(mockApplications);
      prismaService.application.count.mockResolvedValue(1);

      return request(app.getHttpServer())
        .get('/applications')
        .set('Authorization', agentToken)
        .expect(200);
    });

    it('should return 403 for regular user', () => {
      return request(app.getHttpServer())
        .get('/applications')
        .set('Authorization', userToken)
        .expect(403);
    });
  });

  describe('GET /applications/my-applications', () => {
    it("should return user's own applications", () => {
      const mockApplications = [
        {
          id: 'cld456def789abc123',
          userId: 'user-1',
          serviceTypeId: 'cld123abc456def789',
          status: ApplicationStatus.DRAFT,
          submissionDate: null,
          completionDate: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          serviceType: mockServiceType,
          user: mockUser,
          workflow: null,
        },
      ];

      prismaService.application.findMany.mockResolvedValue(mockApplications);
      prismaService.application.count.mockResolvedValue(1);

      return request(app.getHttpServer())
        .get('/applications/my-applications')
        .set('Authorization', userToken)
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveLength(1);
          expect(res.body.data[0].userId).toBe('user-1');
        });
    });
  });

  describe('GET /applications/statistics', () => {
    it('should return statistics for admin', () => {
      const mockStats = {
        total: 10,
        byStatus: {
          draft: 2,
          submitted: 3,
          inProgress: 3,
          completed: 1,
          rejected: 1,
        },
      };

      prismaService.application.count
        .mockResolvedValueOnce(10) // total
        .mockResolvedValueOnce(2) // draft
        .mockResolvedValueOnce(3) // submitted
        .mockResolvedValueOnce(3) // in progress
        .mockResolvedValueOnce(1) // completed
        .mockResolvedValueOnce(1); // rejected

      return request(app.getHttpServer())
        .get('/applications/statistics')
        .set('Authorization', adminToken)
        .expect(200)
        .expect((res) => {
          expect(res.body.total).toBe(10);
          expect(res.body.byStatus.draft).toBe(2);
        });
    });

    it('should return 403 for non-admin', () => {
      return request(app.getHttpServer())
        .get('/applications/statistics')
        .set('Authorization', userToken)
        .expect(403);
    });
  });

  describe('GET /applications/:id', () => {
    it('should return application details for owner', () => {
      const mockApplication = {
        id: 'cld456def789abc123',
        userId: 'user-1',
        serviceTypeId: 'cld123abc456def789',
        status: ApplicationStatus.DRAFT,
        submissionDate: null,
        completionDate: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: mockServiceType,
        user: mockUser,
        workflow: null,
        checkpointCalls: [],
        applicationQuery: [],
      };

      prismaService.application.findUnique.mockResolvedValue(mockApplication);

      return request(app.getHttpServer())
        .get('/applications/cld456def789abc123')
        .set('Authorization', userToken)
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe('cld456def789abc123');
          expect(res.body.serviceType.name).toBe(
            'Critical Skills Employment Permit',
          );
        });
    });

    it('should return 404 for non-existent application', () => {
      prismaService.application.findUnique.mockResolvedValue(null);

      return request(app.getHttpServer())
        .get('/applications/invalid-id')
        .set('Authorization', userToken)
        .expect(404);
    });
  });

  describe('PATCH /applications/:id', () => {
    it('should update application status', () => {
      const mockApplication = {
        id: 'cld456def789abc123',
        userId: 'user-1',
        serviceTypeId: 'cld123abc456def789',
        status: ApplicationStatus.DRAFT,
        submissionDate: null,
        completionDate: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: mockServiceType,
        user: mockUser,
        workflow: null,
        checkpointCalls: [],
        applicationQuery: [],
      };

      const updatedApplication = {
        ...mockApplication,
        status: ApplicationStatus.SUBMITTED,
        submissionDate: new Date(),
      };

      prismaService.application.findUnique.mockResolvedValue(mockApplication);
      prismaService.application.update.mockResolvedValue(updatedApplication);
      prismaService.application_workflow.create.mockResolvedValue({
        id: 'workflow-1',
        applicationId: 'cld456def789abc123',
        currentStep: 1,
        status: WorkflowStatus.IN_PROGRESS,
        startDate: new Date(),
        lastUpdated: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      return request(app.getHttpServer())
        .patch('/applications/cld456def789abc123')
        .set('Authorization', userToken)
        .send({
          status: ApplicationStatus.SUBMITTED,
        })
        .expect(200)
        .expect((res) => {
          expect(res.body.status).toBe(ApplicationStatus.SUBMITTED);
        });
    });
  });

  describe('DELETE /applications/:id', () => {
    it('should delete draft application', () => {
      const mockApplication = {
        id: 'cld456def789abc123',
        userId: 'user-1',
        serviceTypeId: 'cld123abc456def789',
        status: ApplicationStatus.DRAFT,
        submissionDate: null,
        completionDate: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: mockServiceType,
        user: mockUser,
        workflow: null,
        checkpointCalls: [],
        applicationQuery: [],
      };

      prismaService.application.findUnique.mockResolvedValue(mockApplication);
      prismaService.application.delete.mockResolvedValue(mockApplication);

      return request(app.getHttpServer())
        .delete('/applications/cld456def789abc123')
        .set('Authorization', userToken)
        .expect(200);
    });

    it('should return 400 for non-draft application', () => {
      const mockApplication = {
        id: 'cld456def789abc123',
        userId: 'user-1',
        serviceTypeId: 'cld123abc456def789',
        status: ApplicationStatus.SUBMITTED,
        submissionDate: new Date(),
        completionDate: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: mockServiceType,
        user: mockUser,
        workflow: null,
        checkpointCalls: [],
        applicationQuery: [],
      };

      prismaService.application.findUnique.mockResolvedValue(mockApplication);

      return request(app.getHttpServer())
        .delete('/applications/cld456def789abc123')
        .set('Authorization', userToken)
        .expect(400);
    });
  });

  describe('GET /applications/:id/progress', () => {
    it('should return application progress', () => {
      const mockApplication = {
        id: 'cld456def789abc123',
        userId: 'user-1',
        serviceTypeId: 'cld123abc456def789',
        status: ApplicationStatus.SUBMITTED,
        submissionDate: new Date(),
        completionDate: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: mockServiceType,
        user: mockUser,
        workflow: {
          id: 'workflow-1',
          currentStep: 1,
          status: WorkflowStatus.IN_PROGRESS,
          startDate: new Date(),
          lastUpdated: new Date(),
          applicationId: 'cld456def789abc123',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        checkpointCalls: [],
        applicationQuery: [],
      };

      prismaService.application.findUnique.mockResolvedValue(mockApplication);

      return request(app.getHttpServer())
        .get('/applications/cld456def789abc123/progress')
        .set('Authorization', userToken)
        .expect(200)
        .expect((res) => {
          expect(res.body.applicationId).toBe('cld456def789abc123');
          expect(res.body.currentStep).toBe(1);
          expect(res.body.progressPercentage).toBe(100);
          expect(res.body.status).toBe(WorkflowStatus.IN_PROGRESS);
        });
    });
  });

  describe('PATCH /applications/:id/workflow', () => {
    it('should advance workflow for admin', () => {
      const mockApplication = {
        id: 'cld456def789abc123',
        userId: 'user-1',
        serviceTypeId: 'cld123abc456def789',
        status: ApplicationStatus.IN_PROGRESS,
        submissionDate: new Date(),
        completionDate: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: mockServiceType,
        user: mockUser,
        workflow: {
          id: 'workflow-1',
          currentStep: 1,
          status: WorkflowStatus.IN_PROGRESS,
          startDate: new Date(),
          lastUpdated: new Date(),
          applicationId: 'cld456def789abc123',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        checkpointCalls: [],
        applicationQuery: [],
      };

      prismaService.application.findUnique.mockResolvedValue(mockApplication);
      prismaService.application_workflow.update.mockResolvedValue({
        ...mockApplication.workflow,
        currentStep: 2,
      });

      return request(app.getHttpServer())
        .patch('/applications/cld456def789abc123/workflow')
        .set('Authorization', adminToken)
        .send({
          currentStep: 2,
        })
        .expect(200);
    });

    it('should return 403 for non-admin', () => {
      return request(app.getHttpServer())
        .patch('/applications/cld456def789abc123/workflow')
        .set('Authorization', userToken)
        .send({
          currentStep: 2,
        })
        .expect(403);
    });
  });

  describe('POST /applications/checkpoint-calls', () => {
    it('should create checkpoint call for admin', () => {
      const mockApplication = {
        id: 'cld456def789abc123',
        userId: 'user-1',
        serviceTypeId: 'cld123abc456def789',
        status: ApplicationStatus.IN_PROGRESS,
        submissionDate: new Date(),
        completionDate: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: mockServiceType,
        user: mockUser,
      };

      const mockCall = {
        id: 'call-1',
        scheduledDate: new Date('2025-01-30T14:00:00.000Z'),
        completedDate: null,
        notes: 'Initial consultation',
        status: 'SCHEDULED',
        applicationId: 'cld456def789abc123',
        createdAt: new Date(),
        updatedAt: new Date(),
        application: mockApplication,
      };

      prismaService.application.findUnique.mockResolvedValue(mockApplication);
      prismaService.checkpoint_call.create.mockResolvedValue(mockCall);

      return request(app.getHttpServer())
        .post('/applications/checkpoint-calls')
        .set('Authorization', adminToken)
        .send({
          scheduledDate: '2025-01-30T14:00:00.000Z',
          notes: 'Initial consultation',
          applicationId: 'cld456def789abc123',
        })
        .expect(201)
        .expect((res) => {
          expect(res.body.id).toBe('call-1');
          expect(res.body.status).toBe('SCHEDULED');
        });
    });

    it('should return 403 for non-admin', () => {
      return request(app.getHttpServer())
        .post('/applications/checkpoint-calls')
        .set('Authorization', userToken)
        .send({
          scheduledDate: '2025-01-30T14:00:00.000Z',
          applicationId: 'cld456def789abc123',
        })
        .expect(403);
    });
  });

  describe('GET /applications/:id/checkpoint-calls', () => {
    it('should return checkpoint calls for application owner', () => {
      const mockApplication = {
        id: 'cld456def789abc123',
        userId: 'user-1',
        serviceTypeId: 'cld123abc456def789',
        status: ApplicationStatus.IN_PROGRESS,
        submissionDate: new Date(),
        completionDate: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: mockServiceType,
        user: mockUser,
        workflow: null,
        checkpointCalls: [],
        applicationQuery: [],
      };

      const mockCalls = [
        {
          id: 'call-1',
          scheduledDate: new Date('2025-01-30T14:00:00.000Z'),
          completedDate: null,
          notes: 'Initial consultation',
          status: 'SCHEDULED',
          applicationId: 'cld456def789abc123',
          createdAt: new Date(),
          updatedAt: new Date(),
          application: mockApplication,
        },
      ];

      prismaService.application.findUnique.mockResolvedValue(mockApplication);
      prismaService.checkpoint_call.findMany.mockResolvedValue(mockCalls);

      return request(app.getHttpServer())
        .get('/applications/cld456def789abc123/checkpoint-calls')
        .set('Authorization', userToken)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveLength(1);
          expect(res.body[0].id).toBe('call-1');
        });
    });
  });

  describe('POST /applications/queries', () => {
    it('should create application query', () => {
      const mockApplication = {
        id: 'cld456def789abc123',
        userId: 'user-1',
        serviceTypeId: 'cld123abc456def789',
        status: ApplicationStatus.IN_PROGRESS,
        submissionDate: new Date(),
        completionDate: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: mockServiceType,
        user: mockUser,
        workflow: null,
        checkpointCalls: [],
        applicationQuery: [],
      };

      const mockQuery = {
        id: 'query-1',
        queryText: 'When will my application be processed?',
        responseText: null,
        queryDate: new Date(),
        responseDate: null,
        status: QueryStatus.PENDING,
        applicationId: 'cld456def789abc123',
        createdAt: new Date(),
        updatedAt: new Date(),
        application: mockApplication,
      };

      prismaService.application.findUnique.mockResolvedValue(mockApplication);
      prismaService.application_query.create.mockResolvedValue(mockQuery);

      return request(app.getHttpServer())
        .post('/applications/queries')
        .set('Authorization', userToken)
        .send({
          queryText: 'When will my application be processed?',
          applicationId: 'cld456def789abc123',
        })
        .expect(201)
        .expect((res) => {
          expect(res.body.id).toBe('query-1');
          expect(res.body.status).toBe(QueryStatus.PENDING);
        });
    });
  });

  describe('PATCH /applications/queries/:queryId/respond', () => {
    it('should respond to query for admin', () => {
      const mockQuery = {
        id: 'query-1',
        queryText: 'When will my application be processed?',
        responseText: null,
        queryDate: new Date(),
        responseDate: null,
        status: QueryStatus.PENDING,
        applicationId: 'cld456def789abc123',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const respondedQuery = {
        ...mockQuery,
        responseText:
          'Your application will be processed within 5-7 business days.',
        responseDate: new Date(),
        status: QueryStatus.RESPONDED,
        application: {
          id: 'cld456def789abc123',
          userId: 'user-1',
          serviceType: mockServiceType,
          user: mockUser,
        },
      };

      prismaService.application_query.findUnique.mockResolvedValue(mockQuery);
      prismaService.application_query.update.mockResolvedValue(respondedQuery);

      return request(app.getHttpServer())
        .patch('/applications/queries/query-1/respond')
        .set('Authorization', adminToken)
        .send({
          responseText:
            'Your application will be processed within 5-7 business days.',
        })
        .expect(200)
        .expect((res) => {
          expect(res.body.responseText).toBe(
            'Your application will be processed within 5-7 business days.',
          );
          expect(res.body.status).toBe(QueryStatus.RESPONDED);
        });
    });

    it('should return 403 for non-admin', () => {
      return request(app.getHttpServer())
        .patch('/applications/queries/query-1/respond')
        .set('Authorization', userToken)
        .send({
          responseText: 'Test response',
        })
        .expect(403);
    });
  });

  describe('GET /applications/:id/queries', () => {
    it('should return queries for application owner', () => {
      const mockApplication = {
        id: 'cld456def789abc123',
        userId: 'user-1',
        serviceTypeId: 'cld123abc456def789',
        status: ApplicationStatus.IN_PROGRESS,
        submissionDate: new Date(),
        completionDate: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: mockServiceType,
        user: mockUser,
        workflow: null,
        checkpointCalls: [],
        applicationQuery: [],
      };

      const mockQueries = [
        {
          id: 'query-1',
          queryText: 'When will my application be processed?',
          responseText:
            'Your application will be processed within 5-7 business days.',
          queryDate: new Date(),
          responseDate: new Date(),
          status: QueryStatus.RESPONDED,
          applicationId: 'cld456def789abc123',
          createdAt: new Date(),
          updatedAt: new Date(),
          application: mockApplication,
        },
      ];

      prismaService.application.findUnique.mockResolvedValue(mockApplication);
      prismaService.application_query.findMany.mockResolvedValue(mockQueries);

      return request(app.getHttpServer())
        .get('/applications/cld456def789abc123/queries')
        .set('Authorization', userToken)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveLength(1);
          expect(res.body[0].id).toBe('query-1');
          expect(res.body[0].status).toBe(QueryStatus.RESPONDED);
        });
    });
  });
});
