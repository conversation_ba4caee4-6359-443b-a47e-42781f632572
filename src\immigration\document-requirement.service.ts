import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../utils/prisma.service';
import {
  CreateDocumentRequirementDto,
  UpdateDocumentRequirementDto,
  FilterDocumentRequirementDto,
  DocumentRequirementResponseDto,
  PaginatedDocumentRequirementResponseDto,
} from './dto/document-requirement.dto';
import { document_requirement } from '@prisma/client';

@Injectable()
export class DocumentRequirementService {
  private readonly logger = new Logger(DocumentRequirementService.name);

  constructor(private prisma: PrismaService) {}

  /**
   * Create a new document requirement
   * @param dto Data for creating a document requirement
   * @returns The created document requirement
   * @throws BadRequestException if service type not found
   */
  async create(
    dto: CreateDocumentRequirementDto,
  ): Promise<document_requirement> {
    // Check if service type exists
    const serviceType = await this.prisma.service_type.findUnique({
      where: { id: dto.serviceTypeId },
    });

    if (!serviceType) {
      throw new BadRequestException(
        `Service type with ID ${dto.serviceTypeId} not found`,
      );
    }

    try {
      const documentRequirement = await this.prisma.document_requirement.create(
        {
          data: {
            name: dto.name,
            description: dto.description,
            required: dto.required ?? true,
            category: dto.category,
            serviceTypeId: dto.serviceTypeId,
          },
        },
      );

      this.logger.log(
        `Document requirement created with ID: ${documentRequirement.id}`,
      );
      return documentRequirement;
    } catch (error) {
      this.logger.error(
        `Failed to create document requirement: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        'Failed to create document requirement: ' + error.message,
      );
    }
  }

  /**
   * Get all document requirements with optional filtering and pagination
   * @param filterDto Filter and pagination options
   * @returns Paginated list of document requirements
   */
  async findAll(
    filterDto: FilterDocumentRequirementDto,
  ): Promise<PaginatedDocumentRequirementResponseDto> {
    const {
      search,
      category,
      required,
      serviceTypeId,
      page = 1,
      limit = 10,
      sortBy = 'name',
      sortOrder = 'asc',
    } = filterDto;

    const effectiveLimit = Math.min(limit, 100);
    const skip = (page - 1) * effectiveLimit;

    this.logger.log(
      `Finding document requirements with filters: ${JSON.stringify(filterDto)}`,
    );

    // Build the where clause based on filters
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (category) {
      where.category = category;
    }

    if (required !== undefined) {
      where.required = required;
    }

    if (serviceTypeId) {
      where.serviceTypeId = serviceTypeId;
    }

    // Build the orderBy clause
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    try {
      const [data, total] = await Promise.all([
        this.prisma.document_requirement.findMany({
          where,
          skip,
          take: effectiveLimit,
          orderBy,
          include: {
            serviceType: {
              select: {
                id: true,
                name: true,
                description: true,
                price: true,
              },
            },
          },
        }),
        this.prisma.document_requirement.count({ where }),
      ]);

      const totalPages = Math.ceil(total / effectiveLimit);

      this.logger.log(
        `Found ${data.length} document requirements out of ${total} total`,
      );

      return {
        data: data.map(this.mapToResponseDto),
        total,
        page,
        limit: effectiveLimit,
        totalPages,
      };
    } catch (error) {
      this.logger.error(
        `Failed to fetch document requirements: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        'Failed to fetch document requirements: ' + error.message,
      );
    }
  }

  /**
   * Get a document requirement by ID
   * @param id Document requirement ID
   * @returns The document requirement
   * @throws NotFoundException if document requirement not found
   */
  async findOne(id: string): Promise<document_requirement> {
    const documentRequirement =
      await this.prisma.document_requirement.findUnique({
        where: { id },
        include: { serviceType: true },
      });

    if (!documentRequirement) {
      throw new NotFoundException(
        `Document requirement with ID ${id} not found`,
      );
    }

    return documentRequirement;
  }

  /**
   * Update a document requirement
   * @param id Document requirement ID
   * @param dto Data for updating the document requirement
   * @returns The updated document requirement
   * @throws NotFoundException if document requirement not found
   * @throws BadRequestException if service type not found
   */
  async update(
    id: string,
    dto: UpdateDocumentRequirementDto,
  ): Promise<document_requirement> {
    // Check if document requirement exists
    await this.findOne(id);

    // Check if service type exists if provided
    if (dto.serviceTypeId) {
      const serviceType = await this.prisma.service_type.findUnique({
        where: { id: dto.serviceTypeId },
      });

      if (!serviceType) {
        throw new BadRequestException(
          `Service type with ID ${dto.serviceTypeId} not found`,
        );
      }
    }

    try {
      return await this.prisma.document_requirement.update({
        where: { id },
        data: {
          ...(dto.name && { name: dto.name }),
          ...(dto.description && { description: dto.description }),
          ...(dto.required !== undefined && { required: dto.required }),
          ...(dto.category && { category: dto.category }),
          ...(dto.serviceTypeId && { serviceTypeId: dto.serviceTypeId }),
        },
      });
    } catch (error) {
      throw new BadRequestException(
        'Failed to update document requirement: ' + error.message,
      );
    }
  }

  /**
   * Delete a document requirement
   * @param id Document requirement ID
   * @returns The deleted document requirement
   * @throws NotFoundException if document requirement not found
   */
  async remove(id: string): Promise<document_requirement> {
    // Check if document requirement exists
    await this.findOne(id);

    try {
      const deletedRequirement = await this.prisma.document_requirement.delete({
        where: { id },
      });

      this.logger.log(`Document requirement deleted with ID: ${id}`);
      return deletedRequirement;
    } catch (error) {
      this.logger.error(
        `Failed to delete document requirement: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        'Failed to delete document requirement: ' + error.message,
      );
    }
  }

  /**
   * Map document requirement to response DTO
   * @param documentRequirement Document requirement from database
   * @returns Mapped response DTO
   */
  private mapToResponseDto = (
    documentRequirement: any,
  ): DocumentRequirementResponseDto => {
    return {
      id: documentRequirement.id,
      name: documentRequirement.name,
      description: documentRequirement.description,
      required: documentRequirement.required,
      category: documentRequirement.category,
      serviceTypeId: documentRequirement.serviceTypeId,
      createdAt: documentRequirement.createdAt,
      updatedAt: documentRequirement.updatedAt,
      serviceType: documentRequirement.serviceType || undefined,
    };
  };
}
