# Testing Guidelines

## Overview

This directory contains tests for the Career Ireland Immigration SaaS platform. The tests are designed to run in isolation without hitting production services like the database or Supabase storage.

## Testing Approach

We use the following approach to ensure tests don't interact with production services:

1. **Mock Services**: We use mock implementations of services like `PrismaService` and `MediaService` to prevent tests from hitting the actual database or Supabase storage.

2. **Environment Variables**: We set test-specific environment variables in the `jest-setup.ts` file to ensure tests use test configurations.

3. **In-Memory Data**: Our mock services use in-memory data structures to simulate database operations.

4. **Test Runner**: We use a custom test runner script (`scripts/run-tests.js`) that sets the correct environment variables.

## Mock Services

### MockPrismaService

The `MockPrismaService` provides an in-memory implementation of the Prisma client. It includes:

- In-memory storage for test data
- Methods that mimic Prisma's API
- Helper methods for seeding and clearing test data

### MockMediaService

The `MockMediaService` provides a mock implementation of the MediaService that:

- Returns fake URLs instead of uploading to Supabase
- Simulates file operations without actually performing them

## Running Tests

```bash
# Run all unit tests
$ npm test

# Run all tests (unit tests and e2e tests)
$ npm run test:all

# Run only DTO/model tests
$ npm run test:model

# Run only service tests
$ npm run test:service

# Run tests in watch mode (tests will re-run when files change)
$ npm run test:watch

# Run tests with coverage report
$ npm run test:cov

# Run tests in debug mode
$ npm run test:debug

# Run end-to-end tests
$ npm run test:e2e

# Run a specific test file
$ npm test -- test/document.service.spec.ts

# Run tests matching a specific pattern
$ npm test -- -t "uploadDocument"
```

## Writing Tests

When writing tests:

1. Use the mock services provided in the `src/utils/prisma.service.mock.ts` and `src/media/media.service.mock.ts` files.

2. Don't create direct connections to the database or Supabase in tests.

3. Use the `beforeEach` hook to set up test data and the `afterEach` hook to clean up.

4. Make assertions against the mock services to verify that the correct operations would be performed.

## Example

```typescript
describe('DocumentService', () => {
  let service: DocumentService;
  let prismaService: MockPrismaService;
  let mediaService: MockMediaService;

  beforeEach(async () => {
    // Create fresh instances of mock services
    const mockPrismaService = new MockPrismaService();
    const mockMediaService = new MockMediaService();

    // Seed test data
    mockPrismaService.seedTestData();

    // Set up the test module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentService,
        { provide: PrismaService, useValue: mockPrismaService },
        { provide: MediaService, useValue: mockMediaService },
      ],
    }).compile();

    // Get service instances
    service = module.get<DocumentService>(DocumentService);
    prismaService = module.get<MockPrismaService>(PrismaService);
    mediaService = module.get<MockMediaService>(MediaService);
  });

  afterEach(() => {
    // Clean up test data
    prismaService.clearTestData();
  });

  // Test cases...
});
```
