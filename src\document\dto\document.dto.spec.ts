import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { 
  DocumentFilterDto, 
  UploadDocumentDto, 
  VerifyDocumentDto 
} from './document.dto';
import { VerificationStatus } from '@prisma/client';

describe('Document DTOs', () => {
  describe('UploadDocumentDto', () => {
    it('should validate a valid UploadDocumentDto', async () => {
      // Arrange
      const dto = plainToInstance(UploadDocumentDto, {
        documentName: 'Passport',
        expiryDate: '2025-12-31',
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBe(0);
    });

    it('should validate a valid UploadDocumentDto without expiryDate', async () => {
      // Arrange
      const dto = plainToInstance(UploadDocumentDto, {
        documentName: 'Passport',
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBe(0);
    });

    it('should fail validation with missing documentName', async () => {
      // Arrange
      const dto = plainToInstance(UploadDocumentDto, {
        expiryDate: '2025-12-31',
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('documentName');
    });

    it('should fail validation with invalid expiryDate format', async () => {
      // Arrange
      const dto = plainToInstance(UploadDocumentDto, {
        documentName: 'Passport',
        expiryDate: 'invalid-date',
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('expiryDate');
    });
  });

  describe('VerifyDocumentDto', () => {
    it('should validate a valid VerifyDocumentDto with APPROVED status', async () => {
      // Arrange
      const dto = plainToInstance(VerifyDocumentDto, {
        verificationStatus: VerificationStatus.APPROVED,
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBe(0);
    });

    it('should validate a valid VerifyDocumentDto with REJECTED status and reason', async () => {
      // Arrange
      const dto = plainToInstance(VerifyDocumentDto, {
        verificationStatus: VerificationStatus.REJECTED,
        rejectionReason: 'Document is not legible',
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBe(0);
    });

    it('should fail validation with invalid verificationStatus', async () => {
      // Arrange
      const dto = plainToInstance(VerifyDocumentDto, {
        verificationStatus: 'INVALID_STATUS',
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('verificationStatus');
    });
  });

  describe('DocumentFilterDto', () => {
    it('should validate a valid DocumentFilterDto with verificationStatus', async () => {
      // Arrange
      const dto = plainToInstance(DocumentFilterDto, {
        verificationStatus: VerificationStatus.PENDING,
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBe(0);
    });

    it('should validate a valid DocumentFilterDto with documentName', async () => {
      // Arrange
      const dto = plainToInstance(DocumentFilterDto, {
        documentName: 'Passport',
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBe(0);
    });

    it('should validate a valid empty DocumentFilterDto', async () => {
      // Arrange
      const dto = plainToInstance(DocumentFilterDto, {});

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBe(0);
    });

    it('should fail validation with invalid verificationStatus', async () => {
      // Arrange
      const dto = plainToInstance(DocumentFilterDto, {
        verificationStatus: 'INVALID_STATUS',
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('verificationStatus');
    });
  });
});
