import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PrismaService } from '../src/utils/prisma.service';
import { ServiceTypeService } from '../src/immigration/service-type.service';
import { DocumentRequirementService } from '../src/immigration/document-requirement.service';
import { ServiceTypeController } from '../src/immigration/service-type.controller';
import { DocumentRequirementController } from '../src/immigration/document-requirement.controller';

/**
 * Test module for authentication testing
 * 
 * This module uses real controllers and guards (not mocks) to properly test
 * authentication and authorization behavior.
 */
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [
        () => ({
          jwt: {
            secret: 'test-jwt-secret-for-auth-tests',
          },
          jwtSecretKey: 'test-jwt-secret-for-auth-tests',
          jwtAdminSecretKey: 'test-admin-jwt-secret-for-auth-tests',
          jwtAgentSecretKey: 'test-agent-jwt-secret-for-auth-tests',
        }),
      ],
    }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('jwtSecretKey') || 'test-jwt-secret-for-auth-tests',
        signOptions: { expiresIn: '1d' },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [ServiceTypeController, DocumentRequirementController],
  providers: [PrismaService, ServiceTypeService, DocumentRequirementService],
})
export class TestAuthAppModule {}
