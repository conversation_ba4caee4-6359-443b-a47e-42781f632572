import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PrismaService } from '../utils/prisma.service';
import { MailerService } from '../mailer/mailer.service';

// Security Services
import { EncryptionService } from './encryption.service';
import { AuditService } from './audit.service';
import { SecurityMonitoringService } from './security-monitoring.service';
import { GDPRService } from './gdpr.service';

// Security Controllers
import { SecurityController } from './security.controller';
import { AuditController } from './audit.controller';
import { GDPRController } from './gdpr.controller';

// Security Interceptors and Guards
import { AuditInterceptor } from './audit.interceptor';

/**
 * Security Module
 *
 * Provides comprehensive security features including:
 * - Field-level encryption
 * - Comprehensive audit logging
 * - Security monitoring and alerting
 * - GDPR compliance features
 *
 * This module is marked as Global to make security services
 * available throughout the application.
 */
@Global()
@Module({
  imports: [
    ConfigModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'your-secret-key',
      signOptions: { expiresIn: '24h' },
    }),
  ],
  providers: [
    // Core Services
    PrismaService,
    MailerService,

    // Security Services
    EncryptionService,
    AuditService,
    SecurityMonitoringService,
    GDPRService,

    // Security Interceptors
    AuditInterceptor,
  ],
  controllers: [SecurityController, AuditController, GDPRController],
  exports: [
    // Export security services for use in other modules
    EncryptionService,
    AuditService,
    SecurityMonitoringService,
    GDPRService,
    AuditInterceptor,
  ],
})
export class SecurityModule {}
