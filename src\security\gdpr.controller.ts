import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { GDPRService } from './gdpr.service';
import { JwtGuard } from '../guards/jwt.guard';
import { JwtAdminAgent } from '../guards/jwt.admin-agent.guard';
import { ConsentType, DataExportType, DataDeletionType } from '@prisma/client';
import {
  RecordConsentDto,
  RequestDataExportDto,
  RequestDataDeletionDto,
  ConsentResponseDto,
  DataExportResponseDto,
  DataDeletionResponseDto,
} from './dto/security.dto';

/**
 * GDPR Compliance Controller
 *
 * Provides endpoints for GDPR compliance features including:
 * - Consent management
 * - Data export requests (Right to Access)
 * - Data deletion requests (Right to be Forgotten)
 * - Compliance reporting
 */
@ApiTags('GDPR')
@Controller('gdpr')
@ApiBearerAuth()
export class GDPRController {
  constructor(private readonly gdprService: GDPRService) {}

  /**
   * Record user consent
   */
  @Post('consent')
  @UseGuards(JwtGuard)
  @ApiOperation({ summary: 'Record user consent for data processing' })
  @ApiResponse({ status: 201, description: 'Consent recorded successfully' })
  async recordConsent(
    @Body() consentDto: RecordConsentDto,
    @Request() req: any,
  ) {
    const userId = req.user?.id;
    const ipAddress = req.ip || req.connection?.remoteAddress;
    const userAgent = req.headers['user-agent'];

    await this.gdprService.recordConsent({
      userId,
      ...consentDto,
      ipAddress,
      userAgent,
    });

    return { message: 'Consent recorded successfully' };
  }

  /**
   * Get user's consent status
   */
  @Get('consent')
  @UseGuards(JwtGuard)
  @ApiOperation({ summary: "Get current user's consent status" })
  @ApiResponse({
    status: 200,
    description: 'Consent status retrieved successfully',
  })
  async getUserConsents(@Request() req: any) {
    const userId = req.user?.id;
    return this.gdprService.getUserConsents(userId);
  }

  /**
   * Check specific consent
   */
  @Get('consent/:consentType')
  @UseGuards(JwtGuard)
  @ApiOperation({ summary: 'Check if user has granted specific consent' })
  @ApiResponse({
    status: 200,
    description: 'Consent status checked successfully',
  })
  @ApiParam({ name: 'consentType', enum: ConsentType })
  async checkConsent(
    @Param('consentType') consentType: ConsentType,
    @Request() req: any,
  ) {
    const userId = req.user?.id;
    const hasConsent = await this.gdprService.hasConsent(userId, consentType);
    return { hasConsent };
  }

  /**
   * Request data export (Right to Access)
   */
  @Post('export')
  @UseGuards(JwtGuard)
  @ApiOperation({ summary: 'Request data export (Right to Access)' })
  @ApiResponse({
    status: 201,
    description: 'Data export request created successfully',
  })
  async requestDataExport(
    @Body() exportDto: RequestDataExportDto,
    @Request() req: any,
  ) {
    const userId = req.user?.id;
    const ipAddress = req.ip || req.connection?.remoteAddress;
    const userAgent = req.headers['user-agent'];

    const requestId = await this.gdprService.requestDataExport({
      userId,
      ...exportDto,
      ipAddress,
      userAgent,
    });

    return {
      message: 'Data export request created successfully',
      requestId,
    };
  }

  /**
   * Get data export status
   */
  @Get('export/:requestId')
  @UseGuards(JwtGuard)
  @ApiOperation({ summary: 'Get data export request status' })
  @ApiResponse({
    status: 200,
    description: 'Export status retrieved successfully',
  })
  @ApiParam({ name: 'requestId', description: 'Data export request ID' })
  async getDataExportStatus(
    @Param('requestId') requestId: string,
    @Request() req: any,
  ) {
    const userId = req.user?.id;
    return this.gdprService.getDataExportStatus(requestId, userId);
  }

  /**
   * Request data deletion (Right to be Forgotten)
   */
  @Post('deletion')
  @UseGuards(JwtGuard)
  @ApiOperation({ summary: 'Request data deletion (Right to be Forgotten)' })
  @ApiResponse({
    status: 201,
    description: 'Data deletion request created successfully',
  })
  async requestDataDeletion(
    @Body() deletionDto: RequestDataDeletionDto,
    @Request() req: any,
  ) {
    const userId = req.user?.id;
    const ipAddress = req.ip || req.connection?.remoteAddress;
    const userAgent = req.headers['user-agent'];

    const requestId = await this.gdprService.requestDataDeletion({
      userId,
      ...deletionDto,
      ipAddress,
      userAgent,
    });

    return {
      message: 'Data deletion request created successfully',
      requestId,
    };
  }

  /**
   * Get data deletion status
   */
  @Get('deletion/:requestId')
  @UseGuards(JwtGuard)
  @ApiOperation({ summary: 'Get data deletion request status' })
  @ApiResponse({
    status: 200,
    description: 'Deletion status retrieved successfully',
  })
  @ApiParam({ name: 'requestId', description: 'Data deletion request ID' })
  async getDataDeletionStatus(
    @Param('requestId') requestId: string,
    @Request() req: any,
  ) {
    const userId = req.user?.id;
    return this.gdprService.getDataDeletionStatus(requestId, userId);
  }

  /**
   * Get user's export requests
   */
  @Get('my-exports')
  @UseGuards(JwtGuard)
  @ApiOperation({ summary: "Get current user's data export requests" })
  @ApiResponse({
    status: 200,
    description: 'Export requests retrieved successfully',
  })
  async getMyExportRequests(@Request() req: any) {
    const userId = req.user?.id;

    // This would need to be implemented in the GDPR service
    // For now, return a placeholder
    return { message: 'Feature coming soon' };
  }

  /**
   * Get user's deletion requests
   */
  @Get('my-deletions')
  @UseGuards(JwtGuard)
  @ApiOperation({ summary: "Get current user's data deletion requests" })
  @ApiResponse({
    status: 200,
    description: 'Deletion requests retrieved successfully',
  })
  async getMyDeletionRequests(@Request() req: any) {
    const userId = req.user?.id;

    // This would need to be implemented in the GDPR service
    // For now, return a placeholder
    return { message: 'Feature coming soon' };
  }

  /**
   * Get GDPR compliance report (Admin/Agent access)
   */
  @Get('compliance-report')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({ summary: 'Get GDPR compliance report' })
  @ApiResponse({
    status: 200,
    description: 'Compliance report retrieved successfully',
  })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  async getComplianceReport(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.gdprService.getComplianceReport({
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
    });
  }

  /**
   * Get all consent records (Admin access only)
   */
  @Get('admin/consents')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({ summary: 'Get all consent records (Admin access)' })
  @ApiResponse({
    status: 200,
    description: 'All consent records retrieved successfully',
  })
  @ApiQuery({ name: 'userId', required: false, type: String })
  @ApiQuery({ name: 'consentType', required: false, enum: ConsentType })
  @ApiQuery({ name: 'granted', required: false, type: Boolean })
  async getAllConsents(
    @Query('userId') userId?: string,
    @Query('consentType') consentType?: ConsentType,
    @Query('granted') granted?: boolean,
  ) {
    // This would need to be implemented in the GDPR service
    // For now, return a placeholder
    return { message: 'Feature coming soon' };
  }

  /**
   * Get all export requests (Admin access only)
   */
  @Get('admin/exports')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({ summary: 'Get all data export requests (Admin access)' })
  @ApiResponse({
    status: 200,
    description: 'All export requests retrieved successfully',
  })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiQuery({ name: 'requestType', required: false, enum: DataExportType })
  async getAllExportRequests(
    @Query('status') status?: string,
    @Query('requestType') requestType?: DataExportType,
  ) {
    // This would need to be implemented in the GDPR service
    // For now, return a placeholder
    return { message: 'Feature coming soon' };
  }

  /**
   * Get all deletion requests (Admin access only)
   */
  @Get('admin/deletions')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({ summary: 'Get all data deletion requests (Admin access)' })
  @ApiResponse({
    status: 200,
    description: 'All deletion requests retrieved successfully',
  })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiQuery({ name: 'requestType', required: false, enum: DataDeletionType })
  async getAllDeletionRequests(
    @Query('status') status?: string,
    @Query('requestType') requestType?: DataDeletionType,
  ) {
    // This would need to be implemented in the GDPR service
    // For now, return a placeholder
    return { message: 'Feature coming soon' };
  }

  /**
   * Manually process export request (Admin access only)
   */
  @Post('admin/exports/:requestId/process')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({ summary: 'Manually process export request (Admin access)' })
  @ApiResponse({
    status: 200,
    description: 'Export request processed successfully',
  })
  @ApiParam({ name: 'requestId', description: 'Export request ID' })
  @HttpCode(HttpStatus.OK)
  async processExportRequest(@Param('requestId') requestId: string) {
    // This would trigger manual processing of the export request
    // For now, return a placeholder
    return { message: 'Export request processing initiated' };
  }

  /**
   * Manually process deletion request (Admin access only)
   */
  @Post('admin/deletions/:requestId/process')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({ summary: 'Manually process deletion request (Admin access)' })
  @ApiResponse({
    status: 200,
    description: 'Deletion request processed successfully',
  })
  @ApiParam({ name: 'requestId', description: 'Deletion request ID' })
  @HttpCode(HttpStatus.OK)
  async processDeletionRequest(@Param('requestId') requestId: string) {
    // This would trigger manual processing of the deletion request
    // For now, return a placeholder
    return { message: 'Deletion request processing initiated' };
  }
}
