import { Modu<PERSON> } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ReminderController } from './reminder.controller';
import { ReminderService } from './reminder.service';
import { PrismaService } from '../utils/prisma.service';
import { MailerService } from '../mailer/mailer.service';

/**
 * Reminder Module
 * Handles all reminder-related functionality including CRUD operations,
 * overdue tracking, and scheduling
 */
@Module({
  controllers: [ReminderController],
  providers: [ReminderService, PrismaService, MailerService, JwtService],
  exports: [ReminderService],
})
export class ReminderModule {}
