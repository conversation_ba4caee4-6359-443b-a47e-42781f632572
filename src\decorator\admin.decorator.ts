import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { IJWTPayload } from 'src/types';

/**
 * Decorator to extract the admin user from the request
 * Returns the admin user object from the JWT token
 */
export const GetAdmin = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): IJWTPayload => {
    const request = ctx.switchToHttp().getRequest();
    return request.user;
  },
);
