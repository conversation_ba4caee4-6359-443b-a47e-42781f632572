/**
 * Jest setup file for configuring the test environment
 *
 * This file is executed before each test file runs and sets up
 * environment variables and global mocks to prevent tests from
 * hitting production services.
 */

// Set environment variables for testing
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
process.env.SUPABASE_URL = 'https://mock-supabase-url.com';
process.env.SUPABASE_KEY = 'mock-supabase-key';
process.env.jwtSecretKey = 'test-secret-key-for-e2e-testing';
process.env.jwtAdminSecretKey = 'test-secret-key-for-e2e-testing';
process.env.jwtAgentSecretKey = 'test-secret-key-for-e2e-testing';
process.env.jwtMentorSecretKey = 'test-secret-key-for-e2e-testing';
process.env.jwtRefreshTokenKey = 'test-secret-key-for-e2e-testing';
process.env.jwtOtpSecretKey = 'test-secret-key-for-e2e-testing';

// Mock Supabase client
jest.mock('@supabase/supabase-js', () => {
  return {
    createClient: jest.fn(() => ({
      storage: {
        from: jest.fn(() => ({
          upload: jest.fn(() => ({
            data: {
              Key: 'mock-file-key',
            },
            error: null,
          })),
          getPublicUrl: jest.fn(() => ({
            data: {
              publicUrl: 'https://mock-supabase-storage.com/mock-file.pdf',
            },
          })),
          remove: jest.fn(() => ({
            data: {},
            error: null,
          })),
        })),
      },
    })),
  };
});

// Global teardown
afterAll(() => {
  jest.clearAllMocks();
});
