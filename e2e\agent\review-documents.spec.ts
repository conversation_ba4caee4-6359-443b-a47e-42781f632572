import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { PrismaService } from '../../src/utils/prisma.service';
import { MockPrismaService } from '../../src/utils/prisma.service.mock';
import { MinimalTestAppModule } from '../../test/minimal-test-app.module';
import { getTestCredentials, AuthTestUtils } from '../utils/auth-helpers';
import {
  DOCUMENT_FIXTURES,
  DOCUMENT_REVIEW_SCENARIOS,
  mockPrismaDocument,
  generateDocumentList,
} from '../utils/fixtures/documents';
import { testResultCollector, testResultReporter } from '../utils/test-results';

/**
 * E2E Test Suite: Agent Document Review
 *
 * Tests the complete workflow of document review by agent users,
 * including document approval, rejection, and audit trail management.
 *
 * Scenarios covered:
 * 1. Approve valid documents with review notes
 * 2. Reject documents with detailed reasons
 * 3. Handle bulk document reviews
 * 4. Track review audit trail and timestamps
 * 5. Validate agent authorization for reviews
 * 6. Handle edge cases (expired documents, missing files)
 */
describe('Agent Document Review (E2E)', () => {
  let app: INestApplication;
  let prismaService: MockPrismaService;

  const agentCredentials = getTestCredentials('agent');
  const adminCredentials = getTestCredentials('admin');
  const userCredentials = getTestCredentials('user');

  beforeAll(async () => {
    testResultCollector.clear();

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [MinimalTestAppModule],
    })
      .overrideProvider(PrismaService)
      .useClass(MockPrismaService)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      }),
    );

    prismaService = moduleFixture.get<MockPrismaService>(PrismaService);
    await app.init();
  });

  afterAll(async () => {
    const suiteResults = testResultCollector.getSuiteResults(
      'Agent Document Review',
    );
    const summary = testResultReporter.generateSummary([suiteResults]);
    testResultReporter.saveResults(summary);
    testResultReporter.saveLogs(summary);
    testResultReporter.generateHtmlReport(summary);

    await app.close();
  });

  beforeEach(() => {
    prismaService.clearTestData();
    jest.clearAllMocks();
  });

  describe('Document Review Queue', () => {
    it('should retrieve pending documents for review', async () => {
      testResultCollector.startTest(
        'Agent Document Review',
        'Get pending documents',
      );

      const pendingDocuments = [
        mockPrismaDocument(DOCUMENT_FIXTURES.pendingPassport),
        mockPrismaDocument(DOCUMENT_FIXTURES.largeDocument),
      ];

      prismaService.document.findMany.mockResolvedValue(pendingDocuments);
      prismaService.document.count.mockResolvedValue(pendingDocuments.length);

      const startTime = Date.now();
      const response = await request(app.getHttpServer())
        .get('/document/pending-review')
        .set('Authorization', agentCredentials.bearerToken);

      const duration = Date.now() - startTime;

      testResultCollector.logHttpRequest({
        method: 'GET',
        url: '/document/pending-review',
        statusCode: response.status,
        requestBody: null,
        responseBody: response.body,
        duration,
        timestamp: new Date(),
      });

      if (response.status === 200) {
        expect(response.body.data).toHaveLength(2);
        response.body.data.forEach((doc: any) => {
          expect(doc.status).toBe('PENDING');
        });
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 200, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should filter documents by user', async () => {
      testResultCollector.startTest(
        'Agent Document Review',
        'Filter documents by user',
      );

      const userDocuments = generateDocumentList(3, 'user-test-id-1');
      const mockDocuments = userDocuments.map((doc) => mockPrismaDocument(doc));

      prismaService.document.findMany.mockResolvedValue(mockDocuments);
      prismaService.document.count.mockResolvedValue(mockDocuments.length);

      const response = await request(app.getHttpServer())
        .get('/document/pending-review')
        .set('Authorization', agentCredentials.bearerToken)
        .query({ userId: 'user-test-id-1' });

      if (response.status === 200) {
        response.body.data.forEach((doc: any) => {
          expect(doc.userId).toBe('user-test-id-1');
        });
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 200, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should sort documents by upload date', async () => {
      testResultCollector.startTest(
        'Agent Document Review',
        'Sort by upload date',
      );

      const documents = generateDocumentList(5).map((doc) =>
        mockPrismaDocument(doc),
      );
      // Sort by creation date descending (newest first)
      documents.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      prismaService.document.findMany.mockResolvedValue(documents);
      prismaService.document.count.mockResolvedValue(documents.length);

      const response = await request(app.getHttpServer())
        .get('/document/pending-review')
        .set('Authorization', agentCredentials.bearerToken)
        .query({ sortBy: 'createdAt', sortOrder: 'desc' });

      if (response.status === 200) {
        const dates = response.body.data.map((doc: any) =>
          new Date(doc.createdAt).getTime(),
        );
        for (let i = 1; i < dates.length; i++) {
          expect(dates[i]).toBeLessThanOrEqual(dates[i - 1]);
        }
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 200, got ${response.status}`,
          response.body,
        );
      }
    });
  });

  describe('Document Approval', () => {
    it('should approve a valid document', async () => {
      testResultCollector.startTest(
        'Agent Document Review',
        'Approve valid document',
      );

      const document = DOCUMENT_FIXTURES.pendingPassport;
      const reviewData = DOCUMENT_REVIEW_SCENARIOS.approve;

      const approvedDocument = {
        ...document,
        status: 'APPROVED' as const,
        reviewedBy: agentCredentials.user.id,
        reviewedAt: new Date(),
      };

      prismaService.document.findUnique.mockResolvedValue(
        mockPrismaDocument(document),
      );
      prismaService.document.update.mockResolvedValue(
        mockPrismaDocument(approvedDocument),
      );

      const response = await request(app.getHttpServer())
        .patch(`/document/${document.id}/review`)
        .set('Authorization', agentCredentials.bearerToken)
        .send(reviewData);

      if (response.status === 200) {
        expect(response.body.status).toBe('APPROVED');
        expect(response.body.reviewedBy).toBe(agentCredentials.user.id);
        expect(response.body.reviewedAt).toBeDefined();
        expect(prismaService.document.update).toHaveBeenCalledWith({
          where: { id: document.id },
          data: expect.objectContaining({
            status: 'APPROVED',
            reviewedBy: agentCredentials.user.id,
            reviewedAt: expect.any(Date),
          }),
          include: expect.any(Object),
        });
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 200, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should approve document with review notes', async () => {
      testResultCollector.startTest(
        'Agent Document Review',
        'Approve with notes',
      );

      const document = DOCUMENT_FIXTURES.largeDocument;
      const reviewData = {
        status: 'APPROVED',
        reviewNotes:
          'Document is valid but large file size noted for future reference',
      };

      prismaService.document.findUnique.mockResolvedValue(
        mockPrismaDocument(document),
      );
      prismaService.document.update.mockResolvedValue(
        mockPrismaDocument({
          ...document,
          status: 'APPROVED',
          reviewedBy: agentCredentials.user.id,
          reviewedAt: new Date(),
        }),
      );

      const response = await request(app.getHttpServer())
        .patch(`/document/${document.id}/review`)
        .set('Authorization', agentCredentials.bearerToken)
        .send(reviewData);

      if (response.status === 200) {
        expect(response.body.status).toBe('APPROVED');
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 200, got ${response.status}`,
          response.body,
        );
      }
    });
  });

  describe('Document Rejection', () => {
    it('should reject document with reason', async () => {
      testResultCollector.startTest(
        'Agent Document Review',
        'Reject with reason',
      );

      const document = DOCUMENT_FIXTURES.pendingPassport;
      const reviewData = DOCUMENT_REVIEW_SCENARIOS.reject;

      const rejectedDocument = {
        ...document,
        status: 'REJECTED' as const,
        reviewedBy: agentCredentials.user.id,
        reviewedAt: new Date(),
        rejectionReason: reviewData.rejectionReason,
      };

      prismaService.document.findUnique.mockResolvedValue(
        mockPrismaDocument(document),
      );
      prismaService.document.update.mockResolvedValue(
        mockPrismaDocument(rejectedDocument),
      );

      const response = await request(app.getHttpServer())
        .patch(`/document/${document.id}/review`)
        .set('Authorization', agentCredentials.bearerToken)
        .send(reviewData);

      if (response.status === 200) {
        expect(response.body.status).toBe('REJECTED');
        expect(response.body.rejectionReason).toBe(reviewData.rejectionReason);
        expect(response.body.reviewedBy).toBe(agentCredentials.user.id);
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 200, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should reject expired document', async () => {
      testResultCollector.startTest(
        'Agent Document Review',
        'Reject expired document',
      );

      const document = DOCUMENT_FIXTURES.expiredDocument;
      const reviewData = DOCUMENT_REVIEW_SCENARIOS.rejectExpired;

      prismaService.document.findUnique.mockResolvedValue(
        mockPrismaDocument(document),
      );
      prismaService.document.update.mockResolvedValue(
        mockPrismaDocument({
          ...document,
          status: 'REJECTED',
          reviewedBy: agentCredentials.user.id,
          reviewedAt: new Date(),
          rejectionReason: reviewData.rejectionReason,
        }),
      );

      const response = await request(app.getHttpServer())
        .patch(`/document/${document.id}/review`)
        .set('Authorization', agentCredentials.bearerToken)
        .send(reviewData);

      if (response.status === 200) {
        expect(response.body.status).toBe('REJECTED');
        expect(response.body.rejectionReason).toContain('expired');
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 200, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should require rejection reason for rejected documents', async () => {
      testResultCollector.startTest(
        'Agent Document Review',
        'Require rejection reason',
      );

      const document = DOCUMENT_FIXTURES.pendingPassport;
      prismaService.document.findUnique.mockResolvedValue(
        mockPrismaDocument(document),
      );

      const response = await request(app.getHttpServer())
        .patch(`/document/${document.id}/review`)
        .set('Authorization', agentCredentials.bearerToken)
        .send({
          status: 'REJECTED',
          // Missing rejectionReason
        });

      if (response.status === 400) {
        expect(response.body.message).toContain('rejection');
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 400, got ${response.status}`,
          response.body,
        );
      }
    });
  });

  describe('Review Validation', () => {
    it('should handle review of non-existent document', async () => {
      testResultCollector.startTest(
        'Agent Document Review',
        'Handle non-existent document',
      );

      prismaService.document.findUnique.mockResolvedValue(null);

      const response = await request(app.getHttpServer())
        .patch('/document/non-existent-id/review')
        .set('Authorization', agentCredentials.bearerToken)
        .send(DOCUMENT_REVIEW_SCENARIOS.approve);

      if (response.status === 404) {
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 404, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should prevent review of already reviewed document', async () => {
      testResultCollector.startTest(
        'Agent Document Review',
        'Prevent re-review',
      );

      const alreadyReviewed = DOCUMENT_FIXTURES.approvedCV;
      prismaService.document.findUnique.mockResolvedValue(
        mockPrismaDocument(alreadyReviewed),
      );

      const response = await request(app.getHttpServer())
        .patch(`/document/${alreadyReviewed.id}/review`)
        .set('Authorization', agentCredentials.bearerToken)
        .send(DOCUMENT_REVIEW_SCENARIOS.approve);

      if (response.status === 400) {
        expect(response.body.message).toContain('already reviewed');
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 400, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should validate review status values', async () => {
      testResultCollector.startTest(
        'Agent Document Review',
        'Validate status values',
      );

      const document = DOCUMENT_FIXTURES.pendingPassport;
      prismaService.document.findUnique.mockResolvedValue(
        mockPrismaDocument(document),
      );

      const response = await request(app.getHttpServer())
        .patch(`/document/${document.id}/review`)
        .set('Authorization', agentCredentials.bearerToken)
        .send({
          status: 'INVALID_STATUS',
          reviewNotes: 'Test notes',
        });

      if (response.status === 400) {
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 400, got ${response.status}`,
          response.body,
        );
      }
    });
  });

  describe('Authorization Tests', () => {
    it('should reject review without authentication', async () => {
      testResultCollector.startTest(
        'Agent Document Review',
        'Reject unauthenticated review',
      );

      await AuthTestUtils.expectUnauthorized(
        request(app.getHttpServer())
          .patch('/document/doc-1/review')
          .send(DOCUMENT_REVIEW_SCENARIOS.approve),
      );
      testResultCollector.endTest('PASS');
    });

    it('should reject review by regular user', async () => {
      testResultCollector.startTest(
        'Agent Document Review',
        'Reject user review',
      );

      await AuthTestUtils.expectForbidden(
        request(app.getHttpServer())
          .patch('/document/doc-1/review')
          .set('Authorization', userCredentials.bearerToken)
          .send(DOCUMENT_REVIEW_SCENARIOS.approve),
      );
      testResultCollector.endTest('PASS');
    });

    it('should allow review by admin', async () => {
      testResultCollector.startTest(
        'Agent Document Review',
        'Allow admin review',
      );

      const document = DOCUMENT_FIXTURES.pendingPassport;
      prismaService.document.findUnique.mockResolvedValue(
        mockPrismaDocument(document),
      );
      prismaService.document.update.mockResolvedValue(
        mockPrismaDocument({
          ...document,
          status: 'APPROVED',
          reviewedBy: adminCredentials.user.id,
          reviewedAt: new Date(),
        }),
      );

      const response = await request(app.getHttpServer())
        .patch(`/document/${document.id}/review`)
        .set('Authorization', adminCredentials.bearerToken)
        .send(DOCUMENT_REVIEW_SCENARIOS.approve);

      if (response.status === 200) {
        expect(response.body.status).toBe('APPROVED');
        expect(response.body.reviewedBy).toBe(adminCredentials.user.id);
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 200, got ${response.status}`,
          response.body,
        );
      }
    });
  });

  describe('Bulk Review Operations', () => {
    it('should handle multiple document reviews', async () => {
      testResultCollector.startTest(
        'Agent Document Review',
        'Bulk document reviews',
      );

      const documents = [
        DOCUMENT_FIXTURES.pendingPassport,
        DOCUMENT_FIXTURES.largeDocument,
      ];

      // Mock individual document lookups and updates
      documents.forEach((doc, index) => {
        prismaService.document.findUnique.mockResolvedValueOnce(
          mockPrismaDocument(doc),
        );

        prismaService.document.update.mockResolvedValueOnce(
          mockPrismaDocument({
            ...doc,
            status: 'APPROVED',
            reviewedBy: agentCredentials.user.id,
            reviewedAt: new Date(),
          }),
        );
      });

      // Test reviewing multiple documents sequentially
      let allSuccessful = true;
      for (const doc of documents) {
        const response = await request(app.getHttpServer())
          .patch(`/document/${doc.id}/review`)
          .set('Authorization', agentCredentials.bearerToken)
          .send(DOCUMENT_REVIEW_SCENARIOS.approve);

        if (response.status !== 200) {
          allSuccessful = false;
          break;
        }
      }

      if (allSuccessful) {
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest('FAIL', 'One or more bulk reviews failed');
      }
    });
  });
});
