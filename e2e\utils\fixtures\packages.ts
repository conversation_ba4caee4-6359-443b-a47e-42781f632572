/**
 * Package fixtures for E2E testing
 *
 * Provides mock data for package-related tests including different types
 * of visa/work permit packages with varying prices and services.
 */

export interface TestPackage {
  id: string;
  name: string;
  note: string;
  amount: number;
  order: number;
  service: string[];
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Standard package fixtures for testing
 */
export const PACKAGE_FIXTURES: Record<string, TestPackage> = {
  criticalSkills: {
    id: 'pkg-critical-skills-1',
    name: 'Critical Skills Employment Permit',
    note: 'Fast-track employment permit for skilled professionals in high-demand sectors',
    amount: 1500,
    order: 1,
    service: [
      'Document review and verification',
      'Application preparation and submission',
      'Priority processing',
      'Status tracking and updates',
      'Interview preparation (if required)',
    ],
  },

  generalEmployment: {
    id: 'pkg-general-employment-1',
    name: 'General Employment Permit',
    note: 'Standard employment permit for various job categories',
    amount: 1200,
    order: 2,
    service: [
      'Document review and verification',
      'Application preparation and submission',
      'Standard processing',
      'Status tracking and updates',
    ],
  },

  dependentVisa: {
    id: 'pkg-dependent-visa-1',
    name: 'Dependent/Partner Visa',
    note: 'Visa for family members of permit holders',
    amount: 800,
    order: 3,
    service: [
      'Relationship documentation review',
      'Application preparation',
      'Supporting document compilation',
      'Status tracking',
    ],
  },

  studentVisa: {
    id: 'pkg-student-visa-1',
    name: 'Student Visa Package',
    note: 'Comprehensive student visa application support',
    amount: 1000,
    order: 4,
    service: [
      'Educational document verification',
      'Financial documentation review',
      'Application preparation and submission',
      'Interview preparation',
      'Status tracking',
    ],
  },

  businessVisa: {
    id: 'pkg-business-visa-1',
    name: 'Business/Investment Visa',
    note: 'For entrepreneurs and investors',
    amount: 2000,
    order: 5,
    service: [
      'Business plan review',
      'Financial documentation verification',
      'Investment proof compilation',
      'Application preparation and submission',
      'Priority processing',
      'Legal consultation',
    ],
  },
};

/**
 * Package creation DTOs for testing
 */
export const PACKAGE_CREATE_DTOS = {
  valid: {
    name: 'Test Package',
    note: 'Test package description',
    amount: 1500,
    order: 1,
    service: ['Service 1', 'Service 2', 'Service 3'],
  },

  minimal: {
    name: 'Minimal Package',
    note: 'Minimal package',
    amount: 500,
    order: 1,
    service: ['Basic service'],
  },

  premium: {
    name: 'Premium Package',
    note: 'Premium package with all services',
    amount: 3000,
    order: 1,
    service: [
      'Premium document review',
      'Priority processing',
      'Dedicated case manager',
      'Legal consultation',
      'Interview preparation',
      'Post-approval support',
    ],
  },
};

/**
 * Invalid package data for validation testing
 */
export const INVALID_PACKAGE_DATA = {
  missingName: {
    note: 'Package without name',
    amount: 1000,
    order: 1,
    service: ['Service 1'],
  },

  missingAmount: {
    name: 'Package without amount',
    note: 'Package description',
    order: 1,
    service: ['Service 1'],
  },

  negativeAmount: {
    name: 'Negative Amount Package',
    note: 'Package with negative amount',
    amount: -100,
    order: 1,
    service: ['Service 1'],
  },

  emptyServices: {
    name: 'Empty Services Package',
    note: 'Package with no services',
    amount: 1000,
    order: 1,
    service: [],
  },

  invalidServiceType: {
    name: 'Invalid Service Type',
    note: 'Package with invalid service type',
    amount: 1000,
    order: 1,
    service: 'Not an array', // Should be array
  },
};

/**
 * Package update scenarios
 */
export const PACKAGE_UPDATE_SCENARIOS = {
  priceIncrease: {
    amount: 1800,
  },

  priceDecrease: {
    amount: 1200,
  },

  addService: {
    service: [
      'Document review and verification',
      'Application preparation and submission',
      'Priority processing',
      'Status tracking and updates',
      'Interview preparation (if required)',
      'Post-approval support', // New service
    ],
  },

  removeService: {
    service: [
      'Document review and verification',
      'Application preparation and submission',
      'Status tracking and updates',
    ],
  },

  updateDescription: {
    note: 'Updated package description with new benefits and features',
  },

  reorder: {
    order: 10,
  },
};

/**
 * Mock Prisma package data
 */
export function mockPrismaPackage(packageData: TestPackage) {
  return {
    id: packageData.id,
    name: packageData.name,
    note: packageData.note,
    amount: packageData.amount,
    order: packageData.order,
    service: packageData.service,
    createdAt: packageData.createdAt || new Date(),
    updatedAt: packageData.updatedAt || new Date(),
  };
}

/**
 * Generate multiple packages for pagination testing
 */
export function generatePackageList(count: number): TestPackage[] {
  const packages: TestPackage[] = [];

  for (let i = 1; i <= count; i++) {
    packages.push({
      id: `pkg-test-${i}`,
      name: `Test Package ${i}`,
      note: `Description for test package ${i}`,
      amount: 1000 + i * 100,
      order: i,
      service: [`Service ${i}A`, `Service ${i}B`],
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }

  return packages;
}

/**
 * Package search and filter test data
 */
export const PACKAGE_FILTER_SCENARIOS = {
  byPriceRange: {
    minAmount: 1000,
    maxAmount: 1500,
  },

  byName: {
    searchTerm: 'Critical Skills',
  },

  byService: {
    serviceFilter: 'Priority processing',
  },

  sortByPrice: {
    sortBy: 'amount',
    sortOrder: 'asc',
  },

  sortByName: {
    sortBy: 'name',
    sortOrder: 'desc',
  },
};

/**
 * Payment-related test data
 */
export const PAYMENT_SCENARIOS = {
  validPayment: {
    packageId: 'pkg-critical-skills-1',
    paymentMethod: 'stripe',
    currency: 'eur',
  },

  guestPayment: {
    packageId: 'pkg-general-employment-1',
    name: 'John Doe',
    email: '<EMAIL>',
    mobile_no: '+353123456789',
  },

  invalidPackage: {
    packageId: 'invalid-package-id',
  },
};

/**
 * Mock Stripe session response
 */
export const MOCK_STRIPE_SESSION = {
  id: 'cs_test_123456789',
  url: 'https://checkout.stripe.com/pay/cs_test_123456789',
  payment_status: 'unpaid',
  status: 'open',
};
