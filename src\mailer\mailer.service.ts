import { Injectable, Logger } from '@nestjs/common';
import { render } from '@react-email/render';
import { Resend } from 'resend';
import {
  ResendEmailDto,
  NotificationEmailDto,
  ReminderEmailDto,
  OverdueReminderDigestDto,
} from './dto/mailer.dto';

// Import email templates
import NotificationEmail from '../template/notification';
import ReminderEmail from '../template/reminder';
import OverdueReminderEmail from '../template/overdue-reminder';

/**
 * Enhanced MailerService with support for notification and reminder emails
 * Integrates with React Email templates and Resend API
 */
@Injectable()
export class MailerService {
  private readonly logger = new Logger(MailerService.name);
  private readonly resend: Resend;
  private readonly defaultFrom: string;

  constructor() {
    this.resend = new Resend(process.env.EMAIL_API_KEY);
    this.defaultFrom =
      process.env.DEFAULT_FROM_EMAIL || '<EMAIL>';
  }

  /**
   * Send a generic email using the existing method
   * @param dto - Email data transfer object
   * @returns Resend API response
   */
  async sendEmail(dto: ResendEmailDto) {
    try {
      const { from, to, subject, html } = dto;
      this.logger.log(`Sending email to ${to} with subject: ${subject}`);

      const { data, error } = await this.resend.emails.send({
        from: from,
        to: [to],
        subject: subject,
        html: html,
      });

      if (error) {
        this.logger.error(`Failed to send email to ${to}:`, error);
        throw new Error(`Email sending failed: ${error.message}`);
      }

      this.logger.log(`Email sent successfully to ${to}, ID: ${data?.id}`);
      return data;
    } catch (error) {
      this.logger.error(`Email service error:`, error);
      throw error;
    }
  }

  /**
   * Send a notification email using the notification template
   * @param dto - Notification email data
   * @returns Resend API response
   */
  async sendNotificationEmail(dto: NotificationEmailDto) {
    try {
      this.logger.log(
        `Sending notification email to ${dto.to} - Type: ${dto.notificationType}`,
      );

      // Render the React email template
      const html = await render(
        NotificationEmail({
          userName: dto.userName,
          message: dto.message,
          notificationType: dto.notificationType,
          actionUrl: dto.actionUrl,
          actionText: dto.actionText,
        }),
      );

      // Generate subject based on notification type
      const subject = this.generateNotificationSubject(
        dto.notificationType,
        dto.message,
      );

      const { data, error } = await this.resend.emails.send({
        from: this.defaultFrom,
        to: [dto.to],
        subject: subject,
        html: html,
      });

      if (error) {
        this.logger.error(
          `Failed to send notification email to ${dto.to}:`,
          error,
        );
        throw new Error(`Notification email sending failed: ${error.message}`);
      }

      this.logger.log(
        `Notification email sent successfully to ${dto.to}, ID: ${data?.id}`,
      );
      return data;
    } catch (error) {
      this.logger.error(`Notification email service error:`, error);
      throw error;
    }
  }

  /**
   * Send a reminder email using the reminder template
   * @param dto - Reminder email data
   * @returns Resend API response
   */
  async sendReminderEmail(dto: ReminderEmailDto) {
    try {
      this.logger.log(
        `Sending reminder email to ${dto.to} - Due: ${dto.dueDate}`,
      );

      // Render the React email template
      const html = await render(
        ReminderEmail({
          userName: dto.userName,
          message: dto.message,
          dueDate: dto.dueDate,
          isOverdue: dto.isOverdue,
          applicationName: dto.applicationName,
          documentName: dto.documentName,
          actionUrl: dto.actionUrl,
          actionText: dto.actionText,
        }),
      );

      // Generate subject based on overdue status
      const subject = dto.isOverdue
        ? `🚨 Overdue: ${dto.message}`
        : `⏰ Reminder: ${dto.message}`;

      const { data, error } = await this.resend.emails.send({
        from: this.defaultFrom,
        to: [dto.to],
        subject: subject,
        html: html,
      });

      if (error) {
        this.logger.error(`Failed to send reminder email to ${dto.to}:`, error);
        throw new Error(`Reminder email sending failed: ${error.message}`);
      }

      this.logger.log(
        `Reminder email sent successfully to ${dto.to}, ID: ${data?.id}`,
      );
      return data;
    } catch (error) {
      this.logger.error(`Reminder email service error:`, error);
      throw error;
    }
  }

  /**
   * Send an overdue reminder digest email
   * @param dto - Overdue reminder digest data
   * @returns Resend API response
   */
  async sendOverdueReminderDigest(dto: OverdueReminderDigestDto) {
    try {
      const overdueCount = dto.overdueReminders.length;
      this.logger.log(
        `Sending overdue reminder digest to ${dto.to} - ${overdueCount} overdue items`,
      );

      // Render the React email template
      const html = await render(
        OverdueReminderEmail({
          userName: dto.userName,
          overdueReminders: dto.overdueReminders,
          dashboardUrl: dto.dashboardUrl,
        }),
      );

      const subject = `🚨 Urgent: ${overdueCount} Overdue Reminder${overdueCount > 1 ? 's' : ''} - Action Required`;

      const { data, error } = await this.resend.emails.send({
        from: this.defaultFrom,
        to: [dto.to],
        subject: subject,
        html: html,
      });

      if (error) {
        this.logger.error(
          `Failed to send overdue digest email to ${dto.to}:`,
          error,
        );
        throw new Error(
          `Overdue digest email sending failed: ${error.message}`,
        );
      }

      this.logger.log(
        `Overdue digest email sent successfully to ${dto.to}, ID: ${data?.id}`,
      );
      return data;
    } catch (error) {
      this.logger.error(`Overdue digest email service error:`, error);
      throw error;
    }
  }

  /**
   * Generate appropriate subject line for notification emails
   * @param type - Notification type
   * @param message - Notification message
   * @returns Generated subject line
   */
  private generateNotificationSubject(type: string, message: string): string {
    const prefix =
      {
        SUCCESS: '✅',
        WARNING: '⚠️',
        ERROR: '❌',
        INFO: 'ℹ️',
      }[type] || 'ℹ️';

    // Truncate message if too long for subject
    const truncatedMessage =
      message.length > 50 ? message.substring(0, 50) + '...' : message;

    return `${prefix} ${truncatedMessage}`;
  }

  /**
   * Send bulk emails (for batch processing)
   * @param emails - Array of email DTOs
   * @returns Array of results
   */
  async sendBulkEmails(emails: ResendEmailDto[]): Promise<any[]> {
    this.logger.log(`Sending ${emails.length} bulk emails`);

    const results = await Promise.allSettled(
      emails.map((email) => this.sendEmail(email)),
    );

    const successful = results.filter(
      (result) => result.status === 'fulfilled',
    ).length;
    const failed = results.length - successful;

    this.logger.log(
      `Bulk email results: ${successful} successful, ${failed} failed`,
    );

    return results;
  }
}
