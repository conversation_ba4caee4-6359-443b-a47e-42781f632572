import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON>ler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { AuditService } from './audit.service';
import { SecurityMonitoringService } from './security-monitoring.service';
import { Reflector } from '@nestjs/core';

/**
 * Audit Logging Interceptor
 *
 * Automatically logs all HTTP requests and responses for audit trail purposes.
 * Integrates with security monitoring to detect suspicious activities.
 *
 * Features:
 * - Automatic request/response logging
 * - User action tracking
 * - Security event detection
 * - Performance monitoring
 * - Error tracking
 */
@Injectable()
export class AuditInterceptor implements NestInterceptor {
  private readonly logger = new Logger(AuditInterceptor.name);

  constructor(
    private readonly auditService: AuditService,
    private readonly securityMonitoring: SecurityMonitoringService,
    private readonly reflector: Reflector,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const startTime = Date.now();

    // Extract request information
    const { method, url, body, query, params, headers, user, userRole } =
      request;

    const ipAddress = this.getClientIP(request);
    const userAgent = headers['user-agent'];
    const sessionId = headers['x-session-id'] || request.sessionID;
    const requestId = headers['x-request-id'] || this.generateRequestId();

    // Determine action and entity from the request
    const { action, entityType, entityId } = this.extractActionInfo(
      method,
      url,
      body,
      params,
    );

    // Skip audit logging for certain endpoints (health checks, etc.)
    if (this.shouldSkipAudit(url)) {
      return next.handle();
    }

    return next.handle().pipe(
      tap(async (responseData) => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        try {
          // Log successful operation
          await this.auditService.logAction({
            action,
            entityType,
            entityId,
            userId: user?.id,
            userRole: userRole || user?.role,
            ipAddress,
            userAgent,
            newValues: this.sanitizeData(responseData),
            metadata: {
              method,
              url,
              query: this.sanitizeData(query),
              params: this.sanitizeData(params),
              statusCode: response.statusCode,
              duration,
              requestSize: JSON.stringify(body || {}).length,
              responseSize: JSON.stringify(responseData || {}).length,
            },
            sessionId,
            requestId,
            success: true,
          });

          // Monitor for suspicious activities
          if (user?.id) {
            await this.securityMonitoring.monitorSuspiciousActivity({
              userId: user.id,
              action,
              ipAddress,
              userAgent,
              metadata: {
                method,
                url,
                duration,
                statusCode: response.statusCode,
              },
            });
          }

          // Monitor API usage patterns
          await this.monitorAPIUsage(ipAddress, url, user?.id);
        } catch (error) {
          this.logger.error(`Failed to log audit trail: ${error.message}`);
        }
      }),
      catchError(async (error) => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        try {
          // Log failed operation
          await this.auditService.logAction({
            action,
            entityType,
            entityId,
            userId: user?.id,
            userRole: userRole || user?.role,
            ipAddress,
            userAgent,
            metadata: {
              method,
              url,
              query: this.sanitizeData(query),
              params: this.sanitizeData(params),
              duration,
              requestSize: JSON.stringify(body || {}).length,
            },
            sessionId,
            requestId,
            success: false,
            errorMessage: error.message,
          });

          // Log security event for certain types of errors
          if (this.isSecurityRelevantError(error)) {
            await this.securityMonitoring.createSecurityEvent({
              eventType: this.getSecurityEventType(error, action),
              severity: this.getErrorSeverity(error),
              title: `Security-relevant error: ${error.message}`,
              description: `${method} ${url} failed with error: ${error.message}`,
              userId: user?.id,
              ipAddress,
              userAgent,
              metadata: {
                method,
                url,
                action,
                entityType,
                errorCode: error.status || error.statusCode,
                errorMessage: error.message,
              },
            });
          }
        } catch (auditError) {
          this.logger.error(
            `Failed to log audit trail for error: ${auditError.message}`,
          );
        }

        throw error;
      }),
    );
  }

  /**
   * Extract client IP address from request
   */
  private getClientIP(request: any): string {
    return (
      request.headers['x-forwarded-for']?.split(',')[0] ||
      request.headers['x-real-ip'] ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      request.ip ||
      'unknown'
    );
  }

  /**
   * Generate a unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Extract action and entity information from request
   */
  private extractActionInfo(
    method: string,
    url: string,
    body: any,
    params: any,
  ): { action: string; entityType: string; entityId?: string } {
    // Parse URL to extract entity type and action
    const urlParts = url
      .split('/')
      .filter((part) => part && !part.includes('?'));

    // Remove 'api' prefix if present
    if (urlParts[0] === 'api') {
      urlParts.shift();
    }

    const entityType = urlParts[0] || 'unknown';
    const entityId = params?.id || body?.id;

    // Map HTTP methods to actions
    const actionMap: Record<string, string> = {
      GET: 'READ',
      POST: 'CREATE',
      PUT: 'UPDATE',
      PATCH: 'UPDATE',
      DELETE: 'DELETE',
    };

    let action = actionMap[method] || method;

    // Enhance action based on URL patterns
    if (url.includes('/login')) action = 'LOGIN';
    if (url.includes('/logout')) action = 'LOGOUT';
    if (url.includes('/register')) action = 'REGISTER';
    if (url.includes('/upload')) action = 'UPLOAD';
    if (url.includes('/download')) action = 'DOWNLOAD';
    if (url.includes('/export')) action = 'EXPORT';
    if (url.includes('/verify')) action = 'VERIFY';
    if (url.includes('/approve')) action = 'APPROVE';
    if (url.includes('/reject')) action = 'REJECT';

    return { action, entityType, entityId };
  }

  /**
   * Check if audit logging should be skipped for this URL
   */
  private shouldSkipAudit(url: string): boolean {
    const skipPatterns = [
      '/health',
      '/metrics',
      '/favicon.ico',
      '/doc', // Swagger documentation
      '/.well-known',
    ];

    return skipPatterns.some((pattern) => url.includes(pattern));
  }

  /**
   * Sanitize sensitive data from logs
   */
  private sanitizeData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const sensitiveFields = [
      'password',
      'token',
      'secret',
      'key',
      'authorization',
      'cookie',
      'session',
    ];

    const sanitized = { ...data };

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    // Recursively sanitize nested objects
    for (const key in sanitized) {
      if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        sanitized[key] = this.sanitizeData(sanitized[key]);
      }
    }

    return sanitized;
  }

  /**
   * Check if error is security-relevant
   */
  private isSecurityRelevantError(error: any): boolean {
    const securityErrorCodes = [401, 403, 429]; // Unauthorized, Forbidden, Too Many Requests
    const securityErrorMessages = [
      'unauthorized',
      'forbidden',
      'access denied',
      'invalid token',
      'authentication failed',
      'permission denied',
    ];

    const statusCode = error.status || error.statusCode;
    const message = error.message?.toLowerCase() || '';

    return (
      securityErrorCodes.includes(statusCode) ||
      securityErrorMessages.some((pattern) => message.includes(pattern))
    );
  }

  /**
   * Get security event type based on error
   */
  private getSecurityEventType(error: any, action: string): any {
    const statusCode = error.status || error.statusCode;

    if (statusCode === 401) {
      return 'FAILED_LOGIN';
    }
    if (statusCode === 403) {
      return 'UNAUTHORIZED_ACCESS';
    }
    if (statusCode === 429) {
      return 'RATE_LIMIT_EXCEEDED';
    }

    return 'SUSPICIOUS_ACTIVITY';
  }

  /**
   * Get error severity level
   */
  private getErrorSeverity(error: any): any {
    const statusCode = error.status || error.statusCode;

    if (statusCode === 401 || statusCode === 403) {
      return 'MEDIUM';
    }
    if (statusCode === 429) {
      return 'HIGH';
    }

    return 'LOW';
  }

  /**
   * Monitor API usage patterns for abuse detection
   */
  private async monitorAPIUsage(
    ipAddress: string,
    endpoint: string,
    userId?: string,
  ): Promise<void> {
    try {
      // This is a simplified implementation
      // In a real system, you would use Redis or similar for rate limiting
      // For now, we'll just log high-frequency access patterns
      // The actual rate limiting would be implemented separately
    } catch (error) {
      this.logger.warn(`Failed to monitor API usage: ${error.message}`);
    }
  }
}
