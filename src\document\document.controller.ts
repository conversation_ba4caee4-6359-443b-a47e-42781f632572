/**
 * Document Management System Controller
 *
 * This controller handles all document-related operations including:
 * - Document upload and storage
 * - Document retrieval and filtering
 * - Document verification workflow
 * - Document deletion
 *
 * The controller implements role-based access control with different
 * permissions for regular users, agents, and administrators.
 */
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  HttpStatus,
  HttpCode,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nest-lab/fastify-multer';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiResponse,
} from '@nestjs/swagger';
import { DocumentService } from './document.service';
import { JwtGuard } from 'src/guards/jwt.guard';
import { GetUser } from 'src/decorator/user.decorator';
import { IJWTPayload } from 'src/types';
import {
  DocumentFilterDto,
  UploadDocumentDto,
  VerifyDocumentDto,
  DocumentResponseDto,
  PaginatedDocumentResponseDto,
  BulkDocumentOperationDto,
  BulkVerifyDocumentDto,
} from './dto/document.dto';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';
import { JwtAdminAgent } from 'src/guards/jwt.admin-agent.guard';
import { MediaService } from 'src/media/media.service';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

/**
 * Controller for document management endpoints
 * Provides APIs for document upload, retrieval, verification, and deletion
 */
@ApiTags('documents')
@Controller('documents')
export class DocumentController {
  constructor(
    private documentService: DocumentService,
    private mediaService: MediaService,
  ) {}

  /**
   * Upload a new document
   *
   * This endpoint allows authenticated users to upload documents for immigration services.
   * The document is stored in Supabase storage and a reference is saved in the database.
   *
   * Admins and agents MUST upload documents on behalf of other users by providing a valid userId in CUID format.
   * Regular users can only upload documents for themselves.
   *
   * Access: All authenticated users
   *
   * @param user - The authenticated user from the JWT token
   * @param file - The uploaded file (PDF, JPG, PNG)
   * @param dto - Document metadata including name, expiry date, and required userId (for admins/agents)
   * @param req - The request object containing role information
   * @returns The created document object with metadata
   * @throws BadRequestException - If an admin or agent doesn't provide a userId or provides an invalid CUID format
   * @throws NotFoundException - If the provided userId doesn't exist in the database
   */
  @Post('upload')
  @UseGuards(JwtGuard) // Authenticate the user
  @ApiBearerAuth() // Require authentication
  @ApiOperation({
    summary: 'Upload a document',
    description:
      'Upload a document for immigration services. Admins and agents MUST provide a valid userId parameter (CUID format) to specify which user the document belongs to.',
  })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(
    FileInterceptor('file', { limits: { fileSize: MAX_FILE_SIZE } }),
  )
  @ApiBody({
    schema: {
      type: 'object',
      required: ['file', 'documentName'],
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Document file (PDF, JPG, PNG)',
        },
        documentName: {
          type: 'string',
          description: 'Name of the document',
        },
        expiryDate: {
          type: 'string',
          format: 'date',
          description: 'Expiry date of the document (optional)',
        },
        userId: {
          type: 'string',
          description:
            'User ID in CUID format (REQUIRED for admin/agent use, must be a valid user ID)',
          example: 'cmb15qlnj0000f5wsl08v5k30',
        },
        category: {
          type: 'string',
          enum: ['EMPLOYEE', 'EMPLOYER'],
          description: 'Category of the document',
        },
        serviceTypeId: {
          type: 'string',
          description: 'Service type ID this document is related to',
        },
        applicationId: {
          type: 'string',
          description: 'Application ID this document is related to',
        },
        documentRequirementId: {
          type: 'string',
          description: 'Document requirement ID this document fulfills',
        },
        notes: {
          type: 'string',
          description: 'Additional notes about the document',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Document uploaded successfully',
    type: DocumentResponseDto,
  })
  async uploadDocument(
    @GetUser() user: IJWTPayload,
    @UploadedFile() file: Express.Multer.File,
    @Body() dto: UploadDocumentDto,
    @Req() req: any,
  ) {
    // Check if the user is an admin or agent
    const isAdmin = req.userRole === 'admin';
    const isAgent = req.userRole === 'agent';

    // If the user is an admin or agent, they must provide a userId
    // Otherwise, use the user's own ID
    let effectiveUserId = user.id;

    if (isAdmin || isAgent) {
      if (!dto.userId) {
        throw new BadRequestException(
          'Admin and agent users must provide a userId parameter',
        );
      }
      effectiveUserId = dto.userId;
    }

    return this.documentService.uploadDocument(effectiveUserId, file, dto);
  }

  /**
   * Get a specific document by ID
   *
   * This endpoint allows users to retrieve a specific document by its ID.
   * Users can only access their own documents unless they are admins.
   *
   * Access: Document owner only
   *
   * @param user - The authenticated user from the JWT token
   * @param id - The document ID to retrieve
   * @returns The document object if the user has access
   * @throws ForbiddenException if the user doesn't have access to the document
   * @throws NotFoundException if the document doesn't exist
   */
  @Get(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get document by ID',
    description: 'Get a document by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Document ID',
    type: 'string',
  })
  async getDocumentById(@GetUser() user: IJWTPayload, @Param('id') id: string) {
    return this.documentService.getDocumentById(user.id, id);
  }

  /**
   * Get all documents for a specific user
   *
   * This endpoint allows admins and agents to retrieve all documents for a specific user.
   * The response includes different information based on the role of the requester.
   *
   * Access: Admins and Agents only
   *
   * @param userId - The ID of the user whose documents to retrieve
   * @param filters - Optional filters for verification status and document name
   * @param req - The request object containing role information
   * @returns A list of documents for the specified user
   */
  @Get('user/:userId')
  @UseGuards(JwtAdminAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get user documents (Admin/Agent only)',
    description: 'Get all documents for a specific user',
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    type: 'string',
  })
  @ApiQuery({
    name: 'verificationStatus',
    required: false,
    enum: ['PENDING', 'APPROVED', 'REJECTED'],
  })
  @ApiQuery({
    name: 'documentName',
    required: false,
    type: 'string',
  })
  async getUserDocuments(
    @Param('userId') userId: string,
    @Query() filters: DocumentFilterDto,
    @Req() req: any,
  ) {
    const isAdmin = req.userRole === 'admin';
    return this.documentService.getUserDocuments(userId, filters, isAdmin);
  }

  /**
   * Get all documents for the authenticated user
   *
   * This endpoint allows users to retrieve all their own documents.
   * The results can be filtered by verification status and document name.
   *
   * Access: All authenticated users
   *
   * @param user - The authenticated user from the JWT token
   * @param filters - Optional filters for verification status and document name
   * @returns A list of the user's documents
   */
  @Get()
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get my documents',
    description: 'Get all documents for the authenticated user',
  })
  @ApiQuery({
    name: 'verificationStatus',
    required: false,
    enum: ['PENDING', 'APPROVED', 'REJECTED'],
  })
  @ApiQuery({
    name: 'documentName',
    required: false,
    type: 'string',
  })
  async getMyDocuments(
    @GetUser() user: IJWTPayload,
    @Query() filters: DocumentFilterDto,
  ) {
    return this.documentService.getUserDocuments(user.id, filters);
  }

  /**
   * Get all documents in the system with advanced filtering
   *
   * This endpoint allows admins and agents to retrieve all documents in the system.
   * Admins have access to all document information, while agents have limited access.
   * The results can be filtered by multiple criteria including verification status,
   * document name, category, service type, application, expiry dates, and more.
   *
   * Access: Admins and Agents only
   *
   * @param filters - Advanced filters including pagination, date ranges, expiry filters
   * @param req - The request object containing role information
   * @returns A paginated list of all documents in the system
   */
  @Get('admin/all')
  @UseGuards(JwtAdminAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all documents with advanced filtering (Admin/Agent only)',
    description:
      'Get all documents in the system with comprehensive filtering and pagination',
  })
  @ApiQuery({
    name: 'verificationStatus',
    required: false,
    enum: ['PENDING', 'APPROVED', 'REJECTED'],
    description: 'Filter by verification status',
  })
  @ApiQuery({
    name: 'documentName',
    required: false,
    type: 'string',
    description: 'Filter by document name (partial match)',
  })
  @ApiQuery({
    name: 'category',
    required: false,
    enum: ['EMPLOYEE', 'EMPLOYER'],
    description: 'Filter by document category',
  })
  @ApiQuery({
    name: 'serviceTypeId',
    required: false,
    type: 'string',
    description: 'Filter by service type ID',
  })
  @ApiQuery({
    name: 'applicationId',
    required: false,
    type: 'string',
    description: 'Filter by application ID',
  })
  @ApiQuery({
    name: 'documentRequirementId',
    required: false,
    type: 'string',
    description: 'Filter by document requirement ID',
  })
  @ApiQuery({
    name: 'expiryDateFrom',
    required: false,
    type: 'string',
    description: 'Filter by expiry date range - start date (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'expiryDateTo',
    required: false,
    type: 'string',
    description: 'Filter by expiry date range - end date (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'uploadDateFrom',
    required: false,
    type: 'string',
    description: 'Filter by upload date range - start date (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'uploadDateTo',
    required: false,
    type: 'string',
    description: 'Filter by upload date range - end date (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'expiringSoonDays',
    required: false,
    type: 'number',
    description: 'Filter documents expiring within X days',
  })
  @ApiQuery({
    name: 'expiredOnly',
    required: false,
    type: 'boolean',
    description: 'Filter expired documents only',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: 'number',
    description: 'Page number for pagination (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: 'number',
    description: 'Items per page (default: 10, max: 100)',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: ['uploadDate', 'documentName', 'verificationStatus', 'expiryDate'],
    description: 'Sort field (default: uploadDate)',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['asc', 'desc'],
    description: 'Sort order (default: desc)',
  })
  @ApiResponse({
    status: 200,
    description: 'Documents retrieved successfully',
    type: PaginatedDocumentResponseDto,
  })
  async getAllDocuments(
    @Query() filters: DocumentFilterDto,
    @Req() req: any,
  ): Promise<PaginatedDocumentResponseDto> {
    const isAdmin = req.userRole === 'admin';
    return this.documentService.getAllDocuments(filters, isAdmin);
  }

  /**
   * Verify a document (approve or reject)
   *
   * This endpoint allows admins and agents to verify documents by approving or rejecting them.
   * Admins can perform any verification action, while agents have restrictions:
   * - Agents cannot approve documents directly (only set to PENDING or REJECTED)
   * - Rejection requires a reason to be provided
   *
   * Access: Admins and Agents only
   *
   * @param id - The ID of the document to verify
   * @param dto - The verification data (status and optional rejection reason)
   * @param req - The request object containing role information
   * @returns The updated document with the new verification status
   * @throws BadRequestException if rejecting without a reason
   * @throws ForbiddenException if an agent tries to approve a document
   */
  @Put(':id/verify')
  @UseGuards(JwtAdminAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Verify document (Admin/Agent only)',
    description: 'Approve or reject a document',
  })
  @ApiParam({
    name: 'id',
    description: 'Document ID',
    type: 'string',
  })
  async verifyDocument(
    @Param('id') id: string,
    @Body() dto: VerifyDocumentDto,
    @Req() req: any,
  ) {
    const isAdmin = req.userRole === 'admin';
    return this.documentService.verifyDocument(id, dto, isAdmin);
  }

  /**
   * Delete a user's own document
   *
   * This endpoint allows users to delete their own documents.
   * Users can only delete their own documents.
   *
   * Access: Document owner only
   *
   * @param user - The authenticated user from the JWT token
   * @param id - The ID of the document to delete
   * @returns A success message if the document was deleted
   * @throws ForbiddenException if the user doesn't own the document
   * @throws NotFoundException if the document doesn't exist
   */
  @Delete(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Delete document',
    description: 'Delete a document',
  })
  @ApiParam({
    name: 'id',
    description: 'Document ID',
    type: 'string',
  })
  async deleteDocument(@GetUser() user: IJWTPayload, @Param('id') id: string) {
    return this.documentService.deleteDocument(user.id, id);
  }

  /**
   * Delete any document in the system
   *
   * This endpoint allows admins and agents to delete any document in the system.
   * Admins can delete any document, while agents have restrictions:
   * - Agents cannot delete approved documents
   *
   * Access: Admins and Agents only
   *
   * @param id - The ID of the document to delete
   * @param req - The request object containing role information
   * @returns A success message if the document was deleted
   * @throws ForbiddenException if an agent tries to delete an approved document
   * @throws NotFoundException if the document doesn't exist
   */
  @Delete('admin/:id')
  @UseGuards(JwtAdminAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Delete document (Admin/Agent only)',
    description: 'Delete any document in the system',
  })
  @ApiParam({
    name: 'id',
    description: 'Document ID',
    type: 'string',
  })
  async adminDeleteDocument(@Param('id') id: string, @Req() req: any) {
    const isAdmin = req.userRole === 'admin';
    return this.documentService.deleteDocument('', id, true, isAdmin);
  }

  /**
   * Test endpoint for regular user authentication
   *
   * This endpoint is used to test if regular user authentication is working correctly.
   * It returns a simple success message if the user token is valid.
   *
   * Access: All authenticated users
   *
   * @returns A success message if the user token is valid
   */
  @Get('test-auth')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Test user authentication',
    description: 'Test if user authentication is working correctly',
  })
  @HttpCode(HttpStatus.OK)
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User authentication successful',
  })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  async testUserAuth(@Req() req: any) {
    return {
      message: 'User authentication successful',
      user: req.user,
    };
  }

  /**
   * Test endpoint for admin authentication
   *
   * This endpoint is used to test if admin authentication is working correctly.
   * It returns a simple success message if the admin token is valid.
   *
   * Access: Admins only
   *
   * @returns A success message if the admin token is valid
   */
  @Get('admin/test-auth')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Test admin authentication (Admin only)',
    description: 'Test if admin authentication is working correctly',
  })
  @HttpCode(HttpStatus.OK)
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Admin authentication successful',
  })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  async testAdminAuth(@Req() req: any) {
    return {
      message: 'Admin authentication successful',
      user: req.user,
    };
  }

  /**
   * Test endpoint for admin-agent authentication
   *
   * This endpoint is used to test if admin-agent authentication is working correctly.
   * It returns a simple success message if the admin or agent token is valid.
   *
   * Access: Admins and Agents only
   *
   * @returns A success message if the admin or agent token is valid
   */
  @Get('admin-agent/test-auth')
  @UseGuards(JwtAdminAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Test admin-agent authentication (Admin/Agent only)',
    description: 'Test if admin-agent authentication is working correctly',
  })
  @HttpCode(HttpStatus.OK)
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Admin/Agent authentication successful',
  })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  async testAdminAgentAuth(@Req() req: any) {
    return {
      message: 'Admin/Agent authentication successful',
      userRole: req.userRole,
      user: req.user,
    };
  }

  /**
   * Get file upload configuration
   *
   * This endpoint returns the current file upload configuration.
   * It's useful for debugging file upload issues.
   *
   * Access: All authenticated users
   *
   * @returns The current file upload configuration
   */
  @Get('debug-config')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get file upload configuration',
    description: 'Get the current file upload configuration',
  })
  async getFileUploadConfig() {
    return {
      message: 'File upload configuration',
      config: {
        maxFileSize: MAX_FILE_SIZE,
        allowedMimeTypes: this.documentService.getAllowedMimeTypes(),
        supabase: {
          url: process.env.SUPABASE_URL ? 'Set' : 'Not set',
          key: process.env.SUPABASE_KEY ? 'Set (masked)' : 'Not set',
          bucketName: process.env.BUCKET_NAME || 'Not set',
        },
      },
    };
  }

  /**
   * Get documents expiring soon
   *
   * This endpoint retrieves documents that are expiring within a specified number of days.
   * Useful for proactive document renewal management.
   *
   * Access: Admin and Agent users
   *
   * @param days - Number of days to look ahead (default: 30)
   * @param user - The authenticated user from the JWT token
   * @param req - The request object containing role information
   * @returns List of documents expiring within the specified timeframe
   */
  @Get('expiring-soon')
  @UseGuards(JwtAdminAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get documents expiring soon',
    description:
      'Retrieve documents that are expiring within a specified number of days',
  })
  @ApiQuery({
    name: 'days',
    required: false,
    type: Number,
    description: 'Number of days to look ahead (default: 30)',
    example: 30,
  })
  @ApiResponse({
    status: 200,
    description: 'Documents expiring soon retrieved successfully',
    type: PaginatedDocumentResponseDto,
  })
  async getDocumentsExpiringSoon(
    @Query('days') days: string = '30',
    @GetUser() user: IJWTPayload,
    @Req() req: any,
  ): Promise<PaginatedDocumentResponseDto> {
    const isAdmin = req.userRole === 'admin';
    const daysNumber = parseInt(days, 10) || 30;

    return this.documentService.getDocumentsExpiringSoon(daysNumber, isAdmin);
  }

  /**
   * Bulk verify documents
   *
   * This endpoint allows admins and agents to verify multiple documents at once.
   * Agents can only reject documents or set them to pending, while admins can approve.
   *
   * Access: Admin and Agent users
   *
   * @param dto - Bulk verification data including document IDs and status
   * @param user - The authenticated user from the JWT token
   * @param req - The request object containing role information
   * @returns Results of the bulk verification operation
   */
  @Put('bulk-verify')
  @UseGuards(JwtAdminAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Bulk verify documents',
    description:
      'Verify multiple documents at once. Agents can only reject or set to pending.',
  })
  @ApiBody({
    type: BulkVerifyDocumentDto,
    description: 'Bulk verification data',
  })
  @ApiResponse({
    status: 200,
    description: 'Documents verified successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        updatedCount: { type: 'number' },
        status: { type: 'string' },
      },
    },
  })
  async bulkVerifyDocuments(
    @Body() dto: BulkVerifyDocumentDto,
    @GetUser() user: IJWTPayload,
    @Req() req: any,
  ) {
    const isAdmin = req.userRole === 'admin';
    return this.documentService.bulkVerifyDocuments(dto, isAdmin);
  }

  /**
   * Bulk delete documents
   *
   * This endpoint allows users to delete multiple documents at once.
   * Regular users can only delete their own documents, while admins can delete any.
   * Agents cannot delete approved documents.
   *
   * Access: All authenticated users
   *
   * @param dto - Bulk operation data including document IDs
   * @param user - The authenticated user from the JWT token
   * @param req - The request object containing role information
   * @returns Results of the bulk deletion operation
   */
  @Delete('bulk-delete')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Bulk delete documents',
    description:
      'Delete multiple documents at once. Users can only delete their own documents.',
  })
  @ApiBody({
    type: BulkDocumentOperationDto,
    description: 'Bulk operation data',
  })
  @ApiResponse({
    status: 200,
    description: 'Documents deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        deletedCount: { type: 'number' },
      },
    },
  })
  async bulkDeleteDocuments(
    @Body() dto: BulkDocumentOperationDto,
    @GetUser() user: IJWTPayload,
    @Req() req: any,
  ) {
    const isAdmin = req.userRole === 'admin';
    const isAgent = req.userRole === 'agent';
    const hasAdminAccess = isAdmin || isAgent;

    return this.documentService.bulkDeleteDocuments(
      dto,
      user.id,
      hasAdminAccess,
      isAdmin,
    );
  }

  /**
   * Debug endpoint for file upload
   *
   * This endpoint is used to debug file upload issues.
   * It logs detailed information about the uploaded file and returns it.
   *
   * Access: All authenticated users
   *
   * @param file - The uploaded file
   * @returns Detailed information about the uploaded file
   */
  @Post('debug-upload')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Debug file upload',
    description: 'Debug endpoint for file upload issues',
  })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(
    FileInterceptor('file', { limits: { fileSize: MAX_FILE_SIZE } }),
  )
  @ApiBody({
    schema: {
      type: 'object',
      required: ['file'],
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Any file to test upload',
        },
      },
    },
  })
  async debugFileUpload(
    @UploadedFile() file: Express.Multer.File,
    @Req() req: any,
  ) {
    console.log('Debug file upload request received');
    console.log(
      'File:',
      file
        ? {
            originalname: file.originalname,
            mimetype: file.mimetype,
            size: file.size,
            buffer: file.buffer
              ? `Buffer (${file.buffer.length} bytes)`
              : 'No buffer',
          }
        : 'No file received',
    );

    try {
      // Test Supabase connection
      const supabaseTest = await this.mediaService.testSupabaseConnection();

      return {
        message: 'Debug information',
        file: file
          ? {
              originalname: file.originalname,
              mimetype: file.mimetype,
              size: file.size,
              bufferReceived: !!file.buffer,
              bufferSize: file.buffer ? file.buffer.length : 0,
            }
          : 'No file received',
        user: req.user,
        supabaseTest,
      };
    } catch (error) {
      console.error('Error in debug upload:', error);
      return {
        message: 'Error in debug upload',
        error: {
          message: error.message,
          stack: error.stack,
        },
        file: file
          ? {
              originalname: file.originalname,
              mimetype: file.mimetype,
              size: file.size,
              bufferReceived: !!file.buffer,
            }
          : 'No file received',
      };
    }
  }
}
