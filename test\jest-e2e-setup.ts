// Mock environment variables
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.WEBSITE = 'https://test.example.com';
process.env.SUPABASE_URL = 'https://test-supabase-url.com';
process.env.SUPABASE_KEY = 'test-supabase-key';
process.env.RESEND_API_KEY = 'test-resend-api-key';
process.env.STRIPE_SECRET_KEY = 'test-stripe-secret-key';
process.env.STRIPE_WEBHOOK_SECRET = 'test-stripe-webhook-secret';
process.env.OPENAI_API_KEY = 'test-openai-api-key';

// Mock modules
jest.mock('@react-email/components', () => {
  return {
    render: jest.fn().mockReturnValue('Mocked email content'),
    Body: jest.fn(),
    Button: jest.fn(),
    Container: jest.fn(),
    Head: jest.fn(),
    Heading: jest.fn(),
    Html: jest.fn(),
    Img: jest.fn(),
    Link: jest.fn(),
    Preview: jest.fn(),
    Section: jest.fn(),
    Text: jest.fn(),
  };
});

// Mock Prisma
jest.mock('src/utils/prisma.service', () => {
  return {
    PrismaService: jest.fn().mockImplementation(() => {
      return {
        onModuleInit: jest.fn(),
        enableShutdownHooks: jest.fn(),
        user: {
          findUnique: jest.fn(),
          findFirst: jest.fn(),
          findMany: jest.fn(),
          create: jest.fn(),
          update: jest.fn(),
          delete: jest.fn(),
          count: jest.fn(),
        },
        mentor: {
          findUnique: jest.fn(),
          findFirst: jest.fn(),
          findMany: jest.fn(),
          create: jest.fn(),
          update: jest.fn(),
          delete: jest.fn(),
          count: jest.fn(),
        },
        admin: {
          findUnique: jest.fn(),
          findFirst: jest.fn(),
          findMany: jest.fn(),
          create: jest.fn(),
          update: jest.fn(),
          delete: jest.fn(),
          count: jest.fn(),
        },
        otp: {
          findUnique: jest.fn(),
          findFirst: jest.fn(),
          findMany: jest.fn(),
          create: jest.fn(),
          update: jest.fn(),
          delete: jest.fn(),
          count: jest.fn(),
        },
        immigration_service: {
          findUnique: jest.fn(),
          findFirst: jest.fn(),
          findMany: jest.fn(),
          create: jest.fn(),
          update: jest.fn(),
          delete: jest.fn(),
          count: jest.fn(),
        },
      };
    }),
  };
});
