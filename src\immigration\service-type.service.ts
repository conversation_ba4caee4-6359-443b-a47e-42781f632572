import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../utils/prisma.service';
import {
  CreateServiceTypeDto,
  UpdateServiceTypeDto,
  FilterServiceTypeDto,
  ServiceTypeResponseDto,
  PaginatedServiceTypeResponseDto,
} from './dto/service-type.dto';
import { service_type } from '@prisma/client';

@Injectable()
export class ServiceTypeService {
  private readonly logger = new Logger(ServiceTypeService.name);

  constructor(private prisma: PrismaService) {}

  /**
   * Create a new service type
   * @param dto Data for creating a service type
   * @returns The created service type
   */
  async create(dto: CreateServiceTypeDto): Promise<service_type> {
    try {
      return await this.prisma.service_type.create({
        data: {
          name: dto.name,
          description: dto.description,
          price: dto.price,
        },
      });
    } catch (error) {
      throw new BadRequestException(
        'Failed to create service type: ' + error.message,
      );
    }
  }

  /**
   * Get all service types with optional filtering and pagination
   * @param filterDto Filter and pagination options
   * @returns Paginated list of service types
   */
  async findAll(
    filterDto: FilterServiceTypeDto,
  ): Promise<PaginatedServiceTypeResponseDto> {
    const {
      search,
      minPrice,
      maxPrice,
      page = 1,
      limit = 10,
      sortBy = 'name',
      sortOrder = 'asc',
    } = filterDto;

    // Enforce limit constraints (50 for public, 100 for admin)
    const effectiveLimit = Math.min(limit, 50);
    const skip = (page - 1) * effectiveLimit;

    this.logger.log(
      `Finding service types with filters: ${JSON.stringify(filterDto)}`,
    );

    // Build the where clause based on filters
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (minPrice !== undefined || maxPrice !== undefined) {
      where.price = {};
      if (minPrice !== undefined) {
        where.price.gte = minPrice;
      }
      if (maxPrice !== undefined) {
        where.price.lte = maxPrice;
      }
    }

    // Build the orderBy clause
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    try {
      const [data, total] = await Promise.all([
        this.prisma.service_type.findMany({
          where,
          skip,
          take: effectiveLimit,
          orderBy,
        }),
        this.prisma.service_type.count({ where }),
      ]);

      const totalPages = Math.ceil(total / effectiveLimit);

      this.logger.log(
        `Found ${data.length} service types out of ${total} total`,
      );

      return {
        data: data.map(this.mapToResponseDto),
        total,
        page,
        limit: effectiveLimit,
        totalPages,
      };
    } catch (error) {
      this.logger.error(
        `Failed to fetch service types: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        'Failed to fetch service types: ' + error.message,
      );
    }
  }

  /**
   * Get a service type by ID
   * @param id Service type ID
   * @returns The service type
   * @throws NotFoundException if service type not found
   */
  async findOne(id: string): Promise<service_type> {
    const serviceType = await this.prisma.service_type.findUnique({
      where: { id },
    });

    if (!serviceType) {
      throw new NotFoundException(`Service type with ID ${id} not found`);
    }

    return serviceType;
  }

  /**
   * Update a service type
   * @param id Service type ID
   * @param dto Data for updating the service type
   * @returns The updated service type
   * @throws NotFoundException if service type not found
   */
  async update(id: string, dto: UpdateServiceTypeDto): Promise<service_type> {
    // Check if service type exists
    await this.findOne(id);

    try {
      return await this.prisma.service_type.update({
        where: { id },
        data: {
          ...(dto.name && { name: dto.name }),
          ...(dto.description && { description: dto.description }),
          ...(dto.price !== undefined && { price: dto.price }),
        },
      });
    } catch (error) {
      throw new BadRequestException(
        'Failed to update service type: ' + error.message,
      );
    }
  }

  /**
   * Delete a service type
   * @param id Service type ID
   * @returns The deleted service type
   * @throws NotFoundException if service type not found
   * @throws BadRequestException if service type has associated applications
   */
  async remove(id: string): Promise<service_type> {
    // Check if service type exists
    await this.findOne(id);

    // Check if service type has associated applications
    const applicationCount = await this.prisma.application.count({
      where: { serviceTypeId: id },
    });

    if (applicationCount > 0) {
      throw new BadRequestException(
        `Cannot delete service type with ID ${id} because it has ${applicationCount} associated applications`,
      );
    }

    try {
      return await this.prisma.service_type.delete({
        where: { id },
      });
    } catch (error) {
      throw new BadRequestException(
        'Failed to delete service type: ' + error.message,
      );
    }
  }

  /**
   * Get document requirements for a service type
   * @param id Service type ID
   * @returns List of document requirements
   * @throws NotFoundException if service type not found
   */
  async getDocumentRequirements(id: string) {
    // Check if service type exists
    await this.findOne(id);

    return this.prisma.document_requirement.findMany({
      where: { serviceTypeId: id },
      orderBy: { name: 'asc' },
    });
  }

  /**
   * Get all service types with admin-level statistics
   * @param filterDto Filter and pagination options
   * @returns Paginated list of service types with stats
   */
  async findAllWithStats(
    filterDto: FilterServiceTypeDto,
  ): Promise<PaginatedServiceTypeResponseDto> {
    const {
      search,
      minPrice,
      maxPrice,
      page = 1,
      limit = 10,
      sortBy = 'name',
      sortOrder = 'asc',
    } = filterDto;

    // Admin can have higher limits
    const effectiveLimit = Math.min(limit, 100);
    const skip = (page - 1) * effectiveLimit;

    this.logger.log(
      `Admin finding service types with filters: ${JSON.stringify(filterDto)}`,
    );

    // Build the where clause based on filters
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (minPrice !== undefined || maxPrice !== undefined) {
      where.price = {};
      if (minPrice !== undefined) {
        where.price.gte = minPrice;
      }
      if (maxPrice !== undefined) {
        where.price.lte = maxPrice;
      }
    }

    // Build the orderBy clause
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    try {
      const [data, total] = await Promise.all([
        this.prisma.service_type.findMany({
          where,
          skip,
          take: effectiveLimit,
          orderBy,
          include: {
            _count: {
              select: {
                documentRequirements: true,
                applications: true,
              },
            },
          },
        }),
        this.prisma.service_type.count({ where }),
      ]);

      const totalPages = Math.ceil(total / effectiveLimit);

      this.logger.log(
        `Admin found ${data.length} service types out of ${total} total`,
      );

      return {
        data: data.map(this.mapToAdminResponseDto),
        total,
        page,
        limit: effectiveLimit,
        totalPages,
      };
    } catch (error) {
      this.logger.error(
        `Failed to fetch service types with stats: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        'Failed to fetch service types with stats: ' + error.message,
      );
    }
  }

  /**
   * Get a service type by ID with admin-level statistics
   * @param id Service type ID
   * @returns Service type with admin details
   * @throws NotFoundException if service type not found
   */
  async findOneWithStats(id: string): Promise<ServiceTypeResponseDto> {
    this.logger.log(`Admin finding service type: ${id}`);

    const serviceType = await this.prisma.service_type.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            documentRequirements: true,
            applications: true,
          },
        },
      },
    });

    if (!serviceType) {
      throw new NotFoundException(`Service type with ID ${id} not found`);
    }

    return this.mapToAdminResponseDto(serviceType);
  }

  /**
   * Get a service type by ID with its document requirements (public)
   * @param id Service type ID
   * @returns Service type with document requirements
   * @throws NotFoundException if service type not found
   */
  async findOneWithRequirements(id: string): Promise<ServiceTypeResponseDto> {
    this.logger.log(`Public finding service type with requirements: ${id}`);

    const serviceType = await this.prisma.service_type.findUnique({
      where: { id },
      include: {
        documentRequirements: {
          orderBy: { name: 'asc' },
        },
      },
    });

    if (!serviceType) {
      throw new NotFoundException(`Service type with ID ${id} not found`);
    }

    return this.mapToPublicResponseDto(serviceType);
  }

  /**
   * Map service type to basic response DTO
   * @param serviceType Service type from database
   * @returns Mapped response DTO
   */
  private mapToResponseDto = (
    serviceType: service_type,
  ): ServiceTypeResponseDto => {
    return {
      id: serviceType.id,
      name: serviceType.name,
      description: serviceType.description,
      price: Number(serviceType.price),
      createdAt: serviceType.createdAt,
      updatedAt: serviceType.updatedAt,
    };
  };

  /**
   * Map service type to admin response DTO with statistics
   * @param serviceType Service type from database with counts
   * @returns Mapped admin response DTO
   */
  private mapToAdminResponseDto = (
    serviceType: any,
  ): ServiceTypeResponseDto => {
    return {
      id: serviceType.id,
      name: serviceType.name,
      description: serviceType.description,
      price: Number(serviceType.price),
      createdAt: serviceType.createdAt,
      updatedAt: serviceType.updatedAt,
      documentRequirementsCount: serviceType._count?.documentRequirements || 0,
      applicationsCount: serviceType._count?.applications || 0,
    };
  };

  /**
   * Map service type to public response DTO with document requirements
   * @param serviceType Service type from database with requirements
   * @returns Mapped public response DTO
   */
  private mapToPublicResponseDto = (
    serviceType: any,
  ): ServiceTypeResponseDto => {
    return {
      id: serviceType.id,
      name: serviceType.name,
      description: serviceType.description,
      price: Number(serviceType.price),
      createdAt: serviceType.createdAt,
      updatedAt: serviceType.updatedAt,
      documentRequirements: serviceType.documentRequirements || [],
    };
  };
}
