import { JwtService } from '@nestjs/jwt';
import { v4 as uuidv4 } from 'uuid';
import { expect } from '@jest/globals';

/**
 * Authentication helpers for E2E tests
 *
 * Provides utilities for generating JWT tokens and managing test users
 * across different roles (Admin, User, Agent) for comprehensive E2E testing.
 */

export interface TestUser {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'user' | 'agent';
  password?: string;
}

export interface TestCredentials {
  user: TestUser;
  token: string;
  bearerToken: string;
}

/**
 * Test user fixtures for different roles
 */
export const TEST_USERS: Record<string, TestUser> = {
  admin: {
    id: 'admin-test-id-1',
    email: '<EMAIL>',
    name: 'Test Admin',
    role: 'admin',
    password: 'admin123',
  },
  user: {
    id: 'user-test-id-1',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'user',
    password: 'user123',
  },
  agent: {
    id: 'agent-test-id-1',
    email: '<EMAIL>',
    name: 'Test Agent',
    role: 'agent',
    password: 'agent123',
  },
  user2: {
    id: 'user-test-id-2',
    email: '<EMAIL>',
    name: 'Test User 2',
    role: 'user',
    password: 'user123',
  },
};

/**
 * JWT secret for testing (consistent across all roles)
 */
const JWT_SECRET = 'test-secret-key-for-e2e-testing';

/**
 * Generate a JWT token for a test user
 */
export function generateTestToken(user: TestUser): string {
  const jwtService = new JwtService();

  const payload = {
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role,
    sub: user.id,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 60 * 60, // 1 hour
  };

  return jwtService.sign(payload, { secret: JWT_SECRET });
}

/**
 * Get test credentials for a specific role
 */
export function getTestCredentials(
  role: keyof typeof TEST_USERS,
): TestCredentials {
  const user = TEST_USERS[role];
  const token = generateTestToken(user);

  return {
    user,
    token,
    bearerToken: `Bearer ${token}`,
  };
}

/**
 * Create a new test user with random ID
 */
export function createTestUser(
  role: 'admin' | 'user' | 'agent',
  overrides: Partial<TestUser> = {},
): TestUser {
  const id = uuidv4();
  return {
    id,
    email: overrides.email || `test-${id}@example.com`,
    name: overrides.name || `Test ${role} ${id.slice(0, 8)}`,
    role,
    password: 'test123',
    ...overrides,
  };
}

/**
 * Generate credentials for a custom test user
 */
export function generateCredentials(user: TestUser): TestCredentials {
  const token = generateTestToken(user);

  return {
    user,
    token,
    bearerToken: `Bearer ${token}`,
  };
}

/**
 * Mock Prisma user data for testing
 */
export function mockPrismaUser(user: TestUser) {
  return {
    id: user.id,
    email: user.email,
    name: user.name,
    password: '$2b$10$hashedpassword', // Mock hashed password
    isVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

/**
 * Mock Prisma admin data for testing
 */
export function mockPrismaAdmin(user: TestUser) {
  return {
    id: user.id,
    email: user.email,
    name: user.name,
    password: '$2b$10$hashedpassword', // Mock hashed password
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

/**
 * Mock Prisma agent data for testing
 */
export function mockPrismaAgent(user: TestUser) {
  return {
    id: user.id,
    email: user.email,
    name: user.name,
    password: '$2b$10$hashedpassword', // Mock hashed password
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

/**
 * Authentication test utilities
 */
export const AuthTestUtils = {
  /**
   * Test that an endpoint requires authentication
   */
  expectUnauthorized: async (request: any) => {
    const response = await request;
    expect(response.status).toBe(401);
    return response;
  },

  /**
   * Test that an endpoint requires specific role
   */
  expectForbidden: async (request: any) => {
    const response = await request;
    expect(response.status).toBe(403);
    return response;
  },

  /**
   * Test successful authentication
   */
  expectAuthenticated: async (request: any, expectedStatus: number = 200) => {
    const response = await request;
    expect(response.status).toBe(expectedStatus);
    return response;
  },
};
