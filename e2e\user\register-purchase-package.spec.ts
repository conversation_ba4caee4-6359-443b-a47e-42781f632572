import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { PrismaService } from '../../src/utils/prisma.service';
import { MockPrismaService } from '../../src/utils/prisma.service.mock';
import { MinimalTestAppModule } from '../../test/minimal-test-app.module';
import {
  getTestCredentials,
  createTestUser,
  generateCredentials,
  mockPrismaUser,
} from '../utils/auth-helpers';
import {
  PACKAGE_FIXTURES,
  PAYMENT_SCENARIOS,
  MOCK_STRIPE_SESSION,
  mockPrismaPackage,
} from '../utils/fixtures/packages';
import { testResultCollector, testResultReporter } from '../utils/test-results';

/**
 * E2E Test Suite: User Registration and Package Purchase
 *
 * Tests the complete workflow from user registration to package purchase,
 * including authentication, package selection, and payment processing.
 *
 * Scenarios covered:
 * 1. User registration and email verification
 * 2. User login and authentication
 * 3. Browse available packages
 * 4. Initiate package purchase
 * 5. Handle payment processing (Stripe integration)
 * 6. Confirm purchase and generate application ID
 */
describe('User Registration and Package Purchase (E2E)', () => {
  let app: INestApplication;
  let prismaService: MockPrismaService;

  const existingUserCredentials = getTestCredentials('user');

  beforeAll(async () => {
    testResultCollector.clear();

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [MinimalTestAppModule],
    })
      .overrideProvider(PrismaService)
      .useClass(MockPrismaService)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      }),
    );

    prismaService = moduleFixture.get<MockPrismaService>(PrismaService);
    await app.init();
  });

  afterAll(async () => {
    const suiteResults = testResultCollector.getSuiteResults(
      'User Registration and Package Purchase',
    );
    const summary = testResultReporter.generateSummary([suiteResults]);
    testResultReporter.saveResults(summary);
    testResultReporter.saveLogs(summary);
    testResultReporter.generateHtmlReport(summary);

    await app.close();
  });

  beforeEach(() => {
    prismaService.clearTestData();
    jest.clearAllMocks();
  });

  describe('User Registration', () => {
    it('should register a new user successfully', async () => {
      testResultCollector.startTest(
        'User Registration and Package Purchase',
        'Register new user',
      );

      const newUser = createTestUser('user', {
        email: '<EMAIL>',
        name: 'New Test User',
      });

      const registrationData = {
        name: newUser.name,
        email: newUser.email,
        password: newUser.password,
      };

      const mockCreatedUser = mockPrismaUser(newUser);
      prismaService.user.findUnique.mockResolvedValue(null); // User doesn't exist
      prismaService.user.create.mockResolvedValue(mockCreatedUser);

      const startTime = Date.now();
      const response = await request(app.getHttpServer())
        .post('/user/register')
        .send(registrationData);

      const duration = Date.now() - startTime;

      testResultCollector.logHttpRequest({
        method: 'POST',
        url: '/user/register',
        statusCode: response.status,
        requestBody: { ...registrationData, password: '[REDACTED]' },
        responseBody: response.body,
        duration,
        timestamp: new Date(),
      });

      if (response.status === 201) {
        expect(response.body.user.email).toBe(newUser.email);
        expect(response.body.user.name).toBe(newUser.name);
        expect(response.body).toHaveProperty('token');
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 201, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should reject registration with existing email', async () => {
      testResultCollector.startTest(
        'User Registration and Package Purchase',
        'Reject duplicate email',
      );

      const existingUser = mockPrismaUser(existingUserCredentials.user);
      prismaService.user.findUnique.mockResolvedValue(existingUser);

      const response = await request(app.getHttpServer())
        .post('/user/register')
        .send({
          name: 'Another User',
          email: existingUserCredentials.user.email,
          password: 'password123',
        });

      if (response.status === 400 || response.status === 409) {
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 400/409, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should validate required registration fields', async () => {
      testResultCollector.startTest(
        'User Registration and Package Purchase',
        'Validate registration fields',
      );

      const response = await request(app.getHttpServer())
        .post('/user/register')
        .send({
          name: 'Test User',
          // Missing email and password
        });

      if (response.status === 400) {
        expect(response.body.message).toBeDefined();
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 400, got ${response.status}`,
          response.body,
        );
      }
    });
  });

  describe('User Login', () => {
    it('should login with valid credentials', async () => {
      testResultCollector.startTest(
        'User Registration and Package Purchase',
        'Login with valid credentials',
      );

      const user = existingUserCredentials.user;
      const mockUser = mockPrismaUser(user);

      prismaService.user.findUnique.mockResolvedValue(mockUser);

      const response = await request(app.getHttpServer())
        .post('/user/login')
        .send({
          email: user.email,
          password: user.password,
        });

      if (response.status === 200) {
        expect(response.body).toHaveProperty('token');
        expect(response.body.user.email).toBe(user.email);
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 200, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should reject login with invalid credentials', async () => {
      testResultCollector.startTest(
        'User Registration and Package Purchase',
        'Reject invalid credentials',
      );

      prismaService.user.findUnique.mockResolvedValue(null);

      const response = await request(app.getHttpServer())
        .post('/user/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword',
        });

      if (response.status === 401 || response.status === 400) {
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 401/400, got ${response.status}`,
          response.body,
        );
      }
    });
  });

  describe('Package Browsing', () => {
    it('should retrieve available packages', async () => {
      testResultCollector.startTest(
        'User Registration and Package Purchase',
        'Browse available packages',
      );

      const packages = Object.values(PACKAGE_FIXTURES).map((pkg) =>
        mockPrismaPackage(pkg),
      );
      prismaService.packages.findMany.mockResolvedValue(packages);
      prismaService.packages.count.mockResolvedValue(packages.length);

      const response = await request(app.getHttpServer())
        .get('/packages')
        .set('Authorization', existingUserCredentials.bearerToken);

      if (response.status === 200) {
        expect(response.body.data).toHaveLength(packages.length);
        expect(response.body.data[0]).toHaveProperty('name');
        expect(response.body.data[0]).toHaveProperty('amount');
        expect(response.body.data[0]).toHaveProperty('service');
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 200, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should retrieve specific package details', async () => {
      testResultCollector.startTest(
        'User Registration and Package Purchase',
        'Get package details',
      );

      const packageData = mockPrismaPackage(PACKAGE_FIXTURES.criticalSkills);
      prismaService.packages.findUnique.mockResolvedValue(packageData);

      const response = await request(app.getHttpServer())
        .get(`/packages/${packageData.id}`)
        .set('Authorization', existingUserCredentials.bearerToken);

      if (response.status === 200) {
        expect(response.body.id).toBe(packageData.id);
        expect(response.body.name).toBe(packageData.name);
        expect(response.body.service).toEqual(packageData.service);
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 200, got ${response.status}`,
          response.body,
        );
      }
    });
  });

  describe('Package Purchase', () => {
    it('should initiate package purchase successfully', async () => {
      testResultCollector.startTest(
        'User Registration and Package Purchase',
        'Initiate package purchase',
      );

      const packageData = mockPrismaPackage(PACKAGE_FIXTURES.criticalSkills);
      prismaService.packages.findUnique.mockResolvedValue(packageData);

      // Mock Stripe session creation
      const mockStripeResponse = {
        status: 'OK',
        url: MOCK_STRIPE_SESSION.url,
      };

      const response = await request(app.getHttpServer())
        .post('/payment/package')
        .set('Authorization', existingUserCredentials.bearerToken)
        .send({
          packageId: packageData.id,
        });

      if (response.status === 200 || response.status === 201) {
        expect(response.body).toHaveProperty('url');
        expect(response.body.status).toBe('OK');
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 200/201, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should handle purchase of non-existent package', async () => {
      testResultCollector.startTest(
        'User Registration and Package Purchase',
        'Handle non-existent package',
      );

      prismaService.packages.findUnique.mockResolvedValue(null);

      const response = await request(app.getHttpServer())
        .post('/payment/package')
        .set('Authorization', existingUserCredentials.bearerToken)
        .send(PAYMENT_SCENARIOS.invalidPackage);

      if (response.status === 404) {
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 404, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should reject purchase without authentication', async () => {
      testResultCollector.startTest(
        'User Registration and Package Purchase',
        'Reject unauthenticated purchase',
      );

      const response = await request(app.getHttpServer())
        .post('/payment/package')
        .send(PAYMENT_SCENARIOS.validPayment);

      if (response.status === 401 || response.status === 403) {
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 401/403, got ${response.status}`,
          response.body,
        );
      }
    });
  });

  describe('Guest Package Purchase', () => {
    it('should allow guest package purchase', async () => {
      testResultCollector.startTest(
        'User Registration and Package Purchase',
        'Guest package purchase',
      );

      const packageData = mockPrismaPackage(PACKAGE_FIXTURES.generalEmployment);
      prismaService.packages.findUnique.mockResolvedValue(packageData);

      const response = await request(app.getHttpServer())
        .post('/payment/guest-package')
        .send(PAYMENT_SCENARIOS.guestPayment);

      if (response.status === 200 || response.status === 201) {
        expect(response.body).toHaveProperty('url');
        expect(response.body.status).toBe('OK');
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 200/201, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should validate guest purchase information', async () => {
      testResultCollector.startTest(
        'User Registration and Package Purchase',
        'Validate guest purchase info',
      );

      const response = await request(app.getHttpServer())
        .post('/payment/guest-package')
        .send({
          packageId: 'pkg-test-1',
          // Missing name, email, mobile_no
        });

      if (response.status === 400) {
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 400, got ${response.status}`,
          response.body,
        );
      }
    });
  });

  describe('User Profile Management', () => {
    it('should retrieve user profile', async () => {
      testResultCollector.startTest(
        'User Registration and Package Purchase',
        'Get user profile',
      );

      const user = mockPrismaUser(existingUserCredentials.user);
      prismaService.user.findUnique.mockResolvedValue(user);

      const response = await request(app.getHttpServer())
        .get('/user')
        .set('Authorization', existingUserCredentials.bearerToken);

      if (response.status === 200) {
        expect(response.body.email).toBe(user.email);
        expect(response.body.name).toBe(user.name);
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 200, got ${response.status}`,
          response.body,
        );
      }
    });

    it('should update user profile', async () => {
      testResultCollector.startTest(
        'User Registration and Package Purchase',
        'Update user profile',
      );

      const user = mockPrismaUser(existingUserCredentials.user);
      const updatedUser = { ...user, name: 'Updated Name' };

      prismaService.user.findUnique.mockResolvedValue(user);
      prismaService.user.update.mockResolvedValue(updatedUser);

      const response = await request(app.getHttpServer())
        .patch('/user')
        .set('Authorization', existingUserCredentials.bearerToken)
        .send({
          name: 'Updated Name',
        });

      if (response.status === 200) {
        expect(response.body.name).toBe('Updated Name');
        testResultCollector.endTest('PASS');
      } else {
        testResultCollector.endTest(
          'FAIL',
          `Expected 200, got ${response.status}`,
          response.body,
        );
      }
    });
  });
});
