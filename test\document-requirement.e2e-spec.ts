import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { PrismaService } from '../src/utils/prisma.service';
import { MockPrismaService } from '../src/utils/prisma.service.mock';
import { DocCategory } from '@prisma/client';
import { TestAppModule } from './test-app.module';

describe('DocumentRequirementController (e2e)', () => {
  let app: INestApplication;
  let prismaService: MockPrismaService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [TestAppModule],
    })
      .overrideProvider(PrismaService)
      .useClass(MockPrismaService)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ whitelist: true }));

    prismaService = moduleFixture.get<MockPrismaService>(PrismaService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /admin/document-requirements', () => {
    it('should create a new document requirement', () => {
      // Mock data
      const createDto = {
        name: 'Passport',
        description: 'Valid passport with at least 6 months validity',
        required: true,
        category: DocCategory.EMPLOYEE,
        serviceTypeId: 'service-type-1',
      };

      const serviceType = {
        id: 'service-type-1',
        name: 'Critical Skills Employment Permit',
        description: 'Permit for highly skilled professionals',
        price: 1000,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const createdDocumentRequirement = {
        id: 'doc-req-1',
        ...createDto,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Setup mock
      prismaService.service_type.findUnique.mockResolvedValue(serviceType);
      prismaService.document_requirement.create.mockResolvedValue(
        createdDocumentRequirement,
      );

      return request(app.getHttpServer())
        .post('/admin/document-requirements')
        .send(createDto)
        .expect(201)
        .expect((res) => {
          expect(res.body.id).toBe('doc-req-1');
          expect(res.body.name).toBe('Passport');
          expect(res.body.category).toBe(DocCategory.EMPLOYEE);
        });
    });

    it('should return 400 if service type not found', () => {
      // Mock data
      const createDto = {
        name: 'Passport',
        description: 'Valid passport with at least 6 months validity',
        required: true,
        category: DocCategory.EMPLOYEE,
        serviceTypeId: 'non-existent-id',
      };

      // Setup mock
      prismaService.service_type.findUnique.mockResolvedValue(null);

      return request(app.getHttpServer())
        .post('/admin/document-requirements')
        .send(createDto)
        .expect(400);
    });

    it('should return 400 if validation fails', () => {
      return request(app.getHttpServer())
        .post('/admin/document-requirements')
        .send({
          name: 'Passport',
          description: 'Valid passport with at least 6 months validity',
          required: true,
          category: 'INVALID_CATEGORY', // Invalid category
          serviceTypeId: 'service-type-1',
        })
        .expect(400);
    });
  });

  describe('GET /admin/document-requirements/:id', () => {
    it('should return a document requirement by id', () => {
      // Mock data
      const documentRequirement = {
        id: 'doc-req-1',
        name: 'Passport',
        description: 'Valid passport with at least 6 months validity',
        required: true,
        category: DocCategory.EMPLOYEE,
        serviceTypeId: 'service-type-1',
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: {
          id: 'service-type-1',
          name: 'Critical Skills Employment Permit',
        },
      };

      // Setup mock
      prismaService.document_requirement.findUnique.mockResolvedValue(
        documentRequirement,
      );

      return request(app.getHttpServer())
        .get('/admin/document-requirements/doc-req-1')
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe('doc-req-1');
          expect(res.body.name).toBe('Passport');
          expect(res.body.serviceType.name).toBe(
            'Critical Skills Employment Permit',
          );
        });
    });

    it('should return 404 if document requirement not found', () => {
      // Setup mock
      prismaService.document_requirement.findUnique.mockResolvedValue(null);

      return request(app.getHttpServer())
        .get('/admin/document-requirements/non-existent-id')
        .expect(404);
    });
  });

  describe('PUT /admin/document-requirements/:id', () => {
    it('should update a document requirement', () => {
      // Mock data
      const updateDto = {
        name: 'Updated Passport',
        required: false,
      };

      const documentRequirement = {
        id: 'doc-req-1',
        name: 'Passport',
        description: 'Valid passport with at least 6 months validity',
        required: true,
        category: DocCategory.EMPLOYEE,
        serviceTypeId: 'service-type-1',
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: {
          id: 'service-type-1',
          name: 'Critical Skills Employment Permit',
        },
      };

      const updatedDocumentRequirement = {
        ...documentRequirement,
        name: 'Updated Passport',
        required: false,
      };

      // Setup mock
      prismaService.document_requirement.findUnique.mockResolvedValue(
        documentRequirement,
      );
      prismaService.document_requirement.update.mockResolvedValue(
        updatedDocumentRequirement,
      );

      return request(app.getHttpServer())
        .put('/admin/document-requirements/doc-req-1')
        .send(updateDto)
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe('doc-req-1');
          expect(res.body.name).toBe('Updated Passport');
          expect(res.body.required).toBe(false);
        });
    });

    it('should check if new service type exists', () => {
      // Mock data
      const updateDto = {
        serviceTypeId: 'new-service-type-id',
      };

      const documentRequirement = {
        id: 'doc-req-1',
        name: 'Passport',
        description: 'Valid passport with at least 6 months validity',
        required: true,
        category: DocCategory.EMPLOYEE,
        serviceTypeId: 'service-type-1',
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: {
          id: 'service-type-1',
          name: 'Critical Skills Employment Permit',
        },
      };

      // Setup mock
      prismaService.document_requirement.findUnique.mockResolvedValue(
        documentRequirement,
      );
      prismaService.service_type.findUnique.mockResolvedValue(null);

      return request(app.getHttpServer())
        .put('/admin/document-requirements/doc-req-1')
        .send(updateDto)
        .expect(400);
    });
  });

  describe('DELETE /admin/document-requirements/:id', () => {
    it('should delete a document requirement', () => {
      // Mock data
      const documentRequirement = {
        id: 'doc-req-1',
        name: 'Passport',
        description: 'Valid passport with at least 6 months validity',
        required: true,
        category: DocCategory.EMPLOYEE,
        serviceTypeId: 'service-type-1',
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: {
          id: 'service-type-1',
          name: 'Critical Skills Employment Permit',
        },
      };

      // Setup mock
      prismaService.document_requirement.findUnique.mockResolvedValue(
        documentRequirement,
      );
      prismaService.document_requirement.delete.mockResolvedValue(
        documentRequirement,
      );

      return request(app.getHttpServer())
        .delete('/admin/document-requirements/doc-req-1')
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe('doc-req-1');
          expect(res.body.name).toBe('Passport');
        });
    });

    it('should return 404 if document requirement not found', () => {
      // Setup mock
      prismaService.document_requirement.findUnique.mockResolvedValue(null);

      return request(app.getHttpServer())
        .delete('/admin/document-requirements/non-existent-id')
        .expect(404);
    });
  });
});
