import { Modu<PERSON> } from '@nestjs/common';
import { ImmigrationController } from './immigration.controller';
import { ImmigrationService } from './immigration.service';
import { PrismaService } from 'src/utils/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { ServiceTypeController } from './service-type.controller';
import { ServiceTypeService } from './service-type.service';
import { DocumentRequirementController } from './document-requirement.controller';
import { DocumentRequirementService } from './document-requirement.service';
import { ApplicationService } from './application.service';
import { ApplicationController } from './application.controller';
import { WorkflowStepController } from './workflow-step.controller';

@Module({
  controllers: [
    ImmigrationController,
    ServiceTypeController,
    DocumentRequirementController,
    ApplicationController,
    WorkflowStepController,
  ],
  providers: [
    ImmigrationService,
    ServiceTypeService,
    DocumentRequirementService,
    ApplicationService,
    PrismaService,
    JwtService,
  ],
  exports: [ApplicationService, ServiceTypeService, DocumentRequirementService],
})
export class ImmigrationModule {}
