import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';

/**
 * Mock implementation of JwtAdmin guard for testing
 * This guard always allows access and sets the user and userRole in the request
 */
@Injectable()
export class JwtAdmin implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    // Set mock user data
    request['user'] = {
      id: 'admin-id',
      email: '<EMAIL>',
      role: 'admin'
    };
    request['userRole'] = 'admin';
    
    return true;
  }
}
