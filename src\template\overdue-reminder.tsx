import {
  <PERSON>,
  But<PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';

import * as React from 'react';

interface OverdueReminderEmailProps {
  userName?: string;
  overdueReminders: Array<{
    message: string;
    dueDate: string;
    daysPastDue: number;
    applicationName?: string;
    documentName?: string;
  }>;
  dashboardUrl?: string;
}

export default function OverdueReminderEmail({
  userName = '',
  overdueReminders = [],
  dashboardUrl,
}: OverdueReminderEmailProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const totalOverdue = overdueReminders.length;

  return (
    <Html>
      <Head />
      <Preview>{`You have ${totalOverdue} overdue reminder${totalOverdue > 1 ? 's' : ''}`}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Container style={logoSection}>
            <Link href={process.env.WEBSITE} style={logoLink}>
              <Img
                src="https://supabase.careerireland.com/storage/v1/object/public/careerireland-prod/logo/logo-careerireland.webp"
                width="80"
                height="80"
                alt="Logo"
                style={logoImage}
              />
            </Link>
          </Container>

          <Section style={urgentAlert}>
            <Text style={alertText}>
              🚨 URGENT ACTION REQUIRED
            </Text>
          </Section>

          <Heading style={h1}>Overdue Reminders</Heading>

          <Text style={text}>
            {userName ? `Hi ${userName},` : 'Hello,'}
          </Text>

          <Text style={text}>
            You have <strong>{totalOverdue}</strong> overdue reminder{totalOverdue > 1 ? 's' : ''}
            that require immediate attention. Please review and complete these tasks as soon as possible
            to avoid delays in your immigration process.
          </Text>

          <Section style={remindersContainer}>
            {overdueReminders.map((reminder, index) => (
              <Section key={index} style={reminderItem}>
                <Text style={reminderMessage}>{reminder.message}</Text>

                <Section style={reminderDetails}>
                  <Text style={overdueInfo}>
                    <span style={overdueLabel}>Due Date:</span> {formatDate(reminder.dueDate)}
                  </Text>
                  <Text style={daysPastDue}>
                    {reminder.daysPastDue} day{reminder.daysPastDue > 1 ? 's' : ''} overdue
                  </Text>

                  {reminder.applicationName && (
                    <Text style={relatedInfo}>
                      <span style={infoLabel}>Application:</span> {reminder.applicationName}
                    </Text>
                  )}

                  {reminder.documentName && (
                    <Text style={relatedInfo}>
                      <span style={infoLabel}>Document:</span> {reminder.documentName}
                    </Text>
                  )}
                </Section>
              </Section>
            ))}
          </Section>

          {dashboardUrl && (
            <Section style={buttonContainer}>
              <Button style={urgentButton} href={dashboardUrl}>
                View All Reminders
              </Button>
            </Section>
          )}

          <Section style={warningBox}>
            <Text style={warningText}>
              ⚠️ <strong>Important:</strong> Delayed responses to immigration requirements
              can result in application delays, additional fees, or even rejection.
              Please prioritize these overdue items.
            </Text>
          </Section>

          <Text style={text}>
            If you need assistance with any of these items, please don't hesitate to
            contact our support team. We're here to help you stay on track with your
            immigration journey.
          </Text>

          <Text style={text}>
            Best regards,
            <br />
            The Career Ireland Team
          </Text>
        </Container>
      </Body>
    </Html>
  );
}

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  padding: '0 48px',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '24px auto',
  padding: '48px 24px',
  borderRadius: '8px',
  maxWidth: '600px',
};

const logoLink = {
  display: 'inline-block',
  textDecoration: 'none',
};

const logoSection = {
  textAlign: 'center' as const,
  marginBottom: '32px',
  display: 'flex',
  flexDirection: 'column' as const,
  alignItems: 'center',
  maxWidth: '600px',
};

const logoImage = {
  margin: '0 auto',
  borderRadius: '12px',
  padding: '12px',
  backgroundColor: '#f8fafc',
  objectFit: 'contain' as const,
  transition: 'all 0.2s ease',
};

const urgentAlert = {
  backgroundColor: '#fef2f2',
  border: '2px solid #ef4444',
  borderRadius: '8px',
  padding: '16px',
  textAlign: 'center' as const,
  margin: '24px 0',
};

const alertText = {
  color: '#ef4444',
  fontSize: '16px',
  fontWeight: '700',
  margin: '0',
  textTransform: 'uppercase' as const,
  letterSpacing: '0.1em',
};

const h1 = {
  color: '#1f2937',
  fontSize: '28px',
  fontWeight: '700',
  lineHeight: '1.4',
  margin: '0 0 24px',
  textAlign: 'center' as const,
};

const text = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '24px 0',
};

const remindersContainer = {
  margin: '32px 0',
};

const reminderItem = {
  backgroundColor: '#fef2f2',
  border: '1px solid #fecaca',
  borderLeft: '4px solid #ef4444',
  borderRadius: '8px',
  padding: '20px',
  margin: '16px 0',
};

const reminderMessage = {
  color: '#1f2937',
  fontSize: '16px',
  fontWeight: '600',
  lineHeight: '24px',
  margin: '0 0 12px 0',
};

const reminderDetails = {
  borderTop: '1px solid #fecaca',
  paddingTop: '12px',
  marginTop: '12px',
};

const overdueInfo = {
  color: '#374151',
  fontSize: '14px',
  margin: '4px 0',
};

const overdueLabel = {
  fontWeight: '600',
  color: '#1f2937',
};

const daysPastDue = {
  color: '#ef4444',
  fontSize: '14px',
  fontWeight: '600',
  margin: '4px 0',
};

const relatedInfo = {
  color: '#6b7280',
  fontSize: '14px',
  margin: '4px 0',
};

const infoLabel = {
  fontWeight: '500',
  color: '#374151',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const urgentButton = {
  backgroundColor: '#ef4444',
  borderRadius: '6px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '14px 28px',
  margin: '0 auto',
};

const warningBox = {
  backgroundColor: '#fffbeb',
  border: '1px solid #fbbf24',
  borderRadius: '8px',
  padding: '20px',
  margin: '32px 0',
};

const warningText = {
  color: '#92400e',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '0',
};
