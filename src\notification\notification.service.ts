import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../utils/prisma.service';
import { MailerService } from '../mailer/mailer.service';
import {
  CreateNotificationDto,
  UpdateNotificationReadDto,
  NotificationFilterDto,
  BulkNotificationDto,
  NotificationResponseDto,
  NotificationListResponseDto,
} from './dto/notification.dto';
import { NotificationType } from '@prisma/client';

/**
 * Service for managing notifications
 * Handles CRUD operations for user notifications with filtering and pagination
 */
@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly mailerService: MailerService,
  ) {}

  /**
   * Create a new notification for a user
   * @param createNotificationDto - Data for creating the notification
   * @returns The created notification
   */
  async createNotification(
    createNotificationDto: CreateNotificationDto,
  ): Promise<NotificationResponseDto> {
    try {
      this.logger.log(
        `Creating notification for user ${createNotificationDto.userId}`,
      );

      // Verify user exists
      const user = await this.prisma.user.findUnique({
        where: { id: createNotificationDto.userId },
      });

      if (!user) {
        throw new NotFoundException(
          `User with ID ${createNotificationDto.userId} not found`,
        );
      }

      const notification = await this.prisma.notification.create({
        data: {
          message: createNotificationDto.message,
          type: createNotificationDto.type || NotificationType.INFO,
          userId: createNotificationDto.userId,
        },
      });

      this.logger.log(`Notification created with ID: ${notification.id}`);

      // Send email notification asynchronously (don't wait for it to complete)
      this.sendEmailNotification(user, notification).catch((emailError) => {
        this.logger.error(
          `Failed to send email notification: ${emailError.message}`,
          emailError.stack,
        );
        // Don't throw the error - notification creation should succeed even if email fails
      });

      return notification;
    } catch (error) {
      this.logger.error(
        `Failed to create notification: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get notifications for a specific user with filtering and pagination
   * @param userId - The user ID to get notifications for
   * @param filters - Filter and pagination options
   * @returns Paginated list of notifications
   */
  async getUserNotifications(
    userId: string,
    filters: NotificationFilterDto,
  ): Promise<NotificationListResponseDto> {
    try {
      this.logger.log(
        `Getting notifications for user ${userId} with filters:`,
        filters,
      );

      // Verify user exists
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Build where clause for filtering
      const where: any = {
        userId,
      };

      if (filters.type) {
        where.type = filters.type;
      }

      if (filters.read !== undefined) {
        where.read = filters.read;
      }

      if (filters.fromDate || filters.toDate) {
        where.createdAt = {};
        if (filters.fromDate) {
          where.createdAt.gte = new Date(filters.fromDate);
        }
        if (filters.toDate) {
          where.createdAt.lte = new Date(filters.toDate);
        }
      }

      // Calculate pagination
      const page = Math.max(1, filters.page || 1);
      const limit = Math.max(1, Math.min(100, filters.limit || 10)); // Max 100 items per page
      const skip = (page - 1) * limit;

      // Get total count for pagination
      const total = await this.prisma.notification.count({ where });

      // Get notifications with pagination
      const notifications = await this.prisma.notification.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      });

      // Get unread count
      const unreadCount = await this.prisma.notification.count({
        where: {
          userId,
          read: false,
        },
      });

      const totalPages = Math.ceil(total / limit);

      this.logger.log(
        `Retrieved ${notifications.length} notifications for user ${userId}`,
      );

      return {
        notifications,
        total,
        page,
        limit,
        totalPages,
        unreadCount,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get notifications for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Mark a notification as read or unread
   * @param notificationId - The notification ID to update
   * @param userId - The user ID (for authorization)
   * @param updateDto - The update data
   * @returns The updated notification
   */
  async updateNotificationReadStatus(
    notificationId: string,
    userId: string,
    updateDto: UpdateNotificationReadDto,
  ): Promise<NotificationResponseDto> {
    try {
      this.logger.log(
        `Updating notification ${notificationId} read status for user ${userId}`,
      );

      // Find the notification and verify ownership
      const existingNotification = await this.prisma.notification.findFirst({
        where: {
          id: notificationId,
          userId,
        },
      });

      if (!existingNotification) {
        throw new NotFoundException(
          `Notification with ID ${notificationId} not found or does not belong to user ${userId}`,
        );
      }

      const updatedNotification = await this.prisma.notification.update({
        where: { id: notificationId },
        data: { read: updateDto.read },
      });

      this.logger.log(
        `Notification ${notificationId} read status updated to ${updateDto.read}`,
      );
      return updatedNotification;
    } catch (error) {
      this.logger.error(
        `Failed to update notification ${notificationId} read status: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Delete a notification
   * @param notificationId - The notification ID to delete
   * @param userId - The user ID (for authorization)
   * @returns Success message
   */
  async deleteNotification(
    notificationId: string,
    userId: string,
  ): Promise<{ message: string }> {
    try {
      this.logger.log(
        `Deleting notification ${notificationId} for user ${userId}`,
      );

      // Find the notification and verify ownership
      const existingNotification = await this.prisma.notification.findFirst({
        where: {
          id: notificationId,
          userId,
        },
      });

      if (!existingNotification) {
        throw new NotFoundException(
          `Notification with ID ${notificationId} not found or does not belong to user ${userId}`,
        );
      }

      await this.prisma.notification.delete({
        where: { id: notificationId },
      });

      this.logger.log(`Notification ${notificationId} deleted successfully`);
      return { message: 'Notification deleted successfully' };
    } catch (error) {
      this.logger.error(
        `Failed to delete notification ${notificationId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Mark multiple notifications as read
   * @param userId - The user ID
   * @param bulkDto - The notification IDs to mark as read
   * @returns Success message with count
   */
  async markMultipleAsRead(
    userId: string,
    bulkDto: BulkNotificationDto,
  ): Promise<{ message: string; count: number }> {
    try {
      this.logger.log(
        `Marking ${bulkDto.notificationIds.length} notifications as read for user ${userId}`,
      );

      // Verify all notifications belong to the user
      const existingNotifications = await this.prisma.notification.findMany({
        where: {
          id: { in: bulkDto.notificationIds },
          userId,
        },
        select: { id: true },
      });

      if (existingNotifications.length !== bulkDto.notificationIds.length) {
        throw new BadRequestException(
          'Some notifications do not exist or do not belong to the user',
        );
      }

      const result = await this.prisma.notification.updateMany({
        where: {
          id: { in: bulkDto.notificationIds },
          userId,
        },
        data: { read: true },
      });

      this.logger.log(
        `Marked ${result.count} notifications as read for user ${userId}`,
      );
      return {
        message: `Successfully marked ${result.count} notifications as read`,
        count: result.count,
      };
    } catch (error) {
      this.logger.error(
        `Failed to mark notifications as read for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Delete multiple notifications
   * @param userId - The user ID
   * @param bulkDto - The notification IDs to delete
   * @returns Success message with count
   */
  async deleteMultipleNotifications(
    userId: string,
    bulkDto: BulkNotificationDto,
  ): Promise<{ message: string; count: number }> {
    try {
      this.logger.log(
        `Deleting ${bulkDto.notificationIds.length} notifications for user ${userId}`,
      );

      // Verify all notifications belong to the user
      const existingNotifications = await this.prisma.notification.findMany({
        where: {
          id: { in: bulkDto.notificationIds },
          userId,
        },
        select: { id: true },
      });

      if (existingNotifications.length !== bulkDto.notificationIds.length) {
        throw new BadRequestException(
          'Some notifications do not exist or do not belong to the user',
        );
      }

      const result = await this.prisma.notification.deleteMany({
        where: {
          id: { in: bulkDto.notificationIds },
          userId,
        },
      });

      this.logger.log(
        `Deleted ${result.count} notifications for user ${userId}`,
      );
      return {
        message: `Successfully deleted ${result.count} notifications`,
        count: result.count,
      };
    } catch (error) {
      this.logger.error(
        `Failed to delete notifications for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get unread notification count for a user
   * @param userId - The user ID
   * @returns The count of unread notifications
   */
  async getUnreadCount(userId: string): Promise<{ count: number }> {
    try {
      const count = await this.prisma.notification.count({
        where: {
          userId,
          read: false,
        },
      });

      return { count };
    } catch (error) {
      this.logger.error(
        `Failed to get unread count for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Send email notification to user
   * @param user - User object with email and name
   * @param notification - Notification object
   * @private
   */
  private async sendEmailNotification(
    user: any,
    notification: any,
  ): Promise<void> {
    try {
      if (!user.email) {
        this.logger.warn(
          `User ${user.id} has no email address, skipping email notification`,
        );
        return;
      }

      await this.mailerService.sendNotificationEmail({
        to: user.email,
        userName: user.name || user.email,
        message: notification.message,
        notificationType: notification.type,
        actionUrl: process.env.FRONTEND_URL
          ? `${process.env.FRONTEND_URL}/notifications`
          : undefined,
        actionText: 'View Notifications',
      });

      this.logger.log(
        `Email notification sent to ${user.email} for notification ${notification.id}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send email notification: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
