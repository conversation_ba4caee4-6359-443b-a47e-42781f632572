import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsEmail,
  IsOptional,
  IsString,
  IsEnum,
  IsDateString,
} from 'class-validator';
import { NotificationType } from '@prisma/client';

export class ResendEmailDto {
  @ApiProperty()
  @IsString()
  subject: string;
  @ApiProperty()
  @IsString()
  @IsEmail()
  to: string;

  @ApiProperty()
  @IsString()
  from: string;
  @ApiProperty()
  @IsOptional()
  @IsString()
  @IsEmail()
  @IsArray()
  cc: string[];

  @ApiProperty()
  @IsString()
  html: string;

  // @ApiProperty({ required: false })
  // @IsString()
  // @IsOptional()
  // text: string;
}

/**
 * DTO for sending notification emails
 */
export class NotificationEmailDto {
  @ApiProperty({
    description: 'Recipient email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  to: string;

  @ApiProperty({
    description: 'Recipient name',
    example: '<PERSON>',
  })
  @IsString()
  userName: string;

  @ApiProperty({
    description: 'Notification message',
    example: 'Your document has been approved',
  })
  @IsString()
  message: string;

  @ApiProperty({
    description: 'Type of notification',
    enum: NotificationType,
    example: NotificationType.SUCCESS,
  })
  @IsEnum(NotificationType)
  notificationType: NotificationType;

  @ApiPropertyOptional({
    description: 'Optional action URL',
    example: 'https://app.careerireland.com/documents',
  })
  @IsOptional()
  @IsString()
  actionUrl?: string;

  @ApiPropertyOptional({
    description: 'Optional action button text',
    example: 'View Document',
  })
  @IsOptional()
  @IsString()
  actionText?: string;
}

/**
 * DTO for sending reminder emails
 */
export class ReminderEmailDto {
  @ApiProperty({
    description: 'Recipient email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  to: string;

  @ApiProperty({
    description: 'Recipient name',
    example: 'John Doe',
  })
  @IsString()
  userName: string;

  @ApiProperty({
    description: 'Reminder message',
    example: 'Please submit your passport documents',
  })
  @IsString()
  message: string;

  @ApiProperty({
    description: 'Due date for the reminder',
    example: '2024-02-15T10:00:00.000Z',
  })
  @IsDateString()
  dueDate: string;

  @ApiPropertyOptional({
    description: 'Whether the reminder is overdue',
    example: false,
  })
  @IsOptional()
  isOverdue?: boolean;

  @ApiPropertyOptional({
    description: 'Related application name',
    example: 'Work Permit Application',
  })
  @IsOptional()
  @IsString()
  applicationName?: string;

  @ApiPropertyOptional({
    description: 'Related document name',
    example: 'Passport Copy',
  })
  @IsOptional()
  @IsString()
  documentName?: string;

  @ApiPropertyOptional({
    description: 'Optional action URL',
    example: 'https://app.careerireland.com/reminders',
  })
  @IsOptional()
  @IsString()
  actionUrl?: string;

  @ApiPropertyOptional({
    description: 'Optional action button text',
    example: 'Complete Task',
  })
  @IsOptional()
  @IsString()
  actionText?: string;
}

/**
 * DTO for sending overdue reminder digest emails
 */
export class OverdueReminderDigestDto {
  @ApiProperty({
    description: 'Recipient email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  to: string;

  @ApiProperty({
    description: 'Recipient name',
    example: 'John Doe',
  })
  @IsString()
  userName: string;

  @ApiProperty({
    description: 'Array of overdue reminders',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        dueDate: { type: 'string' },
        daysPastDue: { type: 'number' },
        applicationName: { type: 'string' },
        documentName: { type: 'string' },
      },
    },
  })
  overdueReminders: Array<{
    message: string;
    dueDate: string;
    daysPastDue: number;
    applicationName?: string;
    documentName?: string;
  }>;

  @ApiPropertyOptional({
    description: 'Optional dashboard URL',
    example: 'https://app.careerireland.com/dashboard',
  })
  @IsOptional()
  @IsString()
  dashboardUrl?: string;
}
