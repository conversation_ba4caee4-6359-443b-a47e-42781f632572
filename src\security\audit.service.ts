import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../utils/prisma.service';
import * as geoip from 'geoip-lite';
import { UAParser } from 'ua-parser-js';

/**
 * Comprehensive Audit Logging Service
 *
 * Provides detailed audit trails for all system operations.
 * Tracks user actions, data changes, and security events.
 *
 * Features:
 * - Comprehensive action logging
 * - Geographic location tracking
 * - User agent analysis
 * - Risk scoring
 * - Audit trail retrieval and reporting
 */
@Injectable()
export class AuditService {
  private readonly logger = new Logger(AuditService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Log a user action with full context
   */
  async logAction(params: {
    action: string;
    entityType: string;
    entityId?: string;
    userId?: string;
    userRole?: string;
    ipAddress?: string;
    userAgent?: string;
    oldValues?: any;
    newValues?: any;
    metadata?: any;
    sessionId?: string;
    requestId?: string;
    success?: boolean;
    errorMessage?: string;
  }): Promise<void> {
    try {
      const {
        action,
        entityType,
        entityId,
        userId,
        userRole,
        ipAddress,
        userAgent,
        oldValues,
        newValues,
        metadata,
        sessionId,
        requestId,
        success = true,
        errorMessage,
      } = params;

      // Get geographic location from IP
      const location = ipAddress ? this.getLocationFromIP(ipAddress) : null;

      // Calculate risk score
      const riskScore = this.calculateRiskScore({
        action,
        entityType,
        ipAddress,
        userAgent,
        success,
        userId,
      });

      await this.prisma.audit_log.create({
        data: {
          action,
          entityType,
          entityId,
          userId,
          userRole,
          ipAddress,
          userAgent,
          location,
          oldValues: oldValues ? JSON.parse(JSON.stringify(oldValues)) : null,
          newValues: newValues ? JSON.parse(JSON.stringify(newValues)) : null,
          metadata: metadata ? JSON.parse(JSON.stringify(metadata)) : null,
          sessionId,
          requestId,
          success,
          errorMessage,
          riskScore,
        },
      });

      this.logger.log(
        `Audit log created: ${action} on ${entityType}${entityId ? `:${entityId}` : ''} by user ${userId || 'anonymous'}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to create audit log: ${error.message}`,
        error.stack,
      );
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Log authentication events
   */
  async logAuthentication(params: {
    action:
      | 'LOGIN'
      | 'LOGOUT'
      | 'LOGIN_FAILED'
      | 'PASSWORD_RESET'
      | 'PASSWORD_CHANGED';
    userId?: string;
    email?: string;
    ipAddress?: string;
    userAgent?: string;
    success: boolean;
    errorMessage?: string;
    metadata?: any;
  }): Promise<void> {
    const {
      action,
      userId,
      email,
      ipAddress,
      userAgent,
      success,
      errorMessage,
      metadata,
    } = params;

    await this.logAction({
      action,
      entityType: 'authentication',
      entityId: userId || email,
      userId,
      ipAddress,
      userAgent,
      success,
      errorMessage,
      metadata: {
        ...metadata,
        email: email && !userId ? email : undefined,
      },
    });

    // Log failed authentication attempts separately for security monitoring
    if (!success && action === 'LOGIN_FAILED') {
      await this.logFailedAuthAttempt({
        email,
        ipAddress,
        userAgent,
        failureReason: errorMessage || 'INVALID_CREDENTIALS',
        metadata,
      });
    }
  }

  /**
   * Log data access events
   */
  async logDataAccess(params: {
    action: 'READ' | 'download' | 'export';
    entityType: string;
    entityId: string;
    userId: string;
    userRole?: string;
    ipAddress?: string;
    userAgent?: string;
    metadata?: any;
  }): Promise<void> {
    await this.logAction({
      ...params,
      action: `DATA_${params.action.toUpperCase()}`,
    });
  }

  /**
   * Log CRUD operations with before/after values
   */
  async logCRUD(params: {
    action: 'CREATE' | 'UPDATE' | 'DELETE';
    entityType: string;
    entityId: string;
    userId?: string;
    userRole?: string;
    ipAddress?: string;
    userAgent?: string;
    oldValues?: any;
    newValues?: any;
    metadata?: any;
  }): Promise<void> {
    await this.logAction(params);
  }

  /**
   * Log security events
   */
  async logSecurityEvent(params: {
    action: string;
    userId?: string;
    ipAddress?: string;
    userAgent?: string;
    metadata?: any;
    riskLevel?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  }): Promise<void> {
    await this.logAction({
      ...params,
      entityType: 'security',
      action: `SECURITY_${params.action}`,
      metadata: {
        ...params.metadata,
        riskLevel: params.riskLevel,
      },
    });
  }

  /**
   * Get audit logs with filtering and pagination
   */
  async getAuditLogs(params: {
    userId?: string;
    entityType?: string;
    entityId?: string;
    action?: string;
    ipAddress?: string;
    startDate?: Date;
    endDate?: Date;
    success?: boolean;
    riskScoreMin?: number;
    riskScoreMax?: number;
    page?: number;
    limit?: number;
  }) {
    const {
      userId,
      entityType,
      entityId,
      action,
      ipAddress,
      startDate,
      endDate,
      success,
      riskScoreMin,
      riskScoreMax,
      page = 1,
      limit = 50,
    } = params;

    const where: any = {};

    if (userId) where.userId = userId;
    if (entityType) where.entityType = entityType;
    if (entityId) where.entityId = entityId;
    if (action) where.action = { contains: action, mode: 'insensitive' };
    if (ipAddress) where.ipAddress = ipAddress;
    if (success !== undefined) where.success = success;

    if (startDate || endDate) {
      where.timestamp = {};
      if (startDate) where.timestamp.gte = startDate;
      if (endDate) where.timestamp.lte = endDate;
    }

    if (riskScoreMin !== undefined || riskScoreMax !== undefined) {
      where.riskScore = {};
      if (riskScoreMin !== undefined) where.riskScore.gte = riskScoreMin;
      if (riskScoreMax !== undefined) where.riskScore.lte = riskScoreMax;
    }

    const skip = (page - 1) * limit;

    const [logs, total] = await Promise.all([
      this.prisma.audit_log.findMany({
        where,
        skip,
        take: limit,
        orderBy: { timestamp: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      }),
      this.prisma.audit_log.count({ where }),
    ]);

    return {
      data: logs,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Log failed authentication attempt
   */
  private async logFailedAuthAttempt(params: {
    email?: string;
    ipAddress?: string;
    userAgent?: string;
    failureReason: string;
    metadata?: any;
  }): Promise<void> {
    try {
      const { email, ipAddress, userAgent, failureReason, metadata } = params;

      const location = ipAddress ? this.getLocationFromIP(ipAddress) : null;

      await this.prisma.failed_auth_attempt.create({
        data: {
          email,
          ipAddress: ipAddress || 'unknown',
          userAgent,
          location,
          failureReason,
          metadata: metadata ? JSON.parse(JSON.stringify(metadata)) : null,
        },
      });
    } catch (error) {
      this.logger.error(
        `Failed to log authentication attempt: ${error.message}`,
      );
    }
  }

  /**
   * Get geographic location from IP address
   */
  private getLocationFromIP(ipAddress: string): string | null {
    try {
      // Skip private/local IPs
      if (
        ipAddress === '127.0.0.1' ||
        ipAddress === '::1' ||
        ipAddress.startsWith('192.168.') ||
        ipAddress.startsWith('10.')
      ) {
        return 'Local/Private Network';
      }

      const geo = geoip.lookup(ipAddress);
      if (geo) {
        return `${geo.city || 'Unknown City'}, ${geo.country || 'Unknown Country'}`;
      }
      return null;
    } catch (error) {
      this.logger.warn(
        `Failed to get location for IP ${ipAddress}: ${error.message}`,
      );
      return null;
    }
  }

  /**
   * Calculate risk score based on various factors
   */
  private calculateRiskScore(params: {
    action: string;
    entityType: string;
    ipAddress?: string;
    userAgent?: string;
    success: boolean;
    userId?: string;
  }): number {
    let score = 0;

    // Base score for different actions
    const actionScores: Record<string, number> = {
      LOGIN_FAILED: 30,
      DELETE: 25,
      UPDATE: 15,
      CREATE: 10,
      READ: 5,
      SECURITY_: 50, // Any security event
    };

    // Find matching action score
    for (const [actionPattern, actionScore] of Object.entries(actionScores)) {
      if (params.action.includes(actionPattern)) {
        score += actionScore;
        break;
      }
    }

    // Increase score for sensitive entity types
    const sensitiveEntities = ['user', 'document', 'application', 'admin'];
    if (sensitiveEntities.includes(params.entityType)) {
      score += 10;
    }

    // Increase score for failed operations
    if (!params.success) {
      score += 20;
    }

    // Increase score for suspicious user agents
    if (params.userAgent) {
      const ua = new UAParser(params.userAgent);
      const browser = ua.getBrowser();
      const os = ua.getOS();

      // Suspicious patterns
      if (!browser.name || !os.name) {
        score += 15; // Missing browser/OS info
      }

      if (
        params.userAgent.toLowerCase().includes('bot') ||
        params.userAgent.toLowerCase().includes('crawler')
      ) {
        score += 25; // Bot/crawler activity
      }
    }

    // Increase score for anonymous users
    if (!params.userId) {
      score += 10;
    }

    // Cap the score at 100
    return Math.min(score, 100);
  }

  /**
   * Get audit statistics
   */
  async getAuditStatistics(params: {
    startDate?: Date;
    endDate?: Date;
    userId?: string;
  }) {
    const { startDate, endDate, userId } = params;

    const where: any = {};
    if (userId) where.userId = userId;
    if (startDate || endDate) {
      where.timestamp = {};
      if (startDate) where.timestamp.gte = startDate;
      if (endDate) where.timestamp.lte = endDate;
    }

    const [
      totalLogs,
      failedOperations,
      highRiskEvents,
      actionBreakdown,
      entityBreakdown,
    ] = await Promise.all([
      this.prisma.audit_log.count({ where }),
      this.prisma.audit_log.count({ where: { ...where, success: false } }),
      this.prisma.audit_log.count({
        where: { ...where, riskScore: { gte: 70 } },
      }),
      this.prisma.audit_log.groupBy({
        by: ['action'],
        where,
        _count: { action: true },
        orderBy: { _count: { action: 'desc' } },
        take: 10,
      }),
      this.prisma.audit_log.groupBy({
        by: ['entityType'],
        where,
        _count: { entityType: true },
        orderBy: { _count: { entityType: 'desc' } },
        take: 10,
      }),
    ]);

    return {
      totalLogs,
      failedOperations,
      highRiskEvents,
      successRate:
        totalLogs > 0
          ? (((totalLogs - failedOperations) / totalLogs) * 100).toFixed(2)
          : '100.00',
      actionBreakdown: actionBreakdown.map((item) => ({
        action: item.action,
        count: item._count.action,
      })),
      entityBreakdown: entityBreakdown.map((item) => ({
        entityType: item.entityType,
        count: item._count.entityType,
      })),
    };
  }
}
