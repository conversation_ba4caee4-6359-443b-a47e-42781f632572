import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { PrismaService } from '../src/utils/prisma.service';
import { MockPrismaService } from '../src/utils/prisma.service.mock';
import { TestAppModule } from './test-app.module';
import { JwtService } from '@nestjs/jwt';

/**
 * E2E tests for Workflow Step endpoints
 *
 * Tests admin-only workflow step management endpoints:
 * - Creating workflow steps for service types
 * - Getting workflow steps for service types
 */
describe('WorkflowStepController (e2e)', () => {
  let app: INestApplication;
  let prismaService: MockPrismaService;
  let jwtService: JwtService;

  // JWT secret for testing (matches the guard expectation)
  const JWT_SECRET = 'test-secret-key-for-e2e-testing';

  // Helper function to generate test tokens
  const generateTestToken = (role: string, userId: string = 'test-id') => {
    const payload = {
      id: userId,
      email: `${role}@test.com`,
      name: `Test ${role}`,
      role: role,
      sub: userId,
      iat: Math.floor(Date.now() / 1000),
    };
    return jwtService.sign(payload, {
      secret: JWT_SECRET,
      expiresIn: '1h',
    });
  };

  let adminToken: string;
  let userToken: string;
  let agentToken: string;

  const mockServiceType = {
    id: 'cld123abc456def789',
    name: 'Critical Skills Employment Permit',
    description: 'Test service type',
    price: 1000,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [TestAppModule],
    })
      .overrideProvider(PrismaService)
      .useClass(MockPrismaService)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ whitelist: true }));

    prismaService = moduleFixture.get<MockPrismaService>(PrismaService);
    jwtService = moduleFixture.get<JwtService>(JwtService);

    // Generate real JWT tokens for testing
    adminToken = `Bearer ${generateTestToken('admin', 'admin-test-id-1')}`;
    userToken = `Bearer ${generateTestToken('user', 'user-test-id-1')}`;
    agentToken = `Bearer ${generateTestToken('agent', 'agent-test-id-1')}`;

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(() => {
    prismaService.clearTestData();
    jest.clearAllMocks();
  });

  describe('POST /admin/workflow-steps', () => {
    it('should create workflow step for admin', () => {
      const mockWorkflowStep = {
        id: 'step-1',
        name: 'Document Review',
        description: 'Review and validate all submitted documents',
        order: 1,
        estimatedDuration: 5,
        serviceTypeId: 'cld123abc456def789',
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: mockServiceType,
      };

      prismaService.service_type.findUnique.mockResolvedValue(mockServiceType);
      prismaService.workflow_step.create.mockResolvedValue(mockWorkflowStep);

      return request(app.getHttpServer())
        .post('/admin/workflow-steps')
        .set('Authorization', adminToken)
        .send({
          name: 'Document Review',
          description: 'Review and validate all submitted documents',
          order: 1,
          estimatedDuration: 5,
          serviceTypeId: 'cld123abc456def789',
        })
        .expect(201)
        .expect((res) => {
          expect(res.body.id).toBe('step-1');
          expect(res.body.name).toBe('Document Review');
          expect(res.body.order).toBe(1);
          expect(res.body.estimatedDuration).toBe(5);
          expect(res.body.serviceType.name).toBe(
            'Critical Skills Employment Permit',
          );
        });
    });

    it('should return 404 for invalid service type', () => {
      prismaService.service_type.findUnique.mockResolvedValue(null);

      return request(app.getHttpServer())
        .post('/admin/workflow-steps')
        .set('Authorization', adminToken)
        .send({
          name: 'Document Review',
          description: 'Review and validate all submitted documents',
          order: 1,
          estimatedDuration: 5,
          serviceTypeId: 'cld789abc123def456',
        })
        .expect(404);
    });

    it('should return 400 for invalid input', () => {
      return request(app.getHttpServer())
        .post('/admin/workflow-steps')
        .set('Authorization', adminToken)
        .send({
          // Missing required fields
          name: '',
          description: '',
        })
        .expect(400);
    });

    it('should return 403 for user', () => {
      return request(app.getHttpServer())
        .post('/admin/workflow-steps')
        .set('Authorization', userToken)
        .send({
          name: 'Document Review',
          description: 'Review and validate all submitted documents',
          order: 1,
          estimatedDuration: 5,
          serviceTypeId: 'cld123abc456def789',
        })
        .expect(403);
    });

    it('should return 403 for agent', () => {
      return request(app.getHttpServer())
        .post('/admin/workflow-steps')
        .set('Authorization', agentToken)
        .send({
          name: 'Document Review',
          description: 'Review and validate all submitted documents',
          order: 1,
          estimatedDuration: 5,
          serviceTypeId: 'cld123abc456def789',
        })
        .expect(403);
    });

    it('should return 401 without authentication', () => {
      return request(app.getHttpServer())
        .post('/admin/workflow-steps')
        .send({
          name: 'Document Review',
          description: 'Review and validate all submitted documents',
          order: 1,
          estimatedDuration: 5,
          serviceTypeId: 'cld123abc456def789',
        })
        .expect(401);
    });
  });

  describe('GET /admin/workflow-steps/service-type/:serviceTypeId', () => {
    it('should return workflow steps for service type (admin)', () => {
      const mockWorkflowSteps = [
        {
          id: 'step-1',
          name: 'Document Review',
          description: 'Review and validate all submitted documents',
          order: 1,
          estimatedDuration: 5,
          serviceTypeId: 'cld123abc456def789',
          createdAt: new Date(),
          updatedAt: new Date(),
          serviceType: mockServiceType,
        },
        {
          id: 'step-2',
          name: 'Application Processing',
          description: 'Process the application through government systems',
          order: 2,
          estimatedDuration: 10,
          serviceTypeId: 'cld123abc456def789',
          createdAt: new Date(),
          updatedAt: new Date(),
          serviceType: mockServiceType,
        },
      ];

      prismaService.workflow_step.findMany.mockResolvedValue(mockWorkflowSteps);

      return request(app.getHttpServer())
        .get('/admin/workflow-steps/service-type/cld123abc456def789')
        .set('Authorization', adminToken)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveLength(2);
          expect(res.body[0].name).toBe('Document Review');
          expect(res.body[0].order).toBe(1);
          expect(res.body[1].name).toBe('Application Processing');
          expect(res.body[1].order).toBe(2);
        });
    });

    it('should return empty array for service type with no workflow steps', () => {
      prismaService.workflow_step.findMany.mockResolvedValue([]);

      return request(app.getHttpServer())
        .get('/admin/workflow-steps/service-type/cld123abc456def789')
        .set('Authorization', adminToken)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveLength(0);
        });
    });

    it('should return 403 for user', () => {
      return request(app.getHttpServer())
        .get('/admin/workflow-steps/service-type/cld123abc456def789')
        .set('Authorization', userToken)
        .expect(403);
    });

    it('should return 403 for agent', () => {
      return request(app.getHttpServer())
        .get('/admin/workflow-steps/service-type/cld123abc456def789')
        .set('Authorization', agentToken)
        .expect(403);
    });

    it('should return 401 without authentication', () => {
      return request(app.getHttpServer())
        .get('/admin/workflow-steps/service-type/cld123abc456def789')
        .expect(401);
    });
  });

  describe('Workflow Step Ordering', () => {
    it('should return workflow steps in correct order', () => {
      const mockWorkflowSteps = [
        {
          id: 'step-3',
          name: 'Final Review',
          description: 'Final review before approval',
          order: 3,
          estimatedDuration: 2,
          serviceTypeId: 'cld123abc456def789',
          createdAt: new Date(),
          updatedAt: new Date(),
          serviceType: mockServiceType,
        },
        {
          id: 'step-1',
          name: 'Document Review',
          description: 'Review and validate all submitted documents',
          order: 1,
          estimatedDuration: 5,
          serviceTypeId: 'cld123abc456def789',
          createdAt: new Date(),
          updatedAt: new Date(),
          serviceType: mockServiceType,
        },
        {
          id: 'step-2',
          name: 'Application Processing',
          description: 'Process the application through government systems',
          order: 2,
          estimatedDuration: 10,
          serviceTypeId: 'cld123abc456def789',
          createdAt: new Date(),
          updatedAt: new Date(),
          serviceType: mockServiceType,
        },
      ];

      // Mock will sort by order automatically
      const sortedSteps = [...mockWorkflowSteps].sort(
        (a, b) => a.order - b.order,
      );
      prismaService.workflow_step.findMany.mockResolvedValue(sortedSteps);

      return request(app.getHttpServer())
        .get('/admin/workflow-steps/service-type/cld123abc456def789')
        .set('Authorization', adminToken)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveLength(3);
          expect(res.body[0].order).toBe(1);
          expect(res.body[0].name).toBe('Document Review');
          expect(res.body[1].order).toBe(2);
          expect(res.body[1].name).toBe('Application Processing');
          expect(res.body[2].order).toBe(3);
          expect(res.body[2].name).toBe('Final Review');
        });
    });
  });

  describe('Validation Tests', () => {
    it('should validate required fields', () => {
      return request(app.getHttpServer())
        .post('/admin/workflow-steps')
        .set('Authorization', adminToken)
        .send({
          // Missing all required fields
        })
        .expect(400);
    });

    it('should validate field types', () => {
      return request(app.getHttpServer())
        .post('/admin/workflow-steps')
        .set('Authorization', adminToken)
        .send({
          name: 123, // Should be string
          description: 'Valid description',
          order: 'invalid', // Should be number
          estimatedDuration: 'invalid', // Should be number
          serviceTypeId: 'cld123abc456def789',
        })
        .expect(400);
    });

    it('should return 404 for non-existent serviceTypeId', () => {
      return request(app.getHttpServer())
        .post('/admin/workflow-steps')
        .set('Authorization', adminToken)
        .send({
          name: 'Document Review',
          description: 'Review and validate all submitted documents',
          order: 1,
          estimatedDuration: 5,
          serviceTypeId: 'invalid-service-type-id',
        })
        .expect(404);
    });
  });
});
