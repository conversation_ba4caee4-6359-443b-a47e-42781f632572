#!/usr/bin/env node

/**
 * E2E Test Runner Sc<PERSON>t
 * 
 * Comprehensive test runner for the Career Ireland E2E test suite.
 * Provides options for running specific test suites, generating reports,
 * and integrating with CI/CD pipelines.
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  testDir: 'e2e',
  outputDir: 'e2e/output',
  jestConfig: 'test/jest-e2e.json',
  roles: ['admin', 'user', 'agent'],
  defaultTimeout: 60000, // 60 seconds
};

// Command line argument parsing
const args = process.argv.slice(2);
const options = {
  role: null,
  suite: null,
  verbose: false,
  coverage: false,
  watch: false,
  generateReport: true,
  ci: false,
};

// Parse command line arguments
for (let i = 0; i < args.length; i++) {
  const arg = args[i];
  
  switch (arg) {
    case '--role':
      options.role = args[++i];
      break;
    case '--suite':
      options.suite = args[++i];
      break;
    case '--verbose':
      options.verbose = true;
      break;
    case '--coverage':
      options.coverage = true;
      break;
    case '--watch':
      options.watch = true;
      break;
    case '--no-report':
      options.generateReport = false;
      break;
    case '--ci':
      options.ci = true;
      break;
    case '--help':
      showHelp();
      process.exit(0);
    default:
      if (arg.startsWith('--')) {
        console.warn(`Unknown option: ${arg}`);
      }
  }
}

/**
 * Show help information
 */
function showHelp() {
  console.log(`
E2E Test Runner for Career Ireland

Usage: node scripts/run-e2e-tests.js [options]

Options:
  --role <role>      Run tests for specific role (admin, user, agent)
  --suite <name>     Run specific test suite
  --verbose          Enable verbose output
  --coverage         Generate coverage report
  --watch            Watch mode for development
  --no-report        Skip report generation
  --ci               CI mode (structured output)
  --help             Show this help message

Examples:
  node scripts/run-e2e-tests.js                    # Run all E2E tests
  node scripts/run-e2e-tests.js --role admin       # Run admin tests only
  node scripts/run-e2e-tests.js --suite packages   # Run package-related tests
  node scripts/run-e2e-tests.js --verbose --ci     # Verbose output for CI

Roles:
  admin    - Package creation, application prioritization
  user     - Registration, package purchase, document upload
  agent    - Document review and approval workflows
`);
}

/**
 * Ensure output directory exists
 */
function ensureOutputDirectory() {
  if (!fs.existsSync(CONFIG.outputDir)) {
    fs.mkdirSync(CONFIG.outputDir, { recursive: true });
  }
  
  const logsDir = path.join(CONFIG.outputDir, 'logs');
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }
}

/**
 * Build Jest command arguments
 */
function buildJestArgs() {
  const jestArgs = [
    '--config', CONFIG.jestConfig,
    '--testTimeout', CONFIG.defaultTimeout.toString(),
  ];

  // Add test path pattern based on role or suite
  if (options.role) {
    if (!CONFIG.roles.includes(options.role)) {
      console.error(`Invalid role: ${options.role}. Valid roles: ${CONFIG.roles.join(', ')}`);
      process.exit(1);
    }
    jestArgs.push('--testPathPattern', `e2e/${options.role}`);
  } else if (options.suite) {
    jestArgs.push('--testPathPattern', options.suite);
  } else {
    jestArgs.push('--testPathPattern', 'e2e/');
  }

  // Add other options
  if (options.verbose) {
    jestArgs.push('--verbose');
  }
  
  if (options.coverage) {
    jestArgs.push('--coverage');
  }
  
  if (options.watch) {
    jestArgs.push('--watch');
  }
  
  if (options.ci) {
    jestArgs.push('--ci', '--json', '--outputFile', path.join(CONFIG.outputDir, 'jest-results.json'));
  }

  return jestArgs;
}

/**
 * Run Jest tests
 */
function runTests() {
  return new Promise((resolve, reject) => {
    const jestArgs = buildJestArgs();
    
    console.log('🚀 Starting E2E Tests...');
    console.log(`📁 Test Directory: ${CONFIG.testDir}`);
    console.log(`⚙️  Configuration: ${CONFIG.jestConfig}`);
    
    if (options.role) {
      console.log(`👤 Role Filter: ${options.role}`);
    }
    if (options.suite) {
      console.log(`📋 Suite Filter: ${options.suite}`);
    }
    
    console.log(`🔧 Jest Args: ${jestArgs.join(' ')}\n`);

    const jest = spawn('npx', ['jest', ...jestArgs], {
      stdio: 'inherit',
      shell: true,
    });

    jest.on('close', (code) => {
      if (code === 0) {
        console.log('\n✅ All tests completed successfully!');
        resolve(code);
      } else {
        console.log(`\n❌ Tests failed with exit code ${code}`);
        resolve(code); // Don't reject, we want to generate reports even on failure
      }
    });

    jest.on('error', (error) => {
      console.error('❌ Failed to start Jest:', error);
      reject(error);
    });
  });
}

/**
 * Generate test summary from results
 */
function generateTestSummary() {
  try {
    const resultFiles = fs.readdirSync(CONFIG.outputDir)
      .filter(file => file.startsWith('test-results-') && file.endsWith('.json'))
      .sort()
      .reverse(); // Get most recent first

    if (resultFiles.length === 0) {
      console.log('⚠️  No test result files found');
      return null;
    }

    const latestResultFile = path.join(CONFIG.outputDir, resultFiles[0]);
    const results = JSON.parse(fs.readFileSync(latestResultFile, 'utf8'));
    
    return results;
  } catch (error) {
    console.error('❌ Failed to read test results:', error.message);
    return null;
  }
}

/**
 * Display test summary
 */
function displaySummary(results) {
  if (!results) {
    return;
  }

  console.log('\n📊 Test Summary');
  console.log('================');
  console.log(`🏃 Run ID: ${results.runId}`);
  console.log(`🌍 Environment: ${results.environment}`);
  console.log(`⏱️  Duration: ${(results.duration / 1000).toFixed(2)}s`);
  console.log(`📅 Timestamp: ${new Date(results.endTime).toLocaleString()}`);
  console.log('');
  console.log(`📋 Test Suites: ${results.totalSuites}`);
  console.log(`🧪 Total Tests: ${results.totalTests}`);
  console.log(`✅ Passed: ${results.passedTests}`);
  console.log(`❌ Failed: ${results.failedTests}`);
  console.log(`⏭️  Skipped: ${results.skippedTests}`);
  
  if (results.totalTests > 0) {
    const passRate = ((results.passedTests / results.totalTests) * 100).toFixed(1);
    console.log(`📈 Pass Rate: ${passRate}%`);
  }

  // Show suite breakdown
  if (results.suites && results.suites.length > 0) {
    console.log('\n📂 Suite Breakdown:');
    results.suites.forEach(suite => {
      const suitePassRate = suite.totalTests > 0 
        ? ((suite.passedTests / suite.totalTests) * 100).toFixed(1)
        : '0';
      console.log(`  ${suite.suiteName}: ${suite.passedTests}/${suite.totalTests} (${suitePassRate}%)`);
    });
  }

  // Show failed tests
  if (results.failedTests > 0) {
    console.log('\n❌ Failed Tests:');
    results.suites.forEach(suite => {
      const failedTests = suite.tests.filter(test => test.status === 'FAIL');
      if (failedTests.length > 0) {
        console.log(`  ${suite.suiteName}:`);
        failedTests.forEach(test => {
          console.log(`    - ${test.testName}`);
          if (test.error) {
            console.log(`      Error: ${test.error}`);
          }
        });
      }
    });
  }

  console.log(`\n📄 Detailed results: ${CONFIG.outputDir}/result-summary.json`);
  console.log(`🌐 HTML report: ${CONFIG.outputDir}/test-report.html`);
}

/**
 * Main execution function
 */
async function main() {
  try {
    // Ensure output directory exists
    ensureOutputDirectory();
    
    // Run tests
    const exitCode = await runTests();
    
    // Generate and display summary if requested
    if (options.generateReport && !options.watch) {
      console.log('\n📊 Generating test summary...');
      
      // Wait a moment for files to be written
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const results = generateTestSummary();
      displaySummary(results);
    }
    
    // Exit with the same code as Jest
    process.exit(exitCode);
    
  } catch (error) {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  }
}

// Handle process signals
process.on('SIGINT', () => {
  console.log('\n⏹️  Test run interrupted');
  process.exit(130);
});

process.on('SIGTERM', () => {
  console.log('\n⏹️  Test run terminated');
  process.exit(143);
});

// Run the main function
main();
