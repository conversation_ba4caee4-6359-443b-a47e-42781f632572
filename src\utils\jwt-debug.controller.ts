import {
  Body,
  Controller,
  Post,
  Get,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ApiOperation, ApiTags, ApiBody, ApiResponse } from '@nestjs/swagger';

/**
 * Debug controller for JWT tokens
 *
 * This controller provides endpoints for debugging JWT token issues.
 * It should only be used in development environments.
 */
@ApiTags('jwt-debug')
@Controller('jwt-debug')
export class JwtDebugController {
  constructor(private jwtService: JwtService) {}

  /**
   * Debug endpoint to verify a JWT token
   *
   * This endpoint takes a JWT token and attempts to verify it with different secret keys.
   * It returns information about which secret key was able to verify the token.
   *
   * @param body - The request body containing the JWT token
   * @returns Information about the token verification
   */
  @Post('verify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify a JWT token',
    description: 'Verify a JWT token with different secret keys',
  })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['token'],
      properties: {
        token: {
          type: 'string',
          description: 'JWT token to verify',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token verification results',
  })
  async verifyToken(@Body() body: { token: string }) {
    const { token } = body;
    const results = {
      token,
      userVerification: null,
      adminVerification: null,
      agentVerification: null,
      mentorVerification: null,
      refreshVerification: null,
      otpVerification: null,
      errors: {},
    };

    try {
      results.userVerification = await this.jwtService.verifyAsync(token, {
        secret: process.env.jwtSecretKey,
      });
    } catch (error) {
      results.errors['user'] = error.message;
    }

    try {
      results.adminVerification = await this.jwtService.verifyAsync(token, {
        secret: process.env.jwtAdminSecretKey,
      });
    } catch (error) {
      results.errors['admin'] = error.message;
    }

    try {
      results.agentVerification = await this.jwtService.verifyAsync(token, {
        secret: process.env.jwtAgentSecretKey,
      });
    } catch (error) {
      results.errors['agent'] = error.message;
    }

    try {
      results.mentorVerification = await this.jwtService.verifyAsync(token, {
        secret: process.env.jwtMentorSecretKey,
      });
    } catch (error) {
      results.errors['mentor'] = error.message;
    }

    try {
      results.refreshVerification = await this.jwtService.verifyAsync(token, {
        secret: process.env.jwtRefreshTokenKey,
      });
    } catch (error) {
      results.errors['refresh'] = error.message;
    }

    try {
      results.otpVerification = await this.jwtService.verifyAsync(token, {
        secret: process.env.jwtOtpSecretKey,
      });
    } catch (error) {
      results.errors['otp'] = error.message;
    }

    return results;
  }

  /**
   * Check if JWT environment variables are set
   *
   * This endpoint checks if all the JWT-related environment variables are set.
   * It returns information about which environment variables are set and their values (masked).
   *
   * @returns Information about JWT environment variables
   */
  @Get('env-check')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Check JWT environment variables',
    description: 'Check if all JWT-related environment variables are set',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Environment variables check results',
  })
  async checkEnvVars() {
    const maskSecret = (secret: string | undefined) => {
      if (!secret) return null;
      if (secret.length <= 8) return '********';
      return (
        secret.substring(0, 4) +
        '********' +
        secret.substring(secret.length - 4)
      );
    };

    return {
      jwtSecretKey: {
        set: !!process.env.jwtSecretKey,
        value: maskSecret(process.env.jwtSecretKey),
      },
      jwtAdminSecretKey: {
        set: !!process.env.jwtAdminSecretKey,
        value: maskSecret(process.env.jwtAdminSecretKey),
      },
      jwtAgentSecretKey: {
        set: !!process.env.jwtAgentSecretKey,
        value: maskSecret(process.env.jwtAgentSecretKey),
      },
      jwtMentorSecretKey: {
        set: !!process.env.jwtMentorSecretKey,
        value: maskSecret(process.env.jwtMentorSecretKey),
      },
      jwtRefreshTokenKey: {
        set: !!process.env.jwtRefreshTokenKey,
        value: maskSecret(process.env.jwtRefreshTokenKey),
      },
      jwtOtpSecretKey: {
        set: !!process.env.jwtOtpSecretKey,
        value: maskSecret(process.env.jwtOtpSecretKey),
      },
      ACCESS_TOKEN_EXPIRY_DATE: {
        set: !!process.env.ACCESS_TOKEN_EXPIRY_DATE,
        value: process.env.ACCESS_TOKEN_EXPIRY_DATE,
      },
      REFRESH_TOKEN_EXPIRY_DATE: {
        set: !!process.env.REFRESH_TOKEN_EXPIRY_DATE,
        value: process.env.REFRESH_TOKEN_EXPIRY_DATE,
      },
    };
  }
}
