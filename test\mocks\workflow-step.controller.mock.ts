import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  UseGuards,
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ApplicationService } from '../../src/immigration/application.service';
import { CreateWorkflowStepDto } from '../../src/immigration/dto/application.dto';

/**
 * Mock Workflow Step Controller for E2E testing
 *
 * This mock controller implements the same endpoints as the real WorkflowStepController
 * but uses simplified authentication for testing purposes.
 */

// Mock admin guard that handles real JWT tokens
@Injectable()
class MockJwtAdmin implements CanActivate {
  constructor(private jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedException('No bearer token provided');
    }

    const token = authHeader.substring(7);

    try {
      // Decode the JWT token to get the role
      const payload = await this.jwtService.verifyAsync(token, {
        secret: 'test-secret-key-for-e2e-testing',
      });

      if (payload.role === 'admin') {
        request['user'] = payload;
        request['userRole'] = 'admin';
        return true;
      } else if (payload.role === 'user' || payload.role === 'agent') {
        // Valid token but wrong role - return 403
        throw new ForbiddenException('Admin access required');
      } else {
        // Invalid role - return 401
        throw new UnauthorizedException('Invalid role');
      }
    } catch (error) {
      // Check if it's a ForbiddenException (re-throw it)
      if (error instanceof ForbiddenException) {
        throw error;
      }
      // JWT verification failed - return 401
      throw new UnauthorizedException('Invalid token');
    }
  }
}

@Controller('admin/workflow-steps')
export class WorkflowStepController {
  constructor(private readonly applicationService: ApplicationService) {}

  @UseGuards(MockJwtAdmin)
  @Post()
  async createWorkflowStep(
    @Body() createWorkflowStepDto: CreateWorkflowStepDto,
  ) {
    return this.applicationService.createWorkflowStep(createWorkflowStepDto);
  }

  @UseGuards(MockJwtAdmin)
  @Get('service-type/:serviceTypeId')
  async getWorkflowSteps(@Param('serviceTypeId') serviceTypeId: string) {
    return this.applicationService.getWorkflowSteps(serviceTypeId);
  }
}
