/**
 * Mock implementation of PrismaService for testing purposes
 * This prevents tests from connecting to the actual database
 */
import { Injectable } from '@nestjs/common';
import { VerificationStatus } from '@prisma/client';

@Injectable()
export class MockPrismaService {
  // In-memory storage for mock data
  private documents = new Map();
  private users = new Map();
  private serviceTypes = new Map();
  private documentRequirements = new Map();
  private applications = new Map();
  private packagesData = new Map(); // Renamed to avoid conflict
  private workflows = new Map();
  private workflowSteps = new Map();
  private checkpointCalls = new Map();
  private applicationQueries = new Map();
  private idCounter = 1;

  constructor() {
    this.setupTestData();
  }

  private setupTestData() {
    // Add test packages
    const testPackages = [
      {
        id: 'pkg-critical-skills-1',
        name: 'Critical Skills Employment Permit',
        note: 'Fast-track employment permit for skilled professionals in high-demand sectors',
        amount: 1500,
        order: 1,
        service: [
          'Document review and verification',
          'Application preparation and submission',
          'Priority processing',
          'Status tracking and updates',
          'Interview preparation (if required)',
        ],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'pkg-general-employment-1',
        name: 'General Employment Permit',
        note: 'Standard employment permit for various job categories',
        amount: 1200,
        order: 2,
        service: [
          'Document review and verification',
          'Application preparation and submission',
          'Standard processing',
          'Status tracking and updates',
        ],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'pkg-dependent-visa-1',
        name: 'Dependent/Partner Visa',
        note: 'Visa for family members of permit holders',
        amount: 800,
        order: 3,
        service: [
          'Relationship documentation review',
          'Application preparation',
          'Supporting document compilation',
          'Status tracking',
        ],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    testPackages.forEach((pkg) => {
      this.packagesData.set(pkg.id, pkg);
    });

    // Add test users
    this.users.set('user-1', {
      id: 'user-1',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'USER',
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    this.users.set('admin-1', {
      id: 'admin-1',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'ADMIN',
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    this.users.set('agent-1', {
      id: 'agent-1',
      name: 'Agent User',
      email: '<EMAIL>',
      role: 'AGENT',
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Add test service types (using CUID format)
    this.serviceTypes.set('cld123abc456def789', {
      id: 'cld123abc456def789',
      name: 'Critical Skills Employment Permit',
      description: 'Test service type',
      price: 1000,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    this.serviceTypes.set('cld789abc123def456', {
      id: 'cld789abc123def456',
      name: 'General Employment Permit',
      description: 'Another test service type',
      price: 800,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Add test application (using CUID format)
    this.applications.set('cld456def789abc123', {
      id: 'cld456def789abc123',
      userId: 'user-1', // Match the test expectation
      serviceTypeId: 'cld123abc456def789',
      status: 'DRAFT',
      submissionDate: null,
      completionDate: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }

  // Mock user model
  user = {
    findUnique: jest.fn(async (params) => {
      if (params.where.email) {
        return (
          Array.from(this.users.values()).find(
            (user) => user.email === params.where.email,
          ) || null
        );
      }
      return this.users.get(params.where.id) || null;
    }),
    create: jest.fn(async (params) => {
      const id = `user-${this.idCounter++}`;
      const user = {
        id,
        ...params.data,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      this.users.set(id, user);
      return user;
    }),
    update: jest.fn(async (params) => {
      const existing = this.users.get(params.where.id);
      if (!existing) return null;

      const updated = {
        ...existing,
        ...params.data,
        updatedAt: new Date(),
      };
      this.users.set(params.where.id, updated);
      return updated;
    }),
  };

  // Mock packages model
  packages = {
    create: jest.fn(async (params) => {
      const id = `pkg-${this.idCounter++}`;
      const packageData = {
        id,
        ...params.data,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      this.packagesData.set(id, packageData);
      return packageData;
    }),
    findUnique: jest.fn(async (params) => {
      return this.packagesData.get(params.where.id) || null;
    }),
    findMany: jest.fn(async (params) => {
      let results = Array.from(this.packagesData.values());

      // Apply filters if provided
      if (params?.where) {
        results = results.filter((pkg) => {
          let match = true;

          if (params.where.name?.contains) {
            match =
              match &&
              pkg.name
                .toLowerCase()
                .includes(params.where.name.contains.toLowerCase());
          }

          if (params.where.amount) {
            if (params.where.amount.gte !== undefined) {
              match = match && pkg.amount >= params.where.amount.gte;
            }
            if (params.where.amount.lte !== undefined) {
              match = match && pkg.amount <= params.where.amount.lte;
            }
          }

          return match;
        });
      }

      // Apply ordering
      if (params?.orderBy) {
        if (params.orderBy.order) {
          results.sort((a, b) => {
            if (params.orderBy.order === 'asc') {
              return a.order - b.order;
            }
            return b.order - a.order;
          });
        }
        if (params.orderBy.amount) {
          results.sort((a, b) => {
            if (params.orderBy.amount === 'asc') {
              return a.amount - b.amount;
            }
            return b.amount - a.amount;
          });
        }
        if (params.orderBy.name) {
          results.sort((a, b) => {
            if (params.orderBy.name === 'asc') {
              return a.name.localeCompare(b.name);
            }
            return b.name.localeCompare(a.name);
          });
        }
      }

      // Apply pagination
      if (params?.skip !== undefined && params?.take !== undefined) {
        results = results.slice(params.skip, params.skip + params.take);
      }

      return results;
    }),
    count: jest.fn(async (params) => {
      let results = Array.from(this.packagesData.values());

      if (params?.where) {
        results = results.filter((pkg) => {
          let match = true;

          if (params.where.name?.contains) {
            match =
              match &&
              pkg.name
                .toLowerCase()
                .includes(params.where.name.contains.toLowerCase());
          }

          if (params.where.amount) {
            if (params.where.amount.gte !== undefined) {
              match = match && pkg.amount >= params.where.amount.gte;
            }
            if (params.where.amount.lte !== undefined) {
              match = match && pkg.amount <= params.where.amount.lte;
            }
          }

          return match;
        });
      }

      return results.length;
    }),
    update: jest.fn(async (params) => {
      const existing = this.packagesData.get(params.where.id);
      if (!existing) return null;

      const updated = {
        ...existing,
        ...params.data,
        updatedAt: new Date(),
      };
      this.packagesData.set(params.where.id, updated);
      return updated;
    }),
    delete: jest.fn(async (params) => {
      const packageData = this.packagesData.get(params.where.id);
      if (packageData) {
        this.packagesData.delete(params.where.id);
      }
      return packageData;
    }),
  };

  // Mock document model
  document = {
    create: jest.fn(async (params) => {
      const id = `doc-${this.idCounter++}`;
      const document = {
        id,
        ...params.data,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      this.documents.set(id, document);
      return document;
    }),
    findUnique: jest.fn(async (params) => {
      const document = this.documents.get(params.where.id);
      if (!document) return null;

      let result = { ...document };

      if (params?.include?.user) {
        result.user = this.users.get(document.userId) || {
          id: document.userId,
          name: 'Mock User',
          email: '<EMAIL>',
        };
      }

      return result;
    }),
    findMany: jest.fn(async (params) => {
      let results = Array.from(this.documents.values());

      // Apply filters if provided
      if (params?.where) {
        results = results.filter((doc) => {
          let match = true;

          if (params.where.userId && doc.userId !== params.where.userId) {
            match = false;
          }

          if (params.where.status && doc.status !== params.where.status) {
            match = false;
          }

          if (params.where.mimeType && doc.mimeType !== params.where.mimeType) {
            match = false;
          }

          return match;
        });
      }

      // Apply ordering
      if (params?.orderBy) {
        if (params.orderBy.createdAt) {
          results.sort((a, b) => {
            if (params.orderBy.createdAt === 'desc') {
              return (
                new Date(b.createdAt).getTime() -
                new Date(a.createdAt).getTime()
              );
            }
            return (
              new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
            );
          });
        }
        if (params.orderBy.size) {
          results.sort((a, b) => {
            if (params.orderBy.size === 'asc') {
              return a.size - b.size;
            }
            return b.size - a.size;
          });
        }
      }

      // Apply pagination
      if (params?.skip !== undefined && params?.take !== undefined) {
        results = results.slice(params.skip, params.skip + params.take);
      }

      // Include related data if requested
      if (params?.include?.user) {
        results = results.map((doc) => ({
          ...doc,
          user: this.users.get(doc.userId) || {
            id: doc.userId,
            name: 'Mock User',
            email: '<EMAIL>',
          },
        }));
      }

      return results;
    }),
    count: jest.fn(async (params) => {
      let results = Array.from(this.documents.values());

      if (params?.where) {
        results = results.filter((doc) => {
          let match = true;

          if (params.where.userId && doc.userId !== params.where.userId) {
            match = false;
          }

          if (params.where.status && doc.status !== params.where.status) {
            match = false;
          }

          if (params.where.mimeType && doc.mimeType !== params.where.mimeType) {
            match = false;
          }

          return match;
        });
      }

      return results.length;
    }),
    update: jest.fn(async (params) => {
      const existing = this.documents.get(params.where.id);
      if (!existing) return null;

      const updated = {
        ...existing,
        ...params.data,
        updatedAt: new Date(),
      };

      let result = { ...updated };

      if (params?.include?.user) {
        result.user = this.users.get(updated.userId) || {
          id: updated.userId,
          name: 'Mock User',
          email: '<EMAIL>',
        };
      }

      this.documents.set(params.where.id, updated);
      return result;
    }),
    delete: jest.fn(async (params) => {
      const document = this.documents.get(params.where.id);
      if (document) {
        this.documents.delete(params.where.id);
      }
      return document;
    }),
  };

  // Mock user_document model
  user_document = {
    create: jest.fn(async (data) => {
      const id = `doc-${this.idCounter++}`;
      const document = {
        id,
        ...data.data,
        uploadDate: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      this.documents.set(id, document);
      return document;
    }),

    findUnique: jest.fn(async (params) => {
      return this.documents.get(params.where.id) || null;
    }),

    findMany: jest.fn(async (params) => {
      const results = Array.from(this.documents.values());

      // Apply filters if provided
      let filtered = results;

      if (params.where) {
        filtered = filtered.filter((doc) => {
          let match = true;

          // Filter by userId
          if (params.where.userId && doc.userId !== params.where.userId) {
            match = false;
          }

          // Filter by verificationStatus
          if (
            params.where.verificationStatus &&
            doc.verificationStatus !== params.where.verificationStatus
          ) {
            match = false;
          }

          // Filter by documentName (contains)
          if (
            params.where.documentName?.contains &&
            !doc.documentName
              .toLowerCase()
              .includes(params.where.documentName.contains.toLowerCase())
          ) {
            match = false;
          }

          return match;
        });
      }

      // Apply ordering if provided
      if (params.orderBy?.uploadDate === 'desc') {
        filtered.sort(
          (a, b) => b.uploadDate.getTime() - a.uploadDate.getTime(),
        );
      }

      // Include related data if requested
      if (params.include?.user) {
        filtered = filtered.map((doc) => ({
          ...doc,
          user: this.users.get(doc.userId) || {
            id: doc.userId,
            name: 'Mock User',
            email: '<EMAIL>',
          },
        }));
      }

      return filtered;
    }),

    update: jest.fn(async (params) => {
      const document = this.documents.get(params.where.id);
      if (!document) return null;

      const updatedDocument = {
        ...document,
        ...params.data,
        updatedAt: new Date(),
      };

      this.documents.set(params.where.id, updatedDocument);
      return updatedDocument;
    }),

    delete: jest.fn(async (params) => {
      const document = this.documents.get(params.where.id);
      if (!document) return null;

      this.documents.delete(params.where.id);
      return document;
    }),
  };

  // Helper method to seed test data
  seedTestData() {
    // Add some test users
    this.users.set('user-1', {
      id: 'user-1',
      name: 'Test User',
      email: '<EMAIL>',
    });

    this.users.set('admin-1', {
      id: 'admin-1',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'ADMIN',
    });

    // Add some test documents
    this.documents.set('doc-1', {
      id: 'doc-1',
      documentName: 'Passport',
      fileUrl: 'https://mock-storage.com/passport.pdf',
      uploadDate: new Date('2023-01-01'),
      verificationStatus: VerificationStatus.PENDING,
      userId: 'user-1',
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    });

    this.documents.set('doc-2', {
      id: 'doc-2',
      documentName: 'Visa',
      fileUrl: 'https://mock-storage.com/visa.pdf',
      uploadDate: new Date('2023-02-01'),
      verificationStatus: VerificationStatus.APPROVED,
      userId: 'user-1',
      createdAt: new Date('2023-02-01'),
      updatedAt: new Date('2023-02-15'),
    });

    // Add some test service types
    this.serviceTypes.set('service-type-1', {
      id: 'service-type-1',
      name: 'Critical Skills Employment Permit',
      description:
        'Permit for highly skilled professionals in eligible occupations',
      price: 1000,
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    });

    this.serviceTypes.set('service-type-2', {
      id: 'service-type-2',
      name: 'General Employment Permit',
      description: 'Permit for general employment in Ireland',
      price: 800,
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    });

    // Add some test document requirements
    this.documentRequirements.set('doc-req-1', {
      id: 'doc-req-1',
      name: 'Passport',
      description: 'Valid passport with at least 6 months validity',
      required: true,
      category: 'EMPLOYEE',
      serviceTypeId: 'service-type-1',
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    });

    this.documentRequirements.set('doc-req-2', {
      id: 'doc-req-2',
      name: 'Resume',
      description: 'Updated resume with work history',
      required: true,
      category: 'EMPLOYEE',
      serviceTypeId: 'service-type-1',
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    });

    this.documentRequirements.set('doc-req-3', {
      id: 'doc-req-3',
      name: 'Company Registration',
      description: 'Company registration certificate',
      required: true,
      category: 'EMPLOYER',
      serviceTypeId: 'service-type-2',
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    });
  }

  // Mock service_type model
  service_type = {
    create: jest.fn(async (data) => {
      const id = `service-type-${this.idCounter++}`;
      const serviceType = {
        id,
        ...data.data,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      this.serviceTypes.set(id, serviceType);
      return serviceType;
    }),

    findUnique: jest.fn(async (params) => {
      return this.serviceTypes.get(params.where.id) || null;
    }),

    findMany: jest.fn(async (params) => {
      const results = Array.from(this.serviceTypes.values());

      // Apply filters if provided
      let filtered = results;

      if (params.where) {
        filtered = filtered.filter((serviceType) => {
          let match = true;

          // Filter by name or description (contains)
          if (params.where.OR) {
            const searchConditions = params.where.OR;
            const nameMatch = searchConditions.some(
              (condition) =>
                condition.name?.contains &&
                serviceType.name
                  .toLowerCase()
                  .includes(condition.name.contains.toLowerCase()),
            );
            const descriptionMatch = searchConditions.some(
              (condition) =>
                condition.description?.contains &&
                serviceType.description
                  .toLowerCase()
                  .includes(condition.description.contains.toLowerCase()),
            );
            if (!(nameMatch || descriptionMatch)) {
              match = false;
            }
          }

          // Filter by price
          if (params.where.price) {
            if (
              params.where.price.gte !== undefined &&
              serviceType.price < params.where.price.gte
            ) {
              match = false;
            }
            if (
              params.where.price.lte !== undefined &&
              serviceType.price > params.where.price.lte
            ) {
              match = false;
            }
          }

          return match;
        });
      }

      // Apply pagination
      if (params.skip !== undefined && params.take !== undefined) {
        filtered = filtered.slice(params.skip, params.skip + params.take);
      }

      // Apply ordering
      if (params.orderBy?.name === 'asc') {
        filtered.sort((a, b) => a.name.localeCompare(b.name));
      }

      return filtered;
    }),

    update: jest.fn(async (params) => {
      const serviceType = this.serviceTypes.get(params.where.id);
      if (!serviceType) return null;

      const updatedServiceType = {
        ...serviceType,
        ...params.data,
        updatedAt: new Date(),
      };

      this.serviceTypes.set(params.where.id, updatedServiceType);
      return updatedServiceType;
    }),

    delete: jest.fn(async (params) => {
      const serviceType = this.serviceTypes.get(params.where.id);
      if (!serviceType) return null;

      this.serviceTypes.delete(params.where.id);
      return serviceType;
    }),

    count: jest.fn(async (params) => {
      const results = Array.from(this.serviceTypes.values());

      // Apply filters if provided
      let filtered = results;

      if (params?.where) {
        filtered = filtered.filter((serviceType) => {
          let match = true;

          // Filter by name or description (contains)
          if (params.where.OR) {
            const searchConditions = params.where.OR;
            const nameMatch = searchConditions.some(
              (condition) =>
                condition.name?.contains &&
                serviceType.name
                  .toLowerCase()
                  .includes(condition.name.contains.toLowerCase()),
            );
            const descriptionMatch = searchConditions.some(
              (condition) =>
                condition.description?.contains &&
                serviceType.description
                  .toLowerCase()
                  .includes(condition.description.contains.toLowerCase()),
            );
            if (!(nameMatch || descriptionMatch)) {
              match = false;
            }
          }

          // Filter by price
          if (params.where.price) {
            if (
              params.where.price.gte !== undefined &&
              serviceType.price < params.where.price.gte
            ) {
              match = false;
            }
            if (
              params.where.price.lte !== undefined &&
              serviceType.price > params.where.price.lte
            ) {
              match = false;
            }
          }

          return match;
        });
      }

      return filtered.length;
    }),
  };

  // Mock document_requirement model
  document_requirement = {
    create: jest.fn(async (data) => {
      const id = `doc-req-${this.idCounter++}`;
      const documentRequirement = {
        id,
        ...data.data,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      this.documentRequirements.set(id, documentRequirement);
      return documentRequirement;
    }),

    findUnique: jest.fn(async (params) => {
      const documentRequirement =
        this.documentRequirements.get(params.where.id) || null;

      if (documentRequirement && params.include?.serviceType) {
        const serviceType = this.serviceTypes.get(
          documentRequirement.serviceTypeId,
        ) || {
          id: documentRequirement.serviceTypeId,
          name: 'Mock Service Type',
        };

        return {
          ...documentRequirement,
          serviceType,
        };
      }

      return documentRequirement;
    }),

    findMany: jest.fn(async (params) => {
      const results = Array.from(this.documentRequirements.values());

      // Apply filters if provided
      let filtered = results;

      if (params?.where) {
        filtered = filtered.filter((docReq) => {
          let match = true;

          // Filter by serviceTypeId
          if (
            params.where.serviceTypeId &&
            docReq.serviceTypeId !== params.where.serviceTypeId
          ) {
            match = false;
          }

          // Filter by category
          if (
            params.where.category &&
            docReq.category !== params.where.category
          ) {
            match = false;
          }

          return match;
        });
      }

      // Apply ordering
      if (params?.orderBy?.name === 'asc') {
        filtered.sort((a, b) => a.name.localeCompare(b.name));
      }

      return filtered;
    }),

    update: jest.fn(async (params) => {
      const documentRequirement = this.documentRequirements.get(
        params.where.id,
      );
      if (!documentRequirement) return null;

      const updatedDocumentRequirement = {
        ...documentRequirement,
        ...params.data,
        updatedAt: new Date(),
      };

      this.documentRequirements.set(
        params.where.id,
        updatedDocumentRequirement,
      );
      return updatedDocumentRequirement;
    }),

    delete: jest.fn(async (params) => {
      const documentRequirement = this.documentRequirements.get(
        params.where.id,
      );
      if (!documentRequirement) return null;

      this.documentRequirements.delete(params.where.id);
      return documentRequirement;
    }),
  };

  // Mock application model
  application = {
    create: jest.fn(async (params) => {
      const id = `app_${this.idCounter++}`;
      const application = {
        id,
        userId: params.data.userId,
        serviceTypeId: params.data.serviceTypeId,
        status: params.data.status || 'DRAFT',
        submissionDate: params.data.submissionDate || null,
        completionDate: params.data.completionDate || null,
        createdAt: new Date(),
        updatedAt: new Date(),
        ...params.data,
      };

      // Add includes if requested
      if (params.include?.serviceType) {
        application.serviceType = this.serviceTypes.get(
          params.data.serviceTypeId,
        );
      }
      if (params.include?.user) {
        const user = this.users.get(params.data.userId);
        if (user && params.include.user.select) {
          application.user = {};
          Object.keys(params.include.user.select).forEach((key) => {
            if (params.include.user.select[key]) {
              application.user[key] = user[key];
            }
          });
        } else {
          application.user = user;
        }
      }
      if (params.include?.workflow) {
        application.workflow =
          Array.from(this.workflows.values()).find(
            (w) => w.applicationId === id,
          ) || null;
      }

      this.applications.set(id, application);
      return application;
    }),

    findMany: jest.fn(async (params) => {
      let results = Array.from(this.applications.values());

      // Apply filters if provided
      if (params?.where) {
        results = results.filter((app) => {
          let match = true;

          if (params.where.userId && app.userId !== params.where.userId) {
            match = false;
          }
          if (
            params.where.serviceTypeId &&
            app.serviceTypeId !== params.where.serviceTypeId
          ) {
            match = false;
          }
          if (params.where.status && app.status !== params.where.status) {
            match = false;
          }

          return match;
        });
      }

      // Apply pagination
      if (params?.skip) {
        results = results.slice(params.skip);
      }
      if (params?.take) {
        results = results.slice(0, params.take);
      }

      // Apply ordering
      if (params?.orderBy?.createdAt) {
        results.sort((a, b) => {
          if (params.orderBy.createdAt === 'desc') {
            return (
              new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
            );
          }
          return (
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );
        });
      }

      // Add includes if requested
      if (params?.include) {
        results = results.map((app) => {
          const result = { ...app };

          if (params.include.serviceType) {
            result.serviceType = this.serviceTypes.get(app.serviceTypeId);
            if (params.include.serviceType.include?.documentRequirements) {
              result.serviceType.documentRequirements = Array.from(
                this.documentRequirements.values(),
              ).filter((dr) => dr.serviceTypeId === app.serviceTypeId);
            }
            if (params.include.serviceType.include?.workflowSteps) {
              result.serviceType.workflowSteps = Array.from(
                this.workflowSteps.values(),
              )
                .filter((ws) => ws.serviceTypeId === app.serviceTypeId)
                .sort((a, b) => a.order - b.order);
            }
          }

          if (params.include.user) {
            const user = this.users.get(app.userId);
            if (user && params.include.user.select) {
              result.user = {};
              Object.keys(params.include.user.select).forEach((key) => {
                if (params.include.user.select[key]) {
                  result.user[key] = user[key];
                }
              });
            } else {
              result.user = user;
            }
          }

          if (params.include.workflow) {
            result.workflow =
              Array.from(this.workflows.values()).find(
                (w) => w.applicationId === app.id,
              ) || null;
          }

          if (params.include.checkpointCalls) {
            result.checkpointCalls = Array.from(this.checkpointCalls.values())
              .filter((cc) => cc.applicationId === app.id)
              .sort(
                (a, b) =>
                  new Date(b.scheduledDate).getTime() -
                  new Date(a.scheduledDate).getTime(),
              );
          }

          if (params.include.applicationQuery) {
            result.applicationQuery = Array.from(
              this.applicationQueries.values(),
            )
              .filter((aq) => aq.applicationId === app.id)
              .sort(
                (a, b) =>
                  new Date(b.queryDate).getTime() -
                  new Date(a.queryDate).getTime(),
              );
          }

          return result;
        });
      }

      return results;
    }),

    findUnique: jest.fn(async (params) => {
      const application = this.applications.get(params.where.id);
      if (!application) return null;

      let result = { ...application };

      // Add includes if requested
      if (params?.include) {
        if (params.include.serviceType) {
          result.serviceType = this.serviceTypes.get(application.serviceTypeId);
          if (params.include.serviceType.include?.documentRequirements) {
            result.serviceType.documentRequirements = Array.from(
              this.documentRequirements.values(),
            ).filter((dr) => dr.serviceTypeId === application.serviceTypeId);
          }
          if (params.include.serviceType.include?.workflowSteps) {
            result.serviceType.workflowSteps = Array.from(
              this.workflowSteps.values(),
            )
              .filter((ws) => ws.serviceTypeId === application.serviceTypeId)
              .sort((a, b) => a.order - b.order);
          }
        }

        if (params.include.user) {
          const user = this.users.get(application.userId);
          if (user && params.include.user.select) {
            result.user = {};
            Object.keys(params.include.user.select).forEach((key) => {
              if (params.include.user.select[key]) {
                result.user[key] = user[key];
              }
            });
          } else {
            result.user = user;
          }
        }

        if (params.include.workflow) {
          result.workflow =
            Array.from(this.workflows.values()).find(
              (w) => w.applicationId === application.id,
            ) || null;
        }

        if (params.include.checkpointCalls) {
          result.checkpointCalls = Array.from(this.checkpointCalls.values())
            .filter((cc) => cc.applicationId === application.id)
            .sort(
              (a, b) =>
                new Date(b.scheduledDate).getTime() -
                new Date(a.scheduledDate).getTime(),
            );
        }

        if (params.include.applicationQuery) {
          result.applicationQuery = Array.from(this.applicationQueries.values())
            .filter((aq) => aq.applicationId === application.id)
            .sort(
              (a, b) =>
                new Date(b.queryDate).getTime() -
                new Date(a.queryDate).getTime(),
            );
        }
      }

      return result;
    }),

    update: jest.fn(async (params) => {
      const existing = this.applications.get(params.where.id);
      if (!existing) return null;

      const updated = {
        ...existing,
        ...params.data,
        updatedAt: new Date(),
      };

      // Add includes if requested
      if (params?.include) {
        if (params.include.serviceType) {
          updated.serviceType = this.serviceTypes.get(updated.serviceTypeId);
        }
        if (params.include.user) {
          const user = this.users.get(updated.userId);
          if (user && params.include.user.select) {
            updated.user = {};
            Object.keys(params.include.user.select).forEach((key) => {
              if (params.include.user.select[key]) {
                updated.user[key] = user[key];
              }
            });
          } else {
            updated.user = user;
          }
        }
        if (params.include.workflow) {
          updated.workflow =
            Array.from(this.workflows.values()).find(
              (w) => w.applicationId === updated.id,
            ) || null;
        }
      }

      this.applications.set(params.where.id, updated);
      return updated;
    }),

    delete: jest.fn(async (params) => {
      const application = this.applications.get(params.where.id);
      if (application) {
        this.applications.delete(params.where.id);
      }
      return application;
    }),

    count: jest.fn(async (params) => {
      const results = Array.from(this.applications.values());

      // Apply filters if provided
      let filtered = results;

      if (params?.where) {
        filtered = filtered.filter((app) => {
          let match = true;

          // Filter by serviceTypeId
          if (
            params.where.serviceTypeId &&
            app.serviceTypeId !== params.where.serviceTypeId
          ) {
            match = false;
          }

          // Filter by status
          if (params.where.status && app.status !== params.where.status) {
            match = false;
          }

          // Filter by userId
          if (params.where.userId && app.userId !== params.where.userId) {
            match = false;
          }

          return match;
        });
      }

      return filtered.length;
    }),
  };

  // Mock application_workflow model
  application_workflow = {
    create: jest.fn(async (params) => {
      const id = `workflow_${this.idCounter++}`;
      const workflow = {
        id,
        currentStep: params.data.currentStep || 1,
        status: params.data.status || 'IN_PROGRESS',
        startDate: params.data.startDate || new Date(),
        lastUpdated: new Date(),
        applicationId: params.data.applicationId,
        createdAt: new Date(),
        updatedAt: new Date(),
        ...params.data,
      };
      this.workflows.set(id, workflow);
      return workflow;
    }),

    findUnique: jest.fn(async (params) => {
      return this.workflows.get(params.where.id) || null;
    }),

    update: jest.fn(async (params) => {
      const existing = this.workflows.get(params.where.id);
      if (!existing) return null;

      const updated = {
        ...existing,
        ...params.data,
        updatedAt: new Date(),
      };
      this.workflows.set(params.where.id, updated);
      return updated;
    }),

    delete: jest.fn(async (params) => {
      const workflow = this.workflows.get(params.where.id);
      if (workflow) {
        this.workflows.delete(params.where.id);
      }
      return workflow;
    }),
  };

  // Mock workflow_step model
  workflow_step = {
    create: jest.fn(async (params) => {
      const id = `step_${this.idCounter++}`;
      const step = {
        id,
        name: params.data.name,
        description: params.data.description,
        order: params.data.order,
        estimatedDuration: params.data.estimatedDuration,
        serviceTypeId: params.data.serviceTypeId,
        createdAt: new Date(),
        updatedAt: new Date(),
        ...params.data,
      };

      if (params.include?.serviceType) {
        step.serviceType = this.serviceTypes.get(params.data.serviceTypeId);
      }

      this.workflowSteps.set(id, step);
      return step;
    }),

    findMany: jest.fn(async (params) => {
      let results = Array.from(this.workflowSteps.values());

      if (params?.where?.serviceTypeId) {
        results = results.filter(
          (step) => step.serviceTypeId === params.where.serviceTypeId,
        );
      }

      if (params?.orderBy?.order) {
        results.sort((a, b) => {
          if (params.orderBy.order === 'asc') {
            return a.order - b.order;
          }
          return b.order - a.order;
        });
      }

      if (params?.include?.serviceType) {
        results = results.map((step) => ({
          ...step,
          serviceType: this.serviceTypes.get(step.serviceTypeId),
        }));
      }

      return results;
    }),

    findUnique: jest.fn(async (params) => {
      return this.workflowSteps.get(params.where.id) || null;
    }),
  };

  // Mock checkpoint_call model
  checkpoint_call = {
    create: jest.fn(async (params) => {
      const id = `call_${this.idCounter++}`;
      const call = {
        id,
        scheduledDate: new Date(params.data.scheduledDate),
        completedDate: params.data.completedDate
          ? new Date(params.data.completedDate)
          : null,
        notes: params.data.notes || null,
        status: params.data.status || 'SCHEDULED',
        applicationId: params.data.applicationId,
        createdAt: new Date(),
        updatedAt: new Date(),
        ...params.data,
      };

      if (params.include?.application) {
        call.application = this.applications.get(params.data.applicationId);
      }

      this.checkpointCalls.set(id, call);
      return call;
    }),

    findMany: jest.fn(async (params) => {
      let results = Array.from(this.checkpointCalls.values());

      if (params?.where?.applicationId) {
        results = results.filter(
          (call) => call.applicationId === params.where.applicationId,
        );
      }

      if (params?.orderBy?.scheduledDate) {
        results.sort((a, b) => {
          if (params.orderBy.scheduledDate === 'desc') {
            return (
              new Date(b.scheduledDate).getTime() -
              new Date(a.scheduledDate).getTime()
            );
          }
          return (
            new Date(a.scheduledDate).getTime() -
            new Date(b.scheduledDate).getTime()
          );
        });
      }

      if (params?.include?.application) {
        results = results.map((call) => ({
          ...call,
          application: this.applications.get(call.applicationId),
        }));
      }

      return results;
    }),

    findUnique: jest.fn(async (params) => {
      return this.checkpointCalls.get(params.where.id) || null;
    }),

    update: jest.fn(async (params) => {
      const existing = this.checkpointCalls.get(params.where.id);
      if (!existing) return null;

      const updated = {
        ...existing,
        ...params.data,
        updatedAt: new Date(),
      };

      if (params.include?.application) {
        updated.application = this.applications.get(updated.applicationId);
      }

      this.checkpointCalls.set(params.where.id, updated);
      return updated;
    }),
  };

  // Mock application_query model
  application_query = {
    create: jest.fn(async (params) => {
      const id = `query_${this.idCounter++}`;
      const query = {
        id,
        queryText: params.data.queryText,
        responseText: params.data.responseText || null,
        queryDate: params.data.queryDate || new Date(),
        responseDate: params.data.responseDate
          ? new Date(params.data.responseDate)
          : null,
        status: params.data.status || 'PENDING',
        applicationId: params.data.applicationId,
        createdAt: new Date(),
        updatedAt: new Date(),
        ...params.data,
      };

      if (params.include?.application) {
        query.application = this.applications.get(params.data.applicationId);
      }

      this.applicationQueries.set(id, query);
      return query;
    }),

    findMany: jest.fn(async (params) => {
      let results = Array.from(this.applicationQueries.values());

      if (params?.where?.applicationId) {
        results = results.filter(
          (query) => query.applicationId === params.where.applicationId,
        );
      }

      if (params?.orderBy?.queryDate) {
        results.sort((a, b) => {
          if (params.orderBy.queryDate === 'desc') {
            return (
              new Date(b.queryDate).getTime() - new Date(a.queryDate).getTime()
            );
          }
          return (
            new Date(a.queryDate).getTime() - new Date(b.queryDate).getTime()
          );
        });
      }

      if (params?.include?.application) {
        results = results.map((query) => ({
          ...query,
          application: this.applications.get(query.applicationId),
        }));
      }

      return results;
    }),

    findUnique: jest.fn(async (params) => {
      return this.applicationQueries.get(params.where.id) || null;
    }),

    update: jest.fn(async (params) => {
      const existing = this.applicationQueries.get(params.where.id);
      if (!existing) return null;

      const updated = {
        ...existing,
        ...params.data,
        updatedAt: new Date(),
      };

      if (params.include?.application) {
        updated.application = this.applications.get(updated.applicationId);
      }

      this.applicationQueries.set(params.where.id, updated);
      return updated;
    }),
  };

  // Mock method to clear all data (useful between tests)
  clearTestData() {
    this.documents.clear();
    this.users.clear();
    this.serviceTypes.clear();
    this.documentRequirements.clear();
    this.applications.clear();
    this.packagesData.clear();
    this.workflows.clear();
    this.workflowSteps.clear();
    this.checkpointCalls.clear();
    this.applicationQueries.clear();
    this.idCounter = 1;
  }
}
