import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Request,
  UseGuards,
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ApplicationService } from '../../src/immigration/application.service';
import {
  CreateApplicationDto,
  UpdateApplicationDto,
  FilterApplicationDto,
  UpdateWorkflowDto,
  CreateCheckpointCallDto,
  UpdateCheckpointCallDto,
  CreateApplicationQueryDto,
  RespondToQueryDto,
} from '../../src/immigration/dto/application.dto';

/**
 * Mock Application Controller for E2E testing
 *
 * This mock controller implements the same endpoints as the real ApplicationController
 * but uses simplified authentication for testing purposes.
 */

// Mock guard that handles real JWT tokens
@Injectable()
class MockJwtGuard implements CanActivate {
  constructor(private jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedException('No bearer token provided');
    }

    const token = authHeader.substring(7);

    try {
      // Decode the JWT token to get the role
      const payload = await this.jwtService.verifyAsync(token, {
        secret: 'test-secret-key-for-e2e-testing',
      });

      request['user'] = payload;
      request['userRole'] = payload.role;
      return true;
    } catch (error) {
      // JWT verification failed - return 401
      throw new UnauthorizedException('Invalid token');
    }
  }
}

// Mock admin guard that handles real JWT tokens
@Injectable()
class MockJwtAdmin implements CanActivate {
  constructor(private jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedException('No bearer token provided');
    }

    const token = authHeader.substring(7);

    try {
      // Decode the JWT token to get the role
      const payload = await this.jwtService.verifyAsync(token, {
        secret: 'test-secret-key-for-e2e-testing',
      });

      if (payload.role === 'admin') {
        request['user'] = payload;
        request['userRole'] = 'admin';
        return true;
      } else if (payload.role === 'user' || payload.role === 'agent') {
        // Valid token but wrong role - return 403
        throw new ForbiddenException('Admin access required');
      } else {
        // Invalid role - return 401
        throw new UnauthorizedException('Invalid role');
      }
    } catch (error) {
      // Check if it's a ForbiddenException (re-throw it)
      if (error instanceof ForbiddenException) {
        throw error;
      }
      // JWT verification failed - return 401
      throw new UnauthorizedException('Invalid token');
    }
  }
}

// Mock admin-agent guard that handles real JWT tokens
@Injectable()
class MockJwtAdminAgent implements CanActivate {
  constructor(private jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedException('No bearer token provided');
    }

    const token = authHeader.substring(7);

    try {
      // Decode the JWT token to get the role
      const payload = await this.jwtService.verifyAsync(token, {
        secret: 'test-secret-key-for-e2e-testing',
      });

      if (payload.role === 'admin') {
        request['user'] = payload;
        request['userRole'] = 'admin';
        return true;
      } else if (payload.role === 'agent') {
        request['user'] = payload;
        request['userRole'] = 'agent';
        return true;
      } else if (payload.role === 'user') {
        // Valid token but wrong role - return 403
        throw new ForbiddenException('Admin or agent access required');
      } else {
        // Invalid role - return 401
        throw new UnauthorizedException('Invalid role');
      }
    } catch (error) {
      // Check if it's a ForbiddenException (re-throw it)
      if (error instanceof ForbiddenException) {
        throw error;
      }
      // JWT verification failed - return 401
      throw new UnauthorizedException('Invalid token');
    }
  }
}

@Controller('applications')
export class ApplicationController {
  constructor(private readonly applicationService: ApplicationService) {}

  @UseGuards(MockJwtGuard)
  @Post()
  async create(
    @Body() createApplicationDto: CreateApplicationDto,
    @Request() req: any,
  ) {
    return this.applicationService.create(req.user.id, createApplicationDto);
  }

  @UseGuards(MockJwtAdminAgent)
  @Get()
  async findAll(@Query() filterDto: FilterApplicationDto, @Request() req: any) {
    return this.applicationService.findAll(
      filterDto,
      req.user.role,
      req.user.id,
    );
  }

  @UseGuards(MockJwtGuard)
  @Get('my-applications')
  async getMyApplications(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Request() req: any,
  ) {
    return this.applicationService.findAll(
      { page, limit },
      req.user.role,
      req.user.id,
    );
  }

  @UseGuards(MockJwtAdmin)
  @Get('statistics')
  async getStatistics() {
    return this.applicationService.getStatistics();
  }

  @UseGuards(MockJwtGuard)
  @Get(':id')
  async findOne(@Param('id') id: string, @Request() req: any) {
    return this.applicationService.findOne(id, req.user.role, req.user.id);
  }

  @UseGuards(MockJwtGuard)
  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateApplicationDto: UpdateApplicationDto,
    @Request() req: any,
  ) {
    return this.applicationService.update(
      id,
      updateApplicationDto,
      req.user.role,
      req.user.id,
    );
  }

  @UseGuards(MockJwtGuard)
  @Delete(':id')
  async remove(@Param('id') id: string, @Request() req: any) {
    return this.applicationService.remove(id, req.user.role, req.user.id);
  }

  @UseGuards(MockJwtGuard)
  @Get(':id/progress')
  async getProgress(@Param('id') id: string, @Request() req: any) {
    return this.applicationService.getProgress(id, req.user.role, req.user.id);
  }

  @UseGuards(MockJwtAdmin)
  @Patch(':id/workflow')
  async advanceWorkflow(
    @Param('id') id: string,
    @Body() updateWorkflowDto: UpdateWorkflowDto,
    @Request() req: any,
  ) {
    return this.applicationService.advanceWorkflow(
      id,
      updateWorkflowDto,
      req.user.role,
      req.user.id,
    );
  }

  @UseGuards(MockJwtAdmin)
  @Post('checkpoint-calls')
  async createCheckpointCall(
    @Body() createCheckpointCallDto: CreateCheckpointCallDto,
  ) {
    return this.applicationService.createCheckpointCall(
      createCheckpointCallDto,
    );
  }

  @UseGuards(MockJwtAdmin)
  @Patch('checkpoint-calls/:id')
  async updateCheckpointCall(
    @Param('id') id: string,
    @Body() updateCheckpointCallDto: UpdateCheckpointCallDto,
  ) {
    return this.applicationService.updateCheckpointCall(
      id,
      updateCheckpointCallDto,
    );
  }

  @UseGuards(MockJwtGuard)
  @Get(':id/checkpoint-calls')
  async getCheckpointCalls(@Param('id') id: string, @Request() req: any) {
    return this.applicationService.getCheckpointCalls(
      id,
      req.user.role,
      req.user.id,
    );
  }

  @UseGuards(MockJwtGuard)
  @Post('queries')
  async createQuery(
    @Body() createApplicationQueryDto: CreateApplicationQueryDto,
    @Request() req: any,
  ) {
    return this.applicationService.createQuery(
      createApplicationQueryDto,
      req.user.role,
      req.user.id,
    );
  }

  @UseGuards(MockJwtAdmin)
  @Patch('queries/:queryId/respond')
  async respondToQuery(
    @Param('queryId') queryId: string,
    @Body() respondToQueryDto: RespondToQueryDto,
  ) {
    return this.applicationService.respondToQuery(queryId, respondToQueryDto);
  }

  @UseGuards(MockJwtGuard)
  @Get(':id/queries')
  async getQueries(@Param('id') id: string, @Request() req: any) {
    return this.applicationService.getQueries(id, req.user.role, req.user.id);
  }
}
