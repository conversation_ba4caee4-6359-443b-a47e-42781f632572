import { Test, TestingModule } from '@nestjs/testing';
import { DocumentService } from '../src/document/document.service';
import { PrismaService } from '../src/utils/prisma.service';
import { MediaService } from '../src/media/media.service';
import {
  BadRequestException,
  ForbiddenException,
  NotFoundException,
} from '@nestjs/common';
import { VerificationStatus } from '@prisma/client';
import { MockPrismaService } from '../src/utils/prisma.service.mock';
import { MockMediaService } from '../src/media/media.service.mock';

/**
 * Test suite for DocumentService
 *
 * These tests use mock implementations of PrismaService and MediaService
 * to prevent hitting the production database or Supabase storage during testing.
 */
describe('DocumentService', () => {
  let service: DocumentService;
  let prismaService: MockPrismaService;
  let mediaService: MockMediaService;

  beforeEach(async () => {
    // Create fresh instances of mock services for each test
    const mockPrismaService = new MockPrismaService();
    const mockMediaService = new MockMediaService();

    // Seed some test data
    mockPrismaService.seedTestData();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: MediaService,
          useValue: mockMediaService,
        },
      ],
    }).compile();

    service = module.get<DocumentService>(DocumentService);
    prismaService = module.get<MockPrismaService>(PrismaService);
    mediaService = module.get<MockMediaService>(MediaService);

    // Set up spies on the mock services
    jest.spyOn(prismaService.user_document, 'create');
    jest.spyOn(prismaService.user_document, 'findUnique');
    jest.spyOn(prismaService.user_document, 'findMany');
    jest.spyOn(prismaService.user_document, 'update');
    jest.spyOn(prismaService.user_document, 'delete');
    jest.spyOn(mediaService, 'uploadFile');
  });

  afterEach(() => {
    // Clear test data between tests
    prismaService.clearTestData();
    jest.clearAllMocks();
  });

  describe('uploadDocument', () => {
    it('should upload a document successfully', async () => {
      // Arrange
      const userId = 'user-1';
      const file = {
        mimetype: 'application/pdf',
        size: 1024 * 1024, // 1MB
        buffer: Buffer.from('test'),
        originalname: 'test.pdf',
      } as Express.Multer.File;
      const dto = {
        documentName: 'Passport',
        expiryDate: '2025-12-31',
      };

      // Our mock services will handle the responses automatically
      // No need to manually mock resolved values

      // Act
      const result = await service.uploadDocument(userId, file, dto);

      // Assert
      expect(mediaService.uploadFile).toHaveBeenCalledWith(
        file,
        'immigration-documents',
      );
      expect(prismaService.user_document.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          documentName: dto.documentName,
          fileUrl: expect.any(String), // The mock service generates a dynamic URL
          verificationStatus: VerificationStatus.PENDING,
          expiryDate: expect.any(Date),
          userId,
        }),
        include: expect.any(Object), // The service includes related data
      });
      expect(result).toHaveProperty('id', expect.stringContaining('doc-'));
      expect(result).toHaveProperty('documentName', dto.documentName);
      expect(result).toHaveProperty(
        'fileUrl',
        expect.stringContaining('mock-supabase-storage.com'),
      );
      expect(result).toHaveProperty(
        'verificationStatus',
        VerificationStatus.PENDING,
      );
    });

    it('should throw BadRequestException for invalid file type', async () => {
      // Arrange
      const userId = 'user-1';
      const file = {
        mimetype: 'text/plain',
        size: 1024 * 1024,
        buffer: Buffer.from('test'),
        originalname: 'test.txt',
      } as Express.Multer.File;
      const dto = {
        documentName: 'Passport',
      };

      // Act & Assert
      await expect(service.uploadDocument(userId, file, dto)).rejects.toThrow(
        BadRequestException,
      );

      // Verify that neither the storage nor database was called
      expect(mediaService.uploadFile).not.toHaveBeenCalled();
      expect(prismaService.user_document.create).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException for file size exceeding limit', async () => {
      // Arrange
      const userId = 'user-1';
      const file = {
        mimetype: 'application/pdf',
        size: 20 * 1024 * 1024, // 20MB (exceeds 10MB limit)
        buffer: Buffer.from('test'),
        originalname: 'test.pdf',
      } as Express.Multer.File;
      const dto = {
        documentName: 'Passport',
      };

      // Act & Assert
      await expect(service.uploadDocument(userId, file, dto)).rejects.toThrow(
        BadRequestException,
      );

      // Verify that neither the storage nor database was called
      expect(mediaService.uploadFile).not.toHaveBeenCalled();
      expect(prismaService.user_document.create).not.toHaveBeenCalled();
    });
  });

  describe('getDocumentById', () => {
    it('should return a document when user has access', async () => {
      // Arrange
      const userId = 'user-1';
      const documentId = 'doc-1';

      // The document is already seeded in the mock database
      // with userId = 'user-1' and id = 'doc-1'

      // Act
      const result = await service.getDocumentById(userId, documentId);

      // Assert
      expect(prismaService.user_document.findUnique).toHaveBeenCalledWith({
        where: { id: documentId },
      });
      expect(result).toHaveProperty('id', documentId);
      expect(result).toHaveProperty('userId', userId);
    });

    it('should throw NotFoundException when document does not exist', async () => {
      // Arrange
      const userId = 'user-1';
      const documentId = 'non-existent-doc';

      // No need to mock, the document doesn't exist in our mock database

      // Act & Assert
      await expect(service.getDocumentById(userId, documentId)).rejects.toThrow(
        NotFoundException,
      );

      expect(prismaService.user_document.findUnique).toHaveBeenCalledWith({
        where: { id: documentId },
      });
    });

    it('should throw ForbiddenException when user does not have access', async () => {
      // Arrange
      const userId = 'different-user';
      const documentId = 'doc-1';

      // The document exists in the mock database but belongs to 'user-1'

      // Act & Assert
      await expect(service.getDocumentById(userId, documentId)).rejects.toThrow(
        ForbiddenException,
      );

      expect(prismaService.user_document.findUnique).toHaveBeenCalledWith({
        where: { id: documentId },
      });
    });

    it('should allow admin to access any document', async () => {
      // Arrange
      const userId = 'admin-1';
      const documentId = 'doc-1';

      // The document exists in the mock database but belongs to 'user-1'

      // Act
      const result = await service.getDocumentById(userId, documentId, true);

      // Assert
      expect(prismaService.user_document.findUnique).toHaveBeenCalledWith({
        where: { id: documentId },
      });
      expect(result).toHaveProperty('id', documentId);
      expect(result).toHaveProperty('userId', 'user-1'); // Document belongs to user-1
    });
  });

  // Add more tests for other methods...
});
