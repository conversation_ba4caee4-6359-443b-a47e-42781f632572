import { Test, TestingModule } from '@nestjs/testing';
import { DocumentRequirementService } from './document-requirement.service';
import { PrismaService } from '../utils/prisma.service';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { DocCategory } from '@prisma/client';
import { CreateDocumentRequirementDto, UpdateDocumentRequirementDto } from './dto/document-requirement.dto';

describe('DocumentRequirementService', () => {
  let service: DocumentRequirementService;
  let prismaService: PrismaService;

  const mockPrismaService = {
    document_requirement: {
      create: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    service_type: {
      findUnique: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentRequirementService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<DocumentRequirementService>(DocumentRequirementService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a document requirement', async () => {
      const createDto: CreateDocumentRequirementDto = {
        name: 'Passport',
        description: 'Valid passport',
        required: true,
        category: DocCategory.EMPLOYEE,
        serviceTypeId: 'service-type-id',
      };

      const serviceType = {
        id: 'service-type-id',
        name: 'Test Service',
        description: 'Test Description',
        price: 1000,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const expectedResult = {
        id: 'doc-req-id',
        ...createDto,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.service_type.findUnique.mockResolvedValue(serviceType);
      mockPrismaService.document_requirement.create.mockResolvedValue(expectedResult);

      const result = await service.create(createDto);

      expect(result).toEqual(expectedResult);
      expect(mockPrismaService.service_type.findUnique).toHaveBeenCalledWith({
        where: { id: 'service-type-id' },
      });
      expect(mockPrismaService.document_requirement.create).toHaveBeenCalledWith({
        data: createDto,
      });
    });

    it('should throw BadRequestException if service type not found', async () => {
      const createDto: CreateDocumentRequirementDto = {
        name: 'Passport',
        description: 'Valid passport',
        required: true,
        category: DocCategory.EMPLOYEE,
        serviceTypeId: 'non-existent-id',
      };

      mockPrismaService.service_type.findUnique.mockResolvedValue(null);

      await expect(service.create(createDto)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if creation fails', async () => {
      const createDto: CreateDocumentRequirementDto = {
        name: 'Passport',
        description: 'Valid passport',
        required: true,
        category: DocCategory.EMPLOYEE,
        serviceTypeId: 'service-type-id',
      };

      const serviceType = {
        id: 'service-type-id',
        name: 'Test Service',
        description: 'Test Description',
        price: 1000,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.service_type.findUnique.mockResolvedValue(serviceType);
      mockPrismaService.document_requirement.create.mockRejectedValue(new Error('Database error'));

      await expect(service.create(createDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('findOne', () => {
    it('should return a document requirement by id', async () => {
      const documentRequirement = {
        id: 'doc-req-id',
        name: 'Passport',
        description: 'Valid passport',
        required: true,
        category: DocCategory.EMPLOYEE,
        serviceTypeId: 'service-type-id',
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: {
          id: 'service-type-id',
          name: 'Test Service',
        },
      };

      mockPrismaService.document_requirement.findUnique.mockResolvedValue(documentRequirement);

      const result = await service.findOne('doc-req-id');

      expect(result).toEqual(documentRequirement);
      expect(mockPrismaService.document_requirement.findUnique).toHaveBeenCalledWith({
        where: { id: 'doc-req-id' },
        include: { serviceType: true },
      });
    });

    it('should throw NotFoundException if document requirement not found', async () => {
      mockPrismaService.document_requirement.findUnique.mockResolvedValue(null);

      await expect(service.findOne('non-existent-id')).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a document requirement', async () => {
      const updateDto: UpdateDocumentRequirementDto = {
        name: 'Updated Passport',
      };

      const documentRequirement = {
        id: 'doc-req-id',
        name: 'Passport',
        description: 'Valid passport',
        required: true,
        category: DocCategory.EMPLOYEE,
        serviceTypeId: 'service-type-id',
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: {
          id: 'service-type-id',
          name: 'Test Service',
        },
      };

      const updatedDocumentRequirement = {
        ...documentRequirement,
        name: 'Updated Passport',
      };

      mockPrismaService.document_requirement.findUnique.mockResolvedValue(documentRequirement);
      mockPrismaService.document_requirement.update.mockResolvedValue(updatedDocumentRequirement);

      const result = await service.update('doc-req-id', updateDto);

      expect(result).toEqual(updatedDocumentRequirement);
      expect(mockPrismaService.document_requirement.findUnique).toHaveBeenCalledWith({
        where: { id: 'doc-req-id' },
        include: { serviceType: true },
      });
      expect(mockPrismaService.document_requirement.update).toHaveBeenCalledWith({
        where: { id: 'doc-req-id' },
        data: { name: 'Updated Passport' },
      });
    });

    it('should throw NotFoundException if document requirement not found', async () => {
      mockPrismaService.document_requirement.findUnique.mockResolvedValue(null);

      await expect(service.update('non-existent-id', {})).rejects.toThrow(NotFoundException);
    });

    it('should check if new service type exists', async () => {
      const updateDto: UpdateDocumentRequirementDto = {
        serviceTypeId: 'new-service-type-id',
      };

      const documentRequirement = {
        id: 'doc-req-id',
        name: 'Passport',
        description: 'Valid passport',
        required: true,
        category: DocCategory.EMPLOYEE,
        serviceTypeId: 'service-type-id',
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: {
          id: 'service-type-id',
          name: 'Test Service',
        },
      };

      mockPrismaService.document_requirement.findUnique.mockResolvedValue(documentRequirement);
      mockPrismaService.service_type.findUnique.mockResolvedValue(null);

      await expect(service.update('doc-req-id', updateDto)).rejects.toThrow(BadRequestException);
      expect(mockPrismaService.service_type.findUnique).toHaveBeenCalledWith({
        where: { id: 'new-service-type-id' },
      });
    });
  });

  describe('remove', () => {
    it('should delete a document requirement', async () => {
      const documentRequirement = {
        id: 'doc-req-id',
        name: 'Passport',
        description: 'Valid passport',
        required: true,
        category: DocCategory.EMPLOYEE,
        serviceTypeId: 'service-type-id',
        createdAt: new Date(),
        updatedAt: new Date(),
        serviceType: {
          id: 'service-type-id',
          name: 'Test Service',
        },
      };

      mockPrismaService.document_requirement.findUnique.mockResolvedValue(documentRequirement);
      mockPrismaService.document_requirement.delete.mockResolvedValue(documentRequirement);

      const result = await service.remove('doc-req-id');

      expect(result).toEqual(documentRequirement);
      expect(mockPrismaService.document_requirement.findUnique).toHaveBeenCalledWith({
        where: { id: 'doc-req-id' },
        include: { serviceType: true },
      });
      expect(mockPrismaService.document_requirement.delete).toHaveBeenCalledWith({
        where: { id: 'doc-req-id' },
      });
    });

    it('should throw NotFoundException if document requirement not found', async () => {
      mockPrismaService.document_requirement.findUnique.mockResolvedValue(null);

      await expect(service.remove('non-existent-id')).rejects.toThrow(NotFoundException);
    });
  });
});
