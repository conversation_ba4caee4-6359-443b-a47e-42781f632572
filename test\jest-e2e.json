{"moduleFileExtensions": ["js", "json", "ts", "tsx"], "rootDir": "..", "testEnvironment": "node", "testRegex": ".(e2e-spec|spec).ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest", "^.+\\.(tsx)$": ["ts-jest", {"tsconfig": {"jsx": "react"}}]}, "moduleNameMapper": {"^src/(.*)$": "<rootDir>/src/$1", "^src/template/(.*)$": "<rootDir>/test/__mocks__/src/template/$1", "@react-email/components": "<rootDir>/test/__mocks__/@react-email/components.js", "\\.(tsx)$": "<rootDir>/test/__mocks__/react-component.mock.js"}, "roots": ["<rootDir>/src/", "<rootDir>/test/", "<rootDir>/e2e/"], "moduleDirectories": ["node_modules", "<rootDir>"], "setupFilesAfterEnv": ["<rootDir>/test/jest-e2e-setup.ts"]}