import { ApplicationStatus, WorkflowStatus, QueryStatus } from '@prisma/client';

/**
 * Application fixtures for E2E testing
 *
 * Provides mock data for application-related tests including different
 * application statuses, workflow states, and priority levels.
 */

export interface TestApplication {
  id: string;
  userId: string;
  serviceTypeId: string;
  status: ApplicationStatus;
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  submissionDate?: Date;
  completionDate?: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface TestServiceType {
  id: string;
  name: string;
  description: string;
  price: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface TestWorkflow {
  id: string;
  applicationId: string;
  currentStep: number;
  status: WorkflowStatus;
  startDate: Date;
  lastUpdated: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Service type fixtures
 */
export const SERVICE_TYPE_FIXTURES: Record<string, TestServiceType> = {
  criticalSkills: {
    id: 'service-critical-skills-1',
    name: 'Critical Skills Employment Permit',
    description: 'Fast-track employment permit for skilled professionals',
    price: 1500,
  },

  generalEmployment: {
    id: 'service-general-employment-1',
    name: 'General Employment Permit',
    description: 'Standard employment permit application',
    price: 1200,
  },

  dependentVisa: {
    id: 'service-dependent-visa-1',
    name: 'Dependent/Partner Visa',
    description: 'Visa for family members of permit holders',
    price: 800,
  },

  studentVisa: {
    id: 'service-student-visa-1',
    name: 'Student Visa',
    description: 'Student visa application support',
    price: 1000,
  },
};

/**
 * Application fixtures for different scenarios
 */
export const APPLICATION_FIXTURES: Record<string, TestApplication> = {
  draft: {
    id: 'app-draft-1',
    userId: 'user-test-id-1',
    serviceTypeId: 'service-critical-skills-1',
    status: ApplicationStatus.DRAFT,
    priority: 'MEDIUM',
  },

  submitted: {
    id: 'app-submitted-1',
    userId: 'user-test-id-1',
    serviceTypeId: 'service-critical-skills-1',
    status: ApplicationStatus.SUBMITTED,
    priority: 'HIGH',
    submissionDate: new Date(),
  },

  inProgress: {
    id: 'app-in-progress-1',
    userId: 'user-test-id-1',
    serviceTypeId: 'service-general-employment-1',
    status: ApplicationStatus.IN_PROGRESS,
    priority: 'HIGH',
    submissionDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
  },

  completed: {
    id: 'app-completed-1',
    userId: 'user-test-id-1',
    serviceTypeId: 'service-dependent-visa-1',
    status: ApplicationStatus.COMPLETED,
    priority: 'MEDIUM',
    submissionDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    completionDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
  },

  rejected: {
    id: 'app-rejected-1',
    userId: 'user-test-id-2',
    serviceTypeId: 'service-student-visa-1',
    status: ApplicationStatus.REJECTED,
    priority: 'LOW',
    submissionDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 14 days ago
  },

  urgent: {
    id: 'app-urgent-1',
    userId: 'user-test-id-1',
    serviceTypeId: 'service-critical-skills-1',
    status: ApplicationStatus.SUBMITTED,
    priority: 'URGENT',
    submissionDate: new Date(),
  },
};

/**
 * Workflow fixtures
 */
export const WORKFLOW_FIXTURES: Record<string, TestWorkflow> = {
  inProgress: {
    id: 'workflow-1',
    applicationId: 'app-in-progress-1',
    currentStep: 2,
    status: WorkflowStatus.IN_PROGRESS,
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    lastUpdated: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
  },

  completed: {
    id: 'workflow-2',
    applicationId: 'app-completed-1',
    currentStep: 5,
    status: WorkflowStatus.COMPLETED,
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    lastUpdated: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
  },

  blocked: {
    id: 'workflow-3',
    applicationId: 'app-submitted-1',
    currentStep: 1,
    status: WorkflowStatus.BLOCKED,
    startDate: new Date(),
    lastUpdated: new Date(),
  },
};

/**
 * Application creation DTOs
 */
export const APPLICATION_CREATE_DTOS = {
  valid: {
    serviceTypeId: 'service-critical-skills-1',
  },

  withPriority: {
    serviceTypeId: 'service-critical-skills-1',
    priority: 'HIGH',
  },

  invalidServiceType: {
    serviceTypeId: 'invalid-service-type-id',
  },
};

/**
 * Application update scenarios
 */
export const APPLICATION_UPDATE_SCENARIOS = {
  submit: {
    status: ApplicationStatus.SUBMITTED,
  },

  startProgress: {
    status: ApplicationStatus.IN_PROGRESS,
  },

  complete: {
    status: ApplicationStatus.COMPLETED,
  },

  reject: {
    status: ApplicationStatus.REJECTED,
  },

  increasePriority: {
    priority: 'URGENT',
  },

  decreasePriority: {
    priority: 'LOW',
  },
};

/**
 * Priority queue scenarios for testing
 */
export const PRIORITY_SCENARIOS = {
  urgentFirst: [
    { ...APPLICATION_FIXTURES.urgent, priority: 'URGENT' as const },
    { ...APPLICATION_FIXTURES.inProgress, priority: 'HIGH' as const },
    { ...APPLICATION_FIXTURES.submitted, priority: 'MEDIUM' as const },
    { ...APPLICATION_FIXTURES.draft, priority: 'LOW' as const },
  ],

  samePriority: [
    {
      ...APPLICATION_FIXTURES.submitted,
      priority: 'HIGH' as const,
      submissionDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    },
    {
      ...APPLICATION_FIXTURES.inProgress,
      priority: 'HIGH' as const,
      submissionDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    },
  ],
};

/**
 * Mock Prisma application data
 */
export function mockPrismaApplication(
  application: TestApplication,
  includeRelations = true,
) {
  const baseData = {
    id: application.id,
    userId: application.userId,
    serviceTypeId: application.serviceTypeId,
    status: application.status,
    priority: application.priority || 'MEDIUM',
    submissionDate: application.submissionDate || null,
    completionDate: application.completionDate || null,
    createdAt: application.createdAt || new Date(),
    updatedAt: application.updatedAt || new Date(),
  };

  if (!includeRelations) {
    return baseData;
  }

  return {
    ...baseData,
    serviceType: SERVICE_TYPE_FIXTURES.criticalSkills,
    user: {
      id: application.userId,
      name: 'Test User',
      email: '<EMAIL>',
    },
    workflow:
      application.status === ApplicationStatus.IN_PROGRESS
        ? WORKFLOW_FIXTURES.inProgress
        : null,
    checkpointCalls: [],
    applicationQuery: [],
  };
}

/**
 * Generate multiple applications for testing
 */
export function generateApplicationList(
  count: number,
  userId: string = 'user-test-id-1',
): TestApplication[] {
  const applications: TestApplication[] = [];
  const statuses = Object.values(ApplicationStatus);
  const priorities = ['LOW', 'MEDIUM', 'HIGH', 'URGENT'] as const;

  for (let i = 1; i <= count; i++) {
    applications.push({
      id: `app-test-${i}`,
      userId,
      serviceTypeId: 'service-critical-skills-1',
      status: statuses[i % statuses.length],
      priority: priorities[i % priorities.length],
      submissionDate:
        i % 2 === 0
          ? new Date(Date.now() - i * 24 * 60 * 60 * 1000)
          : undefined,
      createdAt: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
      updatedAt: new Date(),
    });
  }

  return applications;
}

/**
 * Application filter scenarios
 */
export const APPLICATION_FILTER_SCENARIOS = {
  byStatus: {
    status: ApplicationStatus.SUBMITTED,
  },

  byServiceType: {
    serviceTypeId: 'service-critical-skills-1',
  },

  byPriority: {
    priority: 'HIGH',
  },

  byDateRange: {
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    endDate: new Date(),
  },

  sortBySubmissionDate: {
    sortBy: 'submissionDate',
    sortOrder: 'desc',
  },

  sortByPriority: {
    sortBy: 'priority',
    sortOrder: 'desc',
  },
};
