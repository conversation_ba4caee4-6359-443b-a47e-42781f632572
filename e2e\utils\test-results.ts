import * as fs from 'fs';
import * as path from 'path';

/**
 * Test result utilities for E2E testing
 * 
 * Provides structured logging and reporting for E2E test results
 * in JSON format for audit trails and CI/CD integration.
 */

export interface TestResult {
  testSuite: string;
  testName: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  duration: number;
  startTime: Date;
  endTime: Date;
  error?: string;
  details?: any;
  httpRequests?: HttpRequestLog[];
}

export interface HttpRequestLog {
  method: string;
  url: string;
  statusCode: number;
  requestBody?: any;
  responseBody?: any;
  duration: number;
  timestamp: Date;
}

export interface TestSuiteResult {
  suiteName: string;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  duration: number;
  startTime: Date;
  endTime: Date;
  tests: TestResult[];
}

export interface TestRunSummary {
  runId: string;
  environment: string;
  totalSuites: number;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  duration: number;
  startTime: Date;
  endTime: Date;
  suites: TestSuiteResult[];
}

/**
 * Test result collector class
 */
export class TestResultCollector {
  private results: TestResult[] = [];
  private httpLogs: HttpRequestLog[] = [];
  private currentTest: Partial<TestResult> | null = null;
  private suiteStartTime: Date = new Date();

  /**
   * Start tracking a new test
   */
  startTest(testSuite: string, testName: string): void {
    this.currentTest = {
      testSuite,
      testName,
      startTime: new Date(),
      httpRequests: [],
    };
  }

  /**
   * Log an HTTP request
   */
  logHttpRequest(request: HttpRequestLog): void {
    this.httpLogs.push(request);
    if (this.currentTest) {
      this.currentTest.httpRequests = this.currentTest.httpRequests || [];
      this.currentTest.httpRequests.push(request);
    }
  }

  /**
   * End the current test with result
   */
  endTest(status: 'PASS' | 'FAIL' | 'SKIP', error?: string, details?: any): void {
    if (!this.currentTest) {
      throw new Error('No test started');
    }

    const endTime = new Date();
    const duration = endTime.getTime() - this.currentTest.startTime!.getTime();

    const result: TestResult = {
      testSuite: this.currentTest.testSuite!,
      testName: this.currentTest.testName!,
      status,
      duration,
      startTime: this.currentTest.startTime!,
      endTime,
      error,
      details,
      httpRequests: this.currentTest.httpRequests || [],
    };

    this.results.push(result);
    this.currentTest = null;
  }

  /**
   * Get test suite results
   */
  getSuiteResults(suiteName: string): TestSuiteResult {
    const suiteTests = this.results.filter(test => test.testSuite === suiteName);
    const endTime = new Date();
    
    return {
      suiteName,
      totalTests: suiteTests.length,
      passedTests: suiteTests.filter(test => test.status === 'PASS').length,
      failedTests: suiteTests.filter(test => test.status === 'FAIL').length,
      skippedTests: suiteTests.filter(test => test.status === 'SKIP').length,
      duration: endTime.getTime() - this.suiteStartTime.getTime(),
      startTime: this.suiteStartTime,
      endTime,
      tests: suiteTests,
    };
  }

  /**
   * Get all results
   */
  getAllResults(): TestResult[] {
    return this.results;
  }

  /**
   * Clear all results
   */
  clear(): void {
    this.results = [];
    this.httpLogs = [];
    this.currentTest = null;
    this.suiteStartTime = new Date();
  }
}

/**
 * Global test result collector instance
 */
export const testResultCollector = new TestResultCollector();

/**
 * Test result reporter
 */
export class TestResultReporter {
  private outputDir: string;

  constructor(outputDir: string = './e2e/output') {
    this.outputDir = outputDir;
    this.ensureOutputDirectory();
  }

  /**
   * Ensure output directory exists
   */
  private ensureOutputDirectory(): void {
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }

    const logsDir = path.join(this.outputDir, 'logs');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
  }

  /**
   * Generate test run summary
   */
  generateSummary(suiteResults: TestSuiteResult[]): TestRunSummary {
    const runId = `run-${Date.now()}`;
    const environment = process.env.NODE_ENV || 'test';
    
    const totalTests = suiteResults.reduce((sum, suite) => sum + suite.totalTests, 0);
    const passedTests = suiteResults.reduce((sum, suite) => sum + suite.passedTests, 0);
    const failedTests = suiteResults.reduce((sum, suite) => sum + suite.failedTests, 0);
    const skippedTests = suiteResults.reduce((sum, suite) => sum + suite.skippedTests, 0);
    
    const startTime = suiteResults.length > 0 
      ? new Date(Math.min(...suiteResults.map(s => s.startTime.getTime())))
      : new Date();
    const endTime = suiteResults.length > 0
      ? new Date(Math.max(...suiteResults.map(s => s.endTime.getTime())))
      : new Date();
    
    return {
      runId,
      environment,
      totalSuites: suiteResults.length,
      totalTests,
      passedTests,
      failedTests,
      skippedTests,
      duration: endTime.getTime() - startTime.getTime(),
      startTime,
      endTime,
      suites: suiteResults,
    };
  }

  /**
   * Save test results to JSON file
   */
  saveResults(summary: TestRunSummary): void {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `test-results-${timestamp}.json`;
    const filepath = path.join(this.outputDir, filename);
    
    fs.writeFileSync(filepath, JSON.stringify(summary, null, 2));
    
    // Also save as latest results
    const latestPath = path.join(this.outputDir, 'result-summary.json');
    fs.writeFileSync(latestPath, JSON.stringify(summary, null, 2));
    
    console.log(`Test results saved to: ${filepath}`);
  }

  /**
   * Save detailed logs
   */
  saveLogs(summary: TestRunSummary): void {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const logsDir = path.join(this.outputDir, 'logs');
    
    // Save individual suite logs
    summary.suites.forEach(suite => {
      const suiteLogFile = path.join(logsDir, `${suite.suiteName}-${timestamp}.json`);
      fs.writeFileSync(suiteLogFile, JSON.stringify(suite, null, 2));
    });
    
    // Save HTTP request logs
    const httpLogs = summary.suites.flatMap(suite => 
      suite.tests.flatMap(test => test.httpRequests || [])
    );
    
    if (httpLogs.length > 0) {
      const httpLogFile = path.join(logsDir, `http-requests-${timestamp}.json`);
      fs.writeFileSync(httpLogFile, JSON.stringify(httpLogs, null, 2));
    }
  }

  /**
   * Generate HTML report
   */
  generateHtmlReport(summary: TestRunSummary): void {
    const htmlContent = this.generateHtmlContent(summary);
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `test-report-${timestamp}.html`;
    const filepath = path.join(this.outputDir, filename);
    
    fs.writeFileSync(filepath, htmlContent);
    
    // Also save as latest report
    const latestPath = path.join(this.outputDir, 'test-report.html');
    fs.writeFileSync(latestPath, htmlContent);
    
    console.log(`HTML report generated: ${filepath}`);
  }

  /**
   * Generate HTML content for the report
   */
  private generateHtmlContent(summary: TestRunSummary): string {
    const passRate = summary.totalTests > 0 
      ? ((summary.passedTests / summary.totalTests) * 100).toFixed(1)
      : '0';
    
    return `
<!DOCTYPE html>
<html>
<head>
    <title>E2E Test Report - ${summary.runId}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: white; padding: 15px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .suite { margin: 20px 0; border: 1px solid #ddd; border-radius: 5px; }
        .suite-header { background: #f8f9fa; padding: 15px; font-weight: bold; }
        .test { padding: 10px 15px; border-bottom: 1px solid #eee; }
        .pass { color: #28a745; }
        .fail { color: #dc3545; }
        .skip { color: #ffc107; }
        .error { background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>E2E Test Report</h1>
        <p><strong>Run ID:</strong> ${summary.runId}</p>
        <p><strong>Environment:</strong> ${summary.environment}</p>
        <p><strong>Duration:</strong> ${(summary.duration / 1000).toFixed(2)}s</p>
        <p><strong>Timestamp:</strong> ${summary.endTime.toISOString()}</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>Total Tests</h3>
            <p style="font-size: 24px; margin: 0;">${summary.totalTests}</p>
        </div>
        <div class="metric">
            <h3>Pass Rate</h3>
            <p style="font-size: 24px; margin: 0; color: #28a745;">${passRate}%</p>
        </div>
        <div class="metric">
            <h3>Passed</h3>
            <p style="font-size: 24px; margin: 0; color: #28a745;">${summary.passedTests}</p>
        </div>
        <div class="metric">
            <h3>Failed</h3>
            <p style="font-size: 24px; margin: 0; color: #dc3545;">${summary.failedTests}</p>
        </div>
        <div class="metric">
            <h3>Skipped</h3>
            <p style="font-size: 24px; margin: 0; color: #ffc107;">${summary.skippedTests}</p>
        </div>
    </div>
    
    ${summary.suites.map(suite => `
        <div class="suite">
            <div class="suite-header">
                ${suite.suiteName} (${suite.passedTests}/${suite.totalTests} passed)
            </div>
            ${suite.tests.map(test => `
                <div class="test">
                    <span class="${test.status.toLowerCase()}">${test.status}</span>
                    <strong>${test.testName}</strong>
                    <span style="float: right;">${(test.duration / 1000).toFixed(2)}s</span>
                    ${test.error ? `<div class="error">${test.error}</div>` : ''}
                </div>
            `).join('')}
        </div>
    `).join('')}
</body>
</html>`;
  }
}

/**
 * Global test result reporter instance
 */
export const testResultReporter = new TestResultReporter();
