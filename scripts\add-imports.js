/**
 * <PERSON><PERSON><PERSON> to add imports to files
 * This script adds the IJWTPayload import to all files that use it
 */

const fs = require('fs');
const path = require('path');

// List of files that need the import
const filesToUpdate = [
  'src/comment/comment.controller.ts',
  'src/comment/comment.service.ts',
  'src/payment/payment.controller.ts',
  'src/payment/payment.service.ts',
  'src/review/review.controller.ts',
  'src/review/review.service.ts',
  'src/user/user.controller.ts',
  'src/user/user.service.ts'
];

// Import statement to add
const importStatement = "import { IJWTPayload } from 'src/types';\n";

// Process each file
filesToUpdate.forEach(filePath => {
  try {
    // Read the file
    const fullPath = path.resolve(filePath);
    const content = fs.readFileSync(fullPath, 'utf8');

    // Check if the import already exists
    if (content.includes("import { IJWTPayload }") || content.includes("import {IJWTPayload}")) {
      console.log(`Import already exists in ${filePath}`);
      return;
    }

    // Find the last import statement
    const lines = content.split('\n');
    let lastImportIndex = -1;

    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('import ')) {
        lastImportIndex = i;
      }
    }

    // Insert the import after the last import
    if (lastImportIndex >= 0) {
      lines.splice(lastImportIndex + 1, 0, importStatement);
      const updatedContent = lines.join('\n');
      
      // Write the updated content back to the file
      fs.writeFileSync(fullPath, updatedContent, 'utf8');
      console.log(`Added import to ${filePath}`);
    } else {
      console.log(`No import statements found in ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
  }
});

console.log('Done adding imports');
