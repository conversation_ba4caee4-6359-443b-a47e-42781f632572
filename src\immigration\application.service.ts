import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../utils/prisma.service';
import {
  CreateApplicationDto,
  UpdateApplicationDto,
  FilterApplicationDto,
  CreateWorkflowStepDto,
  UpdateWorkflowStepDto,
  UpdateWorkflowDto,
  CreateCheckpointCallDto,
  UpdateCheckpointCallDto,
  CreateApplicationQueryDto,
  RespondToQueryDto,
  ApplicationProgressDto,
  EnhancedApplicationProgressDto,
  ApplicationResponseDto,
  PaginatedApplicationResponseDto,
  WorkflowStepResponseDto,
} from './dto/application.dto';
import { ApplicationStatus, WorkflowStatus, QueryStatus } from '@prisma/client';

/**
 * Service for managing applications and workflows
 *
 * This service handles:
 * - Application CRUD operations
 * - Workflow management and progression
 * - Checkpoint call scheduling
 * - Application queries and responses
 * - Progress tracking
 */
@Injectable()
export class ApplicationService {
  private readonly logger = new Logger(ApplicationService.name);

  constructor(private prisma: PrismaService) {}

  /**
   * Create a new application for a user
   */
  async create(userId: string, createApplicationDto: CreateApplicationDto) {
    // Verify service type exists
    const serviceType = await this.prisma.service_type.findUnique({
      where: { id: createApplicationDto.serviceTypeId },
    });

    if (!serviceType) {
      throw new NotFoundException('Service type not found');
    }

    // Create the application
    const application = await this.prisma.application.create({
      data: {
        userId,
        serviceTypeId: createApplicationDto.serviceTypeId,
        status: ApplicationStatus.DRAFT,
      },
      include: {
        serviceType: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return application;
  }

  /**
   * Get all applications with filtering and pagination
   */
  async findAll(
    filterDto: FilterApplicationDto,
    userRole?: string,
    userId?: string,
  ) {
    const {
      page = 1,
      limit = 10,
      status,
      serviceTypeId,
      userId: filterUserId,
    } = filterDto;
    const skip = (page - 1) * limit;

    // Build where clause based on user role and filters
    const where: any = {};

    // If user is not admin, they can only see their own applications
    if (userRole?.toUpperCase() !== 'ADMIN') {
      where.userId = userId;
    } else if (filterUserId) {
      where.userId = filterUserId;
    }

    if (status) {
      where.status = status;
    }

    if (serviceTypeId) {
      where.serviceTypeId = serviceTypeId;
    }

    const [applications, total] = await Promise.all([
      this.prisma.application.findMany({
        where,
        skip,
        take: limit,
        include: {
          serviceType: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          workflow: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.application.count({ where }),
    ]);

    return {
      data: applications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Get a specific application by ID
   */
  async findOne(id: string, userRole?: string, userId?: string) {
    const application = await this.prisma.application.findUnique({
      where: { id },
      include: {
        serviceType: {
          include: {
            documentRequirements: true,
            workflowSteps: {
              orderBy: { order: 'asc' },
            },
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        workflow: true,
        checkpointCalls: {
          orderBy: { scheduledDate: 'desc' },
        },
        applicationQuery: {
          orderBy: { queryDate: 'desc' },
        },
      },
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    // Check if user has permission to view this application
    if (userRole?.toUpperCase() !== 'ADMIN' && application.userId !== userId) {
      throw new ForbiddenException('You can only view your own applications');
    }

    return application;
  }

  /**
   * Update an application
   */
  async update(
    id: string,
    updateApplicationDto: UpdateApplicationDto,
    userRole?: string,
    userId?: string,
  ) {
    const application = await this.findOne(id, userRole, userId);

    // If submitting application, create workflow
    if (
      updateApplicationDto.status === ApplicationStatus.SUBMITTED &&
      application.status === ApplicationStatus.DRAFT
    ) {
      await this.createWorkflow(id);
    }

    const updatedApplication = await this.prisma.application.update({
      where: { id },
      data: updateApplicationDto,
      include: {
        serviceType: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        workflow: true,
      },
    });

    return updatedApplication;
  }

  /**
   * Delete an application (only if in DRAFT status)
   */
  async remove(id: string, userRole?: string, userId?: string) {
    const application = await this.findOne(id, userRole, userId);

    if (application.status !== ApplicationStatus.DRAFT) {
      throw new BadRequestException(
        'Can only delete applications in DRAFT status',
      );
    }

    return this.prisma.application.delete({
      where: { id },
    });
  }

  /**
   * Create workflow for an application
   */
  private async createWorkflow(applicationId: string) {
    const application = await this.prisma.application.findUnique({
      where: { id: applicationId },
      include: {
        serviceType: {
          include: {
            workflowSteps: {
              orderBy: { order: 'asc' },
            },
          },
        },
      },
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    // Create workflow
    await this.prisma.application_workflow.create({
      data: {
        applicationId,
        currentStep: 1,
        status: WorkflowStatus.IN_PROGRESS,
      },
    });

    return true;
  }

  /**
   * Get application progress
   */
  async getProgress(
    applicationId: string,
    userRole?: string,
    userId?: string,
  ): Promise<ApplicationProgressDto> {
    const application = await this.findOne(applicationId, userRole, userId);

    if (!application.workflow) {
      return {
        applicationId,
        currentStep: 0,
        totalSteps: application.serviceType.workflowSteps.length,
        progressPercentage: 0,
        currentStepName: 'Not Started',
        estimatedCompletion: new Date(),
        status: WorkflowStatus.NOT_STARTED,
      };
    }

    const totalSteps = application.serviceType.workflowSteps.length;
    const currentStep = application.workflow.currentStep;
    const progressPercentage = Math.round((currentStep / totalSteps) * 100);

    const currentStepInfo = application.serviceType.workflowSteps.find(
      (step) => step.order === currentStep,
    );

    // Calculate estimated completion based on remaining steps
    const remainingSteps = application.serviceType.workflowSteps.filter(
      (step) => step.order >= currentStep,
    );
    const remainingDays = remainingSteps.reduce(
      (total, step) => total + step.estimatedDuration,
      0,
    );
    const estimatedCompletion = new Date();
    estimatedCompletion.setDate(estimatedCompletion.getDate() + remainingDays);

    return {
      applicationId,
      currentStep,
      totalSteps,
      progressPercentage,
      currentStepName: currentStepInfo?.name || 'Unknown Step',
      estimatedCompletion,
      status: application.workflow.status,
    };
  }

  /**
   * Advance workflow to next step (Admin only)
   */
  async advanceWorkflow(
    applicationId: string,
    updateWorkflowDto: UpdateWorkflowDto,
    userRole?: string,
    userId?: string,
  ) {
    const application = await this.prisma.application.findUnique({
      where: { id: applicationId },
      include: {
        workflow: true,
        serviceType: {
          include: {
            workflowSteps: {
              orderBy: { order: 'asc' },
            },
          },
        },
      },
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    if (!application.workflow) {
      throw new BadRequestException('Application has no workflow');
    }

    const totalSteps = application.serviceType.workflowSteps.length;
    const newStep =
      updateWorkflowDto.currentStep || application.workflow.currentStep + 1;

    if (newStep > totalSteps) {
      // Complete the application
      await Promise.all([
        this.prisma.application_workflow.update({
          where: { id: application.workflow.id },
          data: {
            currentStep: totalSteps,
            status: WorkflowStatus.COMPLETED,
          },
        }),
        this.prisma.application.update({
          where: { id: applicationId },
          data: {
            status: ApplicationStatus.COMPLETED,
            completionDate: new Date(),
          },
        }),
      ]);
    } else {
      await this.prisma.application_workflow.update({
        where: { id: application.workflow.id },
        data: {
          currentStep: newStep,
          status: updateWorkflowDto.status || WorkflowStatus.IN_PROGRESS,
        },
      });
    }

    return this.findOne(applicationId, userRole, userId);
  }

  /**
   * Create workflow steps for a service type (Admin only)
   */
  async createWorkflowStep(createWorkflowStepDto: CreateWorkflowStepDto) {
    // Verify service type exists
    const serviceType = await this.prisma.service_type.findUnique({
      where: { id: createWorkflowStepDto.serviceTypeId },
    });

    if (!serviceType) {
      throw new NotFoundException('Service type not found');
    }

    return this.prisma.workflow_step.create({
      data: createWorkflowStepDto,
      include: {
        serviceType: true,
      },
    });
  }

  /**
   * Get workflow steps for a service type
   */
  async getWorkflowSteps(serviceTypeId: string) {
    return this.prisma.workflow_step.findMany({
      where: { serviceTypeId },
      orderBy: { order: 'asc' },
      include: {
        serviceType: true,
      },
    });
  }

  /**
   * Create a checkpoint call
   */
  async createCheckpointCall(createCheckpointCallDto: CreateCheckpointCallDto) {
    // Verify application exists
    const application = await this.prisma.application.findUnique({
      where: { id: createCheckpointCallDto.applicationId },
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    return this.prisma.checkpoint_call.create({
      data: createCheckpointCallDto,
      include: {
        application: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            serviceType: true,
          },
        },
      },
    });
  }

  /**
   * Update a checkpoint call
   */
  async updateCheckpointCall(
    id: string,
    updateCheckpointCallDto: UpdateCheckpointCallDto,
  ) {
    const checkpointCall = await this.prisma.checkpoint_call.findUnique({
      where: { id },
    });

    if (!checkpointCall) {
      throw new NotFoundException('Checkpoint call not found');
    }

    return this.prisma.checkpoint_call.update({
      where: { id },
      data: updateCheckpointCallDto,
      include: {
        application: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            serviceType: true,
          },
        },
      },
    });
  }

  /**
   * Get checkpoint calls for an application
   */
  async getCheckpointCalls(
    applicationId: string,
    userRole?: string,
    userId?: string,
  ) {
    // Verify user has access to this application
    await this.findOne(applicationId, userRole, userId);

    return this.prisma.checkpoint_call.findMany({
      where: { applicationId },
      orderBy: { scheduledDate: 'desc' },
      include: {
        application: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            serviceType: true,
          },
        },
      },
    });
  }

  /**
   * Create an application query
   */
  async createQuery(
    createApplicationQueryDto: CreateApplicationQueryDto,
    userRole?: string,
    userId?: string,
  ) {
    // Verify user has access to this application
    await this.findOne(
      createApplicationQueryDto.applicationId,
      userRole,
      userId,
    );

    return this.prisma.application_query.create({
      data: createApplicationQueryDto,
      include: {
        application: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            serviceType: true,
          },
        },
      },
    });
  }

  /**
   * Respond to an application query (Admin only)
   */
  async respondToQuery(queryId: string, respondToQueryDto: RespondToQueryDto) {
    const query = await this.prisma.application_query.findUnique({
      where: { id: queryId },
    });

    if (!query) {
      throw new NotFoundException('Query not found');
    }

    return this.prisma.application_query.update({
      where: { id: queryId },
      data: {
        responseText: respondToQueryDto.responseText,
        responseDate: new Date(),
        status: QueryStatus.RESPONDED,
      },
      include: {
        application: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            serviceType: true,
          },
        },
      },
    });
  }

  /**
   * Get queries for an application
   */
  async getQueries(applicationId: string, userRole?: string, userId?: string) {
    // Verify user has access to this application
    await this.findOne(applicationId, userRole, userId);

    return this.prisma.application_query.findMany({
      where: { applicationId },
      orderBy: { queryDate: 'desc' },
      include: {
        application: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            serviceType: true,
          },
        },
      },
    });
  }

  /**
   * Get enhanced application progress with detailed step information
   */
  async getEnhancedProgress(
    applicationId: string,
    userRole?: string,
    userId?: string,
  ): Promise<EnhancedApplicationProgressDto> {
    this.logger.log(
      `Getting enhanced progress for application: ${applicationId}`,
    );

    const application = await this.findOne(applicationId, userRole, userId);

    if (!application.workflow) {
      return {
        currentStep: 0,
        totalSteps: application.serviceType.workflowSteps.length,
        progressPercentage: 0,
        currentStepName: 'Not Started',
        currentStepDescription: 'Application has not been submitted yet',
        estimatedCompletion: new Date(),
        daysElapsed: 0,
        estimatedDaysRemaining: application.serviceType.workflowSteps.reduce(
          (total, step) => total + step.estimatedDuration,
          0,
        ),
        workflowStatus: WorkflowStatus.NOT_STARTED,
        applicationStatus: application.status,
        isOverdue: false,
        daysOverdue: 0,
        completedSteps: [],
        remainingSteps: application.serviceType.workflowSteps.map((step) => ({
          step: step.order,
          name: step.name,
          description: step.description,
          estimatedDuration: step.estimatedDuration,
        })),
      };
    }

    const totalSteps = application.serviceType.workflowSteps.length;
    const currentStep = application.workflow.currentStep;
    const progressPercentage = Math.round((currentStep / totalSteps) * 100);

    const currentStepInfo = application.serviceType.workflowSteps.find(
      (step) => step.order === currentStep,
    );

    // Calculate days elapsed since submission
    const submissionDate = application.submissionDate || application.createdAt;
    const now = new Date();
    const daysElapsed = Math.floor(
      (now.getTime() - submissionDate.getTime()) / (1000 * 60 * 60 * 24),
    );

    // Calculate estimated completion and remaining days
    const remainingSteps = application.serviceType.workflowSteps.filter(
      (step) => step.order >= currentStep,
    );
    const estimatedDaysRemaining = remainingSteps.reduce(
      (total, step) => total + step.estimatedDuration,
      0,
    );
    const estimatedCompletion = new Date();
    estimatedCompletion.setDate(
      estimatedCompletion.getDate() + estimatedDaysRemaining,
    );

    // Calculate if overdue
    const totalEstimatedDuration = application.serviceType.workflowSteps.reduce(
      (total, step) => total + step.estimatedDuration,
      0,
    );
    const isOverdue = daysElapsed > totalEstimatedDuration;
    const daysOverdue = isOverdue ? daysElapsed - totalEstimatedDuration : 0;

    // Build completed and remaining steps
    const completedSteps = application.serviceType.workflowSteps
      .filter((step) => step.order < currentStep)
      .map((step) => ({
        step: step.order,
        name: step.name,
        description: step.description,
        completedDate:
          application.workflow?.lastUpdated || application.submissionDate,
        estimatedDuration: step.estimatedDuration,
        actualDuration: step.estimatedDuration, // TODO: Calculate actual duration when we have step completion tracking
      }));

    const remainingStepsData = application.serviceType.workflowSteps
      .filter((step) => step.order >= currentStep)
      .map((step) => ({
        step: step.order,
        name: step.name,
        description: step.description,
        estimatedDuration: step.estimatedDuration,
      }));

    return {
      currentStep,
      totalSteps,
      progressPercentage,
      currentStepName: currentStepInfo?.name || 'Unknown Step',
      currentStepDescription:
        currentStepInfo?.description || 'No description available',
      estimatedCompletion,
      daysElapsed,
      estimatedDaysRemaining,
      workflowStatus: application.workflow.status,
      applicationStatus: application.status,
      isOverdue,
      daysOverdue,
      completedSteps,
      remainingSteps: remainingStepsData,
    };
  }

  /**
   * Update workflow step (Admin only)
   */
  async updateWorkflowStep(
    stepId: string,
    updateWorkflowStepDto: UpdateWorkflowStepDto,
  ): Promise<WorkflowStepResponseDto> {
    this.logger.log(`Updating workflow step: ${stepId}`);

    const existingStep = await this.prisma.workflow_step.findUnique({
      where: { id: stepId },
      include: { serviceType: true },
    });

    if (!existingStep) {
      throw new NotFoundException('Workflow step not found');
    }

    const updatedStep = await this.prisma.workflow_step.update({
      where: { id: stepId },
      data: updateWorkflowStepDto,
      include: {
        serviceType: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
    });

    return {
      id: updatedStep.id,
      name: updatedStep.name,
      description: updatedStep.description,
      order: updatedStep.order,
      estimatedDuration: updatedStep.estimatedDuration,
      serviceTypeId: updatedStep.serviceTypeId,
      createdAt: updatedStep.createdAt,
      updatedAt: updatedStep.updatedAt,
      serviceType: updatedStep.serviceType,
    };
  }

  /**
   * Delete workflow step (Admin only)
   */
  async deleteWorkflowStep(stepId: string): Promise<{ message: string }> {
    this.logger.log(`Deleting workflow step: ${stepId}`);

    const existingStep = await this.prisma.workflow_step.findUnique({
      where: { id: stepId },
    });

    if (!existingStep) {
      throw new NotFoundException('Workflow step not found');
    }

    // Check if any applications are using this step
    const applicationsUsingStep = await this.prisma.application.count({
      where: {
        serviceTypeId: existingStep.serviceTypeId,
        status: {
          in: [ApplicationStatus.SUBMITTED, ApplicationStatus.IN_PROGRESS],
        },
      },
    });

    if (applicationsUsingStep > 0) {
      throw new BadRequestException(
        'Cannot delete workflow step that is being used by active applications',
      );
    }

    await this.prisma.workflow_step.delete({
      where: { id: stepId },
    });

    return { message: 'Workflow step deleted successfully' };
  }

  /**
   * Get applications with enhanced filtering and pagination
   */
  async findAllEnhanced(
    filters?: FilterApplicationDto,
    userRole?: string,
    userId?: string,
  ): Promise<PaginatedApplicationResponseDto> {
    this.logger.log(`Getting applications with enhanced filtering`, {
      filters,
      userRole,
    });

    const {
      status,
      serviceTypeId,
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = filters || {};

    // Build where clause
    const where: any = {};

    if (status) {
      where.status = status;
    }

    if (serviceTypeId) {
      where.serviceTypeId = serviceTypeId;
    }

    // Add user filtering for non-admin users
    if (userRole !== 'admin' && userId) {
      where.userId = userId;
    }

    // Pagination
    const skip = (page - 1) * limit;
    const take = Math.min(limit, 100); // Max 100 items per page

    // Sort order
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    try {
      const [applications, total] = await Promise.all([
        this.prisma.application.findMany({
          where,
          skip,
          take,
          orderBy,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            serviceType: {
              select: {
                id: true,
                name: true,
                description: true,
                price: true,
              },
            },
            workflow: {
              select: {
                id: true,
                currentStep: true,
                status: true,
                startDate: true,
                lastUpdated: true,
              },
            },
            checkpointCalls: {
              select: {
                id: true,
                scheduledDate: true,
                status: true,
              },
              orderBy: { scheduledDate: 'desc' },
              take: 5, // Latest 5 calls
            },
            applicationQuery: {
              select: {
                id: true,
                queryDate: true,
                status: true,
                queryText: true,
              },
              orderBy: { queryDate: 'desc' },
              take: 5, // Latest 5 queries
            },
            documents: {
              select: {
                id: true,
                documentName: true,
                verificationStatus: true,
                uploadDate: true,
              },
              orderBy: { uploadDate: 'desc' },
              take: 10, // Latest 10 documents
            },
          },
        }),
        this.prisma.application.count({ where }),
      ]);

      const totalPages = Math.ceil(total / take);

      this.logger.log(
        `Found ${applications.length} applications out of ${total} total`,
      );

      // Map applications to response DTOs with progress calculation
      const mappedApplications = await Promise.all(
        applications.map(async (app) => {
          let progress: EnhancedApplicationProgressDto | undefined;

          try {
            progress = await this.getEnhancedProgress(app.id, userRole, userId);
          } catch (error) {
            this.logger.warn(
              `Failed to calculate progress for application ${app.id}: ${error.message}`,
            );
          }

          return {
            id: app.id,
            status: app.status,
            submissionDate: app.submissionDate,
            completionDate: app.completionDate,
            userId: app.userId,
            serviceTypeId: app.serviceTypeId,
            createdAt: app.createdAt,
            updatedAt: app.updatedAt,
            user: app.user,
            serviceType: app.serviceType,
            workflow: app.workflow,
            progress,
            checkpointCalls: app.checkpointCalls,
            applicationQuery: app.applicationQuery,
            documents: app.documents,
          } as ApplicationResponseDto;
        }),
      );

      return {
        data: mappedApplications,
        total,
        page,
        limit: take,
        totalPages,
      };
    } catch (error) {
      this.logger.error(
        `Failed to fetch applications: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        'Failed to fetch applications: ' + error.message,
      );
    }
  }

  /**
   * Get application statistics (Admin only)
   */
  async getStatistics() {
    const [
      totalApplications,
      draftApplications,
      submittedApplications,
      inProgressApplications,
      completedApplications,
      rejectedApplications,
    ] = await Promise.all([
      this.prisma.application.count(),
      this.prisma.application.count({
        where: { status: ApplicationStatus.DRAFT },
      }),
      this.prisma.application.count({
        where: { status: ApplicationStatus.SUBMITTED },
      }),
      this.prisma.application.count({
        where: { status: ApplicationStatus.IN_PROGRESS },
      }),
      this.prisma.application.count({
        where: { status: ApplicationStatus.COMPLETED },
      }),
      this.prisma.application.count({
        where: { status: ApplicationStatus.REJECTED },
      }),
    ]);

    return {
      total: totalApplications,
      byStatus: {
        draft: draftApplications,
        submitted: submittedApplications,
        inProgress: inProgressApplications,
        completed: completedApplications,
        rejected: rejectedApplications,
      },
    };
  }
}
