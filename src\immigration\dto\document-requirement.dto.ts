import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsBoolean,
  IsEnum,
  IsOptional,
  IsNumber,
  Min,
  Max,
  Max<PERSON>ength,
  IsUUID,
} from 'class-validator';
import { Type } from 'class-transformer';
import { DocCategory } from '@prisma/client';

/**
 * DTO for creating a new document requirement
 */
export class CreateDocumentRequirementDto {
  @ApiProperty({
    description: 'Name of the document requirement',
    example: 'Passport',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;

  @ApiProperty({
    description: 'Description of the document requirement',
    example: 'Valid passport with at least 6 months validity',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(1000)
  description: string;

  @ApiProperty({
    description: 'Whether the document is required or optional',
    example: true,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  required?: boolean = true;

  @ApiProperty({
    description: 'Category of the document requirement',
    enum: DocCategory,
    example: DocCategory.EMPLOYEE,
  })
  @IsEnum(DocCategory)
  @IsNotEmpty()
  category: DocCategory;

  @ApiProperty({
    description: 'ID of the service type this requirement belongs to',
    example: 'clz1234abcdef',
  })
  @IsString()
  @IsNotEmpty()
  serviceTypeId: string;
}

/**
 * DTO for updating an existing document requirement
 */
export class UpdateDocumentRequirementDto {
  @ApiProperty({
    description: 'Name of the document requirement',
    example: 'Passport',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Description of the document requirement',
    example: 'Valid passport with at least 6 months validity',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(1000)
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Whether the document is required or optional',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  required?: boolean;

  @ApiProperty({
    description: 'Category of the document requirement',
    enum: DocCategory,
    example: DocCategory.EMPLOYEE,
    required: false,
  })
  @IsEnum(DocCategory)
  @IsOptional()
  category?: DocCategory;

  @ApiProperty({
    description: 'ID of the service type this requirement belongs to',
    example: 'clz1234abcdef',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  @IsOptional()
  serviceTypeId?: string;
}

/**
 * DTO for filtering document requirements
 */
export class FilterDocumentRequirementDto {
  @ApiPropertyOptional({
    description: 'Search term for name or description',
    example: 'passport',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by category',
    enum: DocCategory,
    example: DocCategory.EMPLOYEE,
  })
  @IsEnum(DocCategory)
  @IsOptional()
  category?: DocCategory;

  @ApiPropertyOptional({
    description: 'Filter by required status',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  required?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by service type ID',
    example: 'clz1234abcdef',
  })
  @IsString()
  @IsUUID()
  @IsOptional()
  serviceTypeId?: string;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    default: 1,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  @IsOptional()
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Items per page for pagination (max 100)',
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  @IsOptional()
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Sort field',
    example: 'name',
    enum: ['name', 'category', 'required', 'createdAt', 'updatedAt'],
  })
  @IsString()
  @IsOptional()
  @IsEnum(['name', 'category', 'required', 'createdAt', 'updatedAt'])
  sortBy?: string = 'name';

  @ApiPropertyOptional({
    description: 'Sort order',
    example: 'asc',
    enum: ['asc', 'desc'],
  })
  @IsString()
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'asc';
}

/**
 * Response DTO for document requirement data
 */
export class DocumentRequirementResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the document requirement',
    example: 'clx1234567890abcdef',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the document requirement',
    example: 'Passport',
  })
  name: string;

  @ApiProperty({
    description: 'Description of the document requirement',
    example: 'Valid passport with at least 6 months validity',
  })
  description: string;

  @ApiProperty({
    description: 'Whether the document is required or optional',
    example: true,
  })
  required: boolean;

  @ApiProperty({
    description: 'Category of the document requirement',
    enum: DocCategory,
    example: DocCategory.EMPLOYEE,
  })
  category: DocCategory;

  @ApiProperty({
    description: 'ID of the service type this requirement belongs to',
    example: 'clz1234abcdef',
  })
  serviceTypeId: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Service type information',
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      description: { type: 'string' },
      price: { type: 'number' },
    },
  })
  serviceType?: any;
}

/**
 * Paginated response DTO for document requirements
 */
export class PaginatedDocumentRequirementResponseDto {
  @ApiProperty({
    description: 'Array of document requirements',
    type: [DocumentRequirementResponseDto],
  })
  data: DocumentRequirementResponseDto[];

  @ApiProperty({
    description: 'Total number of document requirements',
    example: 25,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 3,
  })
  totalPages: number;
}
