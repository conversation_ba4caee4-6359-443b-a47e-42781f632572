import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsBoolean,
  IsNumber,
  IsDateString,
  Min,
  Max,
} from 'class-validator';
import {
  SecurityEventType,
  SecuritySeverity,
  ConsentType,
  DataExportType,
  DataDeletionType,
  ExportStatus,
  DeletionStatus,
} from '@prisma/client';

/**
 * DTO for creating a security event
 */
export class CreateSecurityEventDto {
  @ApiProperty({
    description: 'Type of security event',
    enum: SecurityEventType,
    example: SecurityEventType.SUSPICIOUS_ACTIVITY,
  })
  @IsEnum(SecurityEventType)
  @IsNotEmpty()
  eventType: SecurityEventType;

  @ApiProperty({
    description: 'Severity level of the event',
    enum: SecuritySeverity,
    example: SecuritySeverity.MEDIUM,
  })
  @IsEnum(SecuritySeverity)
  @IsNotEmpty()
  severity: SecuritySeverity;

  @ApiProperty({
    description: 'Title of the security event',
    example: 'Suspicious login attempt detected',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Detailed description of the event',
    example: 'Multiple failed login attempts from unusual location',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiPropertyOptional({
    description: 'User ID associated with the event',
    example: 'clx1234567890',
  })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata for the event',
    example: { attemptCount: 5, timeWindow: '15 minutes' },
  })
  @IsOptional()
  metadata?: any;
}

/**
 * DTO for recording user consent
 */
export class RecordConsentDto {
  @ApiProperty({
    description: 'Type of consent being recorded',
    enum: ConsentType,
    example: ConsentType.DATA_PROCESSING,
  })
  @IsEnum(ConsentType)
  @IsNotEmpty()
  consentType: ConsentType;

  @ApiProperty({
    description: 'Whether consent is granted or revoked',
    example: true,
  })
  @IsBoolean()
  granted: boolean;

  @ApiPropertyOptional({
    description: 'Privacy policy version',
    example: '2.0',
  })
  @IsOptional()
  @IsString()
  version?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata for consent',
    example: { source: 'registration_form', campaign: 'gdpr_update' },
  })
  @IsOptional()
  metadata?: any;
}

/**
 * DTO for requesting data export
 */
export class RequestDataExportDto {
  @ApiProperty({
    description: 'Type of data to export',
    enum: DataExportType,
    example: DataExportType.FULL,
  })
  @IsEnum(DataExportType)
  @IsNotEmpty()
  requestType: DataExportType;

  @ApiPropertyOptional({
    description: 'Export format',
    example: 'JSON',
    default: 'JSON',
  })
  @IsOptional()
  @IsString()
  format?: string;
}

/**
 * DTO for requesting data deletion
 */
export class RequestDataDeletionDto {
  @ApiProperty({
    description: 'Type of data to delete',
    enum: DataDeletionType,
    example: DataDeletionType.PERSONAL_DATA_ONLY,
  })
  @IsEnum(DataDeletionType)
  @IsNotEmpty()
  requestType: DataDeletionType;

  @ApiPropertyOptional({
    description: 'Reason for deletion request',
    example: 'No longer using the service',
  })
  @IsOptional()
  @IsString()
  reason?: string;
}

/**
 * DTO for encrypting a field
 */
export class EncryptFieldDto {
  @ApiProperty({
    description: 'Value to encrypt',
    example: 'sensitive data value',
  })
  @IsString()
  @IsNotEmpty()
  value: string;
}

/**
 * DTO for resolving a security event
 */
export class ResolveSecurityEventDto {
  @ApiProperty({
    description: 'Resolution details',
    example: 'False positive - legitimate user behavior confirmed',
  })
  @IsString()
  @IsNotEmpty()
  resolution: string;
}

/**
 * Response DTO for security event
 */
export class SecurityEventResponseDto {
  @ApiProperty({ description: 'Event ID' })
  id: string;

  @ApiProperty({ description: 'Event type', enum: SecurityEventType })
  eventType: SecurityEventType;

  @ApiProperty({ description: 'Event severity', enum: SecuritySeverity })
  severity: SecuritySeverity;

  @ApiProperty({ description: 'Event title' })
  title: string;

  @ApiProperty({ description: 'Event description' })
  description: string;

  @ApiPropertyOptional({ description: 'Associated user ID' })
  userId?: string;

  @ApiPropertyOptional({ description: 'IP address' })
  ipAddress?: string;

  @ApiPropertyOptional({ description: 'User agent' })
  userAgent?: string;

  @ApiPropertyOptional({ description: 'Geographic location' })
  location?: string;

  @ApiProperty({ description: 'Event timestamp' })
  timestamp: Date;

  @ApiProperty({ description: 'Whether event is resolved' })
  resolved: boolean;

  @ApiPropertyOptional({ description: 'Who resolved the event' })
  resolvedBy?: string;

  @ApiPropertyOptional({ description: 'When event was resolved' })
  resolvedAt?: Date;

  @ApiProperty({ description: 'Whether alert was sent' })
  alertSent: boolean;

  @ApiPropertyOptional({ description: 'Additional metadata' })
  metadata?: any;
}

/**
 * Response DTO for audit log entry
 */
export class AuditLogResponseDto {
  @ApiProperty({ description: 'Audit log ID' })
  id: string;

  @ApiProperty({ description: 'Action performed' })
  action: string;

  @ApiProperty({ description: 'Entity type affected' })
  entityType: string;

  @ApiPropertyOptional({ description: 'Entity ID affected' })
  entityId?: string;

  @ApiPropertyOptional({ description: 'User who performed action' })
  userId?: string;

  @ApiPropertyOptional({ description: 'User role' })
  userRole?: string;

  @ApiPropertyOptional({ description: 'IP address' })
  ipAddress?: string;

  @ApiPropertyOptional({ description: 'User agent' })
  userAgent?: string;

  @ApiPropertyOptional({ description: 'Geographic location' })
  location?: string;

  @ApiProperty({ description: 'Timestamp of action' })
  timestamp: Date;

  @ApiProperty({ description: 'Whether action was successful' })
  success: boolean;

  @ApiPropertyOptional({ description: 'Error message if failed' })
  errorMessage?: string;

  @ApiPropertyOptional({ description: 'Risk score (0-100)' })
  riskScore?: number;

  @ApiPropertyOptional({ description: 'Previous values (for updates)' })
  oldValues?: any;

  @ApiPropertyOptional({ description: 'New values (for creates/updates)' })
  newValues?: any;

  @ApiPropertyOptional({ description: 'Additional metadata' })
  metadata?: any;
}

/**
 * Response DTO for consent record
 */
export class ConsentResponseDto {
  @ApiProperty({ description: 'Consent type', enum: ConsentType })
  consentType: ConsentType;

  @ApiProperty({ description: 'Whether consent is granted' })
  granted: boolean;

  @ApiPropertyOptional({ description: 'When consent was granted' })
  grantedAt?: Date;

  @ApiPropertyOptional({ description: 'When consent was revoked' })
  revokedAt?: Date;

  @ApiProperty({ description: 'Privacy policy version' })
  version: string;
}

/**
 * Response DTO for data export request
 */
export class DataExportResponseDto {
  @ApiProperty({ description: 'Request ID' })
  id: string;

  @ApiProperty({ description: 'Export status', enum: ExportStatus })
  status: ExportStatus;

  @ApiProperty({ description: 'When request was made' })
  requestedAt: Date;

  @ApiPropertyOptional({ description: 'When processing started' })
  processedAt?: Date;

  @ApiPropertyOptional({ description: 'When export was completed' })
  completedAt?: Date;

  @ApiPropertyOptional({ description: 'Download URL for completed export' })
  downloadUrl?: string;

  @ApiPropertyOptional({ description: 'When download link expires' })
  expiresAt?: Date;

  @ApiPropertyOptional({ description: 'File size in bytes' })
  fileSize?: number;
}

/**
 * Response DTO for data deletion request
 */
export class DataDeletionResponseDto {
  @ApiProperty({ description: 'Request ID' })
  id: string;

  @ApiProperty({ description: 'Deletion status', enum: DeletionStatus })
  status: DeletionStatus;

  @ApiProperty({ description: 'When request was made' })
  requestedAt: Date;

  @ApiPropertyOptional({ description: 'When processing started' })
  processedAt?: Date;

  @ApiPropertyOptional({ description: 'When deletion was completed' })
  completedAt?: Date;

  @ApiPropertyOptional({ description: 'Summary of deleted data' })
  deletedData?: any;

  @ApiPropertyOptional({ description: 'Summary of retained data' })
  retainedData?: any;
}

/**
 * Response DTO for paginated results
 */
export class PaginatedResponseDto<T> {
  @ApiProperty({ description: 'Data items' })
  data: T[];

  @ApiProperty({ description: 'Total number of items' })
  total: number;

  @ApiProperty({ description: 'Current page number' })
  page: number;

  @ApiProperty({ description: 'Items per page' })
  limit: number;

  @ApiProperty({ description: 'Total number of pages' })
  totalPages: number;
}
