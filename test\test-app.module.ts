import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PrismaService } from '../src/utils/prisma.service';
import { ServiceTypeService } from '../src/immigration/service-type.service';
import { DocumentRequirementService } from '../src/immigration/document-requirement.service';
import { ApplicationService } from '../src/immigration/application.service';
import { ServiceTypeController } from './mocks/service-type.controller.mock';
import { DocumentRequirementController } from './mocks/document-requirement.controller.mock';
import { ApplicationController } from './mocks/application.controller.mock';
import { WorkflowStepController } from './mocks/workflow-step.controller.mock';

// Import actual controllers for E2E testing
import { PackagesController } from '../src/packages/packages.controller';
import { PackagesService } from '../src/packages/packages.service';
import { DocumentController } from '../src/document/document.controller';
import { DocumentService } from '../src/document/document.service';
import { UserController } from '../src/user/user.controller';
import { UserService } from '../src/user/user.service';
import { PaymentController } from '../src/payment/payment.controller';
import { PaymentService } from '../src/payment/payment.service';
import { MediaService } from '../src/media/media.service';
import { OtpService } from '../src/otp/otp.service';
import { MailerService } from '../src/mailer/mailer.service';
import { STRIPE_CLIENT } from '../src/config/stripe.config';
import { SupabaseService } from '../src/utils/supabase.service';

/**
 * A simplified test module that includes all immigration-related controllers and services
 * for comprehensive E2E testing
 */
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET') || 'test-secret',
        signOptions: { expiresIn: '1d' },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [
    ServiceTypeController,
    DocumentRequirementController,
    ApplicationController,
    WorkflowStepController,
    // Add actual controllers for E2E testing
    PackagesController,
    DocumentController,
    UserController,
    PaymentController,
  ],
  providers: [
    PrismaService,
    ServiceTypeService,
    DocumentRequirementService,
    ApplicationService,
    // Add actual services for E2E testing
    PackagesService,
    DocumentService,
    UserService,
    PaymentService,
    MediaService,
    OtpService,
    MailerService,
    // Mock Stripe client for testing
    {
      provide: STRIPE_CLIENT,
      useValue: {
        checkout: {
          sessions: {
            create: jest.fn().mockResolvedValue({
              url: 'https://checkout.stripe.com/pay/mock-session',
            }),
          },
        },
        webhooks: {
          constructEvent: jest.fn(),
        },
      },
    },
    // Mock Supabase service for testing
    {
      provide: SupabaseService,
      useValue: {
        getClient: jest.fn().mockReturnValue({
          storage: {
            from: jest.fn().mockReturnValue({
              upload: jest.fn().mockResolvedValue({
                data: {
                  path: 'mock-uploads/test-file.pdf',
                  fullPath: 'test-bucket/mock-uploads/test-file.pdf',
                },
                error: null,
              }),
              delete: jest.fn().mockResolvedValue({
                data: null,
                error: null,
              }),
              getPublicUrl: jest.fn().mockReturnValue({
                data: {
                  publicUrl:
                    'https://mock-supabase.com/storage/v1/object/public/test-bucket/mock-uploads/test-file.pdf',
                },
              }),
            }),
          },
        }),
      },
    },
  ],
})
export class TestAppModule {}
