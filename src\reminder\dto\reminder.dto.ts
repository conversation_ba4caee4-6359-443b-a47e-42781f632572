import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsUUID, IsDateString, IsDate } from 'class-validator';
import { Type } from 'class-transformer';
import { ReminderStatus } from '@prisma/client';

/**
 * DTO for creating a new reminder
 */
export class CreateReminderDto {
  @ApiProperty({
    description: 'The reminder message content',
    example: 'Submit your passport documents by the due date',
  })
  @IsString()
  message: string;

  @ApiProperty({
    description: 'The due date for the reminder',
    example: '2024-02-15T10:00:00.000Z',
  })
  @IsDateString()
  dueDate: string;

  @ApiProperty({
    description: 'The ID of the user who will receive the reminder',
    example: 'clx1234567890abcdef',
  })
  @IsUUID()
  userId: string;

  @ApiPropertyOptional({
    description: 'Optional application ID to link the reminder to',
    example: 'clx1234567890abcdef',
  })
  @IsUUID()
  @IsOptional()
  applicationId?: string;

  @ApiPropertyOptional({
    description: 'Optional document ID to link the reminder to',
    example: 'clx1234567890abcdef',
  })
  @IsUUID()
  @IsOptional()
  documentId?: string;
}

/**
 * DTO for updating reminder status
 */
export class UpdateReminderStatusDto {
  @ApiProperty({
    description: 'The status of the reminder',
    enum: ReminderStatus,
    example: ReminderStatus.COMPLETED,
  })
  @IsEnum(ReminderStatus)
  status: ReminderStatus;
}

/**
 * DTO for updating reminder details
 */
export class UpdateReminderDto {
  @ApiPropertyOptional({
    description: 'The reminder message content',
    example: 'Updated: Submit your passport documents by the due date',
  })
  @IsString()
  @IsOptional()
  message?: string;

  @ApiPropertyOptional({
    description: 'The due date for the reminder',
    example: '2024-02-20T10:00:00.000Z',
  })
  @IsDateString()
  @IsOptional()
  dueDate?: string;

  @ApiPropertyOptional({
    description: 'The status of the reminder',
    enum: ReminderStatus,
    example: ReminderStatus.PENDING,
  })
  @IsEnum(ReminderStatus)
  @IsOptional()
  status?: ReminderStatus;
}

/**
 * DTO for filtering reminders
 */
export class ReminderFilterDto {
  @ApiPropertyOptional({
    description: 'Filter by reminder status',
    enum: ReminderStatus,
    example: ReminderStatus.PENDING,
  })
  @IsEnum(ReminderStatus)
  @IsOptional()
  status?: ReminderStatus;

  @ApiPropertyOptional({
    description: 'Filter reminders due after this date',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsDateString()
  @IsOptional()
  fromDate?: string;

  @ApiPropertyOptional({
    description: 'Filter reminders due before this date',
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsDateString()
  @IsOptional()
  toDate?: string;

  @ApiPropertyOptional({
    description: 'Filter by application ID',
    example: 'clx1234567890abcdef',
  })
  @IsUUID()
  @IsOptional()
  applicationId?: string;

  @ApiPropertyOptional({
    description: 'Filter by document ID',
    example: 'clx1234567890abcdef',
  })
  @IsUUID()
  @IsOptional()
  documentId?: string;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    default: 1,
  })
  @IsOptional()
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    default: 10,
  })
  @IsOptional()
  limit?: number = 10;
}

/**
 * DTO for bulk operations on reminders
 */
export class BulkReminderDto {
  @ApiProperty({
    description: 'Array of reminder IDs to operate on',
    example: ['clx1234567890abcdef', 'clx0987654321fedcba'],
  })
  @IsUUID(4, { each: true })
  reminderIds: string[];
}

/**
 * Response DTO for reminder data
 */
export class ReminderResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the reminder',
    example: 'clx1234567890abcdef',
  })
  id: string;

  @ApiProperty({
    description: 'The reminder message content',
    example: 'Submit your passport documents by the due date',
  })
  message: string;

  @ApiProperty({
    description: 'The due date for the reminder',
    example: '2024-02-15T10:00:00.000Z',
  })
  dueDate: Date;

  @ApiProperty({
    description: 'The status of the reminder',
    enum: ReminderStatus,
    example: ReminderStatus.PENDING,
  })
  status: ReminderStatus;

  @ApiProperty({
    description: 'The ID of the user who will receive the reminder',
    example: 'clx1234567890abcdef',
  })
  userId: string;

  @ApiPropertyOptional({
    description: 'Optional application ID linked to the reminder',
    example: 'clx1234567890abcdef',
  })
  applicationId?: string;

  @ApiPropertyOptional({
    description: 'Optional document ID linked to the reminder',
    example: 'clx1234567890abcdef',
  })
  documentId?: string;

  @ApiProperty({
    description: 'When the reminder was created',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'When the reminder was last updated',
    example: '2024-01-15T10:30:00.000Z',
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Whether the reminder is overdue',
    example: false,
  })
  isOverdue?: boolean;
}

/**
 * Response DTO for paginated reminder list
 */
export class ReminderListResponseDto {
  @ApiProperty({
    description: 'Array of reminders',
    type: [ReminderResponseDto],
  })
  reminders: ReminderResponseDto[];

  @ApiProperty({
    description: 'Total number of reminders',
    example: 25,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 3,
  })
  totalPages: number;

  @ApiProperty({
    description: 'Number of pending reminders',
    example: 5,
  })
  pendingCount: number;

  @ApiProperty({
    description: 'Number of overdue reminders',
    example: 2,
  })
  overdueCount: number;
}
