import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsBoolean,
  IsOptional,
  IsUUID,
  IsDateString,
} from 'class-validator';
import { NotificationType } from '@prisma/client';

/**
 * DTO for creating a new notification
 */
export class CreateNotificationDto {
  @ApiProperty({
    description: 'The notification message content',
    example: 'Your document has been approved',
  })
  @IsString()
  message: string;

  @ApiPropertyOptional({
    description: 'The type of notification',
    enum: NotificationType,
    example: NotificationType.SUCCESS,
    default: NotificationType.INFO,
  })
  @IsEnum(NotificationType)
  @IsOptional()
  type?: NotificationType = NotificationType.INFO;

  @ApiProperty({
    description: 'The ID of the user who will receive the notification',
    example: 'clx1234567890abcdef',
  })
  @IsUUID()
  userId: string;
}

/**
 * DTO for updating notification read status
 */
export class UpdateNotificationReadDto {
  @ApiProperty({
    description: 'Whether the notification has been read',
    example: true,
  })
  @IsBoolean()
  read: boolean;
}

/**
 * DTO for filtering notifications
 */
export class NotificationFilterDto {
  @ApiPropertyOptional({
    description: 'Filter by notification type',
    enum: NotificationType,
    example: NotificationType.INFO,
  })
  @IsEnum(NotificationType)
  @IsOptional()
  type?: NotificationType;

  @ApiPropertyOptional({
    description: 'Filter by read status',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  read?: boolean;

  @ApiPropertyOptional({
    description: 'Filter notifications created after this date',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsDateString()
  @IsOptional()
  fromDate?: string;

  @ApiPropertyOptional({
    description: 'Filter notifications created before this date',
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsDateString()
  @IsOptional()
  toDate?: string;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    default: 1,
  })
  @IsOptional()
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    default: 10,
  })
  @IsOptional()
  limit?: number = 10;
}

/**
 * DTO for bulk operations on notifications
 */
export class BulkNotificationDto {
  @ApiProperty({
    description: 'Array of notification IDs to operate on',
    example: ['clx1234567890abcdef', 'clx0987654321fedcba'],
  })
  @IsUUID(4, { each: true })
  notificationIds: string[];
}

/**
 * Response DTO for notification data
 */
export class NotificationResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the notification',
    example: 'clx1234567890abcdef',
  })
  id: string;

  @ApiProperty({
    description: 'The notification message content',
    example: 'Your document has been approved',
  })
  message: string;

  @ApiProperty({
    description: 'The type of notification',
    enum: NotificationType,
    example: NotificationType.SUCCESS,
  })
  type: NotificationType;

  @ApiProperty({
    description: 'Whether the notification has been read',
    example: false,
  })
  read: boolean;

  @ApiProperty({
    description: 'The ID of the user who received the notification',
    example: 'clx1234567890abcdef',
  })
  userId: string;

  @ApiProperty({
    description: 'When the notification was created',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'When the notification was last updated',
    example: '2024-01-15T10:30:00.000Z',
  })
  updatedAt: Date;
}

/**
 * Response DTO for paginated notification list
 */
export class NotificationListResponseDto {
  @ApiProperty({
    description: 'Array of notifications',
    type: [NotificationResponseDto],
  })
  notifications: NotificationResponseDto[];

  @ApiProperty({
    description: 'Total number of notifications',
    example: 25,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 3,
  })
  totalPages: number;

  @ApiProperty({
    description: 'Number of unread notifications',
    example: 5,
  })
  unreadCount: number;
}
