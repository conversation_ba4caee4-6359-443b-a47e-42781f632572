// Mock for @react-email/components
const React = require('react');

const createComponent = (name) => {
  return ({ children, ...props }) => {
    return React.createElement(name, props, children);
  };
};

module.exports = {
  Body: createComponent('body'),
  Button: createComponent('button'),
  Container: createComponent('div'),
  Head: createComponent('head'),
  Heading: createComponent('h1'),
  Html: createComponent('html'),
  Img: createComponent('img'),
  Link: createComponent('a'),
  Preview: createComponent('div'),
  Section: createComponent('section'),
  Text: createComponent('p'),
  render: (component) => {
    return 'Mocked rendered email';
  }
};
