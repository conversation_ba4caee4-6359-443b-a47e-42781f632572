import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { SecurityMonitoringService } from './security-monitoring.service';
import { EncryptionService } from './encryption.service';
import { JwtAdmin } from '../guards/jwt.admin.guard';
import { JwtAdminAgent } from '../guards/jwt.admin-agent.guard';
import { SecurityEventType, SecuritySeverity } from '@prisma/client';
import {
  CreateSecurityEventDto,
  EncryptFieldDto,
  ResolveSecurityEventDto,
  SecurityEventResponseDto,
  PaginatedResponseDto,
} from './dto/security.dto';

/**
 * Security Management Controller
 *
 * Provides endpoints for security monitoring, event management,
 * and encryption operations. Restricted to admin and agent users.
 */
@ApiTags('Security')
@Controller('security')
@ApiBearerAuth()
export class SecurityController {
  constructor(
    private readonly securityMonitoring: SecurityMonitoringService,
    private readonly encryptionService: EncryptionService,
  ) {}

  /**
   * Get security events with filtering
   */
  @Get('events')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({
    summary: 'Get security events with filtering and pagination',
  })
  @ApiResponse({
    status: 200,
    description: 'Security events retrieved successfully',
  })
  @ApiQuery({ name: 'eventType', required: false, enum: SecurityEventType })
  @ApiQuery({ name: 'severity', required: false, enum: SecuritySeverity })
  @ApiQuery({ name: 'userId', required: false, type: String })
  @ApiQuery({ name: 'ipAddress', required: false, type: String })
  @ApiQuery({ name: 'resolved', required: false, type: Boolean })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async getSecurityEvents(
    @Query('eventType') eventType?: SecurityEventType,
    @Query('severity') severity?: SecuritySeverity,
    @Query('userId') userId?: string,
    @Query('ipAddress') ipAddress?: string,
    @Query('resolved') resolved?: boolean,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.securityMonitoring.getSecurityEvents({
      eventType,
      severity,
      userId,
      ipAddress,
      resolved,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      page,
      limit,
    });
  }

  /**
   * Resolve a security event
   */
  @Put('events/:eventId/resolve')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({ summary: 'Resolve a security event' })
  @ApiResponse({
    status: 200,
    description: 'Security event resolved successfully',
  })
  @ApiParam({ name: 'eventId', description: 'Security event ID' })
  @HttpCode(HttpStatus.OK)
  async resolveSecurityEvent(
    @Param('eventId') eventId: string,
    @Body('resolution') resolution: string,
    @Request() req: any,
  ) {
    const resolvedBy = req.user?.id || 'system';
    await this.securityMonitoring.resolveSecurityEvent(
      eventId,
      resolvedBy,
      resolution,
    );
    return { message: 'Security event resolved successfully' };
  }

  /**
   * Create a manual security event
   */
  @Post('events')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({ summary: 'Create a manual security event' })
  @ApiResponse({
    status: 201,
    description: 'Security event created successfully',
  })
  async createSecurityEvent(
    @Body() createEventDto: CreateSecurityEventDto,
    @Request() req: any,
  ) {
    const ipAddress = req.ip || req.connection?.remoteAddress;
    const userAgent = req.headers['user-agent'];

    await this.securityMonitoring.createSecurityEvent({
      ...createEventDto,
      ipAddress,
      userAgent,
    });

    return { message: 'Security event created successfully' };
  }

  /**
   * Get security metrics and statistics
   */
  @Get('metrics')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({ summary: 'Get security metrics and statistics' })
  @ApiResponse({
    status: 200,
    description: 'Security metrics retrieved successfully',
  })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  async getSecurityMetrics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.securityMonitoring.getSecurityMetrics({
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
    });
  }

  /**
   * Get encryption metadata for an entity
   */
  @Get('encryption/:entityType/:entityId')
  @UseGuards(JwtAdmin)
  @ApiOperation({ summary: 'Get encryption metadata for an entity' })
  @ApiResponse({
    status: 200,
    description: 'Encryption metadata retrieved successfully',
  })
  @ApiParam({
    name: 'entityType',
    description: 'Entity type (e.g., user, document)',
  })
  @ApiParam({ name: 'entityId', description: 'Entity ID' })
  async getEncryptionMetadata(
    @Param('entityType') entityType: string,
    @Param('entityId') entityId: string,
  ) {
    return this.encryptionService.getEncryptionMetadata(entityType, entityId);
  }

  /**
   * Check if a field is encrypted
   */
  @Get('encryption/:entityType/:entityId/:fieldName/status')
  @UseGuards(JwtAdmin)
  @ApiOperation({ summary: 'Check if a specific field is encrypted' })
  @ApiResponse({
    status: 200,
    description: 'Encryption status retrieved successfully',
  })
  @ApiParam({ name: 'entityType', description: 'Entity type' })
  @ApiParam({ name: 'entityId', description: 'Entity ID' })
  @ApiParam({ name: 'fieldName', description: 'Field name' })
  async checkFieldEncryption(
    @Param('entityType') entityType: string,
    @Param('entityId') entityId: string,
    @Param('fieldName') fieldName: string,
  ) {
    const isEncrypted = await this.encryptionService.isFieldEncrypted(
      entityType,
      entityId,
      fieldName,
    );
    return { isEncrypted };
  }

  /**
   * Encrypt a field manually (admin only)
   */
  @Post('encryption/:entityType/:entityId/:fieldName')
  @UseGuards(JwtAdmin)
  @ApiOperation({ summary: 'Manually encrypt a field' })
  @ApiResponse({ status: 201, description: 'Field encrypted successfully' })
  @ApiParam({ name: 'entityType', description: 'Entity type' })
  @ApiParam({ name: 'entityId', description: 'Entity ID' })
  @ApiParam({ name: 'fieldName', description: 'Field name' })
  async encryptField(
    @Param('entityType') entityType: string,
    @Param('entityId') entityId: string,
    @Param('fieldName') fieldName: string,
    @Body('value') value: string,
  ) {
    await this.encryptionService.encryptField(
      entityType,
      entityId,
      fieldName,
      value,
    );
    return { message: 'Field encrypted successfully' };
  }

  /**
   * Delete encrypted fields for an entity (GDPR compliance)
   */
  @Post('encryption/:entityType/:entityId/delete')
  @UseGuards(JwtAdmin)
  @ApiOperation({ summary: 'Delete encrypted fields for GDPR compliance' })
  @ApiResponse({
    status: 200,
    description: 'Encrypted fields deleted successfully',
  })
  @ApiParam({ name: 'entityType', description: 'Entity type' })
  @ApiParam({ name: 'entityId', description: 'Entity ID' })
  @HttpCode(HttpStatus.OK)
  async deleteEncryptedFields(
    @Param('entityType') entityType: string,
    @Param('entityId') entityId: string,
    @Body('fieldNames') fieldNames?: string[],
  ) {
    await this.encryptionService.deleteEncryptedFields(
      entityType,
      entityId,
      fieldNames,
    );
    return { message: 'Encrypted fields deleted successfully' };
  }

  /**
   * Get security dashboard summary
   */
  @Get('dashboard')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({ summary: 'Get security dashboard summary' })
  @ApiResponse({
    status: 200,
    description: 'Security dashboard data retrieved successfully',
  })
  async getSecurityDashboard() {
    const [metrics, recentEvents] = await Promise.all([
      this.securityMonitoring.getSecurityMetrics({
        startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
      }),
      this.securityMonitoring.getSecurityEvents({
        page: 1,
        limit: 10,
        resolved: false,
      }),
    ]);

    return {
      metrics,
      recentEvents: recentEvents.data,
      summary: {
        totalUnresolvedEvents: metrics.unresolvedEvents,
        criticalEvents: metrics.criticalEvents,
        highEvents: metrics.highEvents,
        recentFailedLogins: metrics.recentFailedLogins,
        resolutionRate: metrics.resolutionRate,
      },
    };
  }

  /**
   * Test security monitoring (development only)
   */
  @Post('test/monitoring')
  @UseGuards(JwtAdmin)
  @ApiOperation({ summary: 'Test security monitoring (development only)' })
  @ApiResponse({ status: 201, description: 'Test security event created' })
  async testSecurityMonitoring(@Request() req: any) {
    const ipAddress = req.ip || req.connection?.remoteAddress;
    const userAgent = req.headers['user-agent'];

    await this.securityMonitoring.createSecurityEvent({
      eventType: SecurityEventType.SUSPICIOUS_ACTIVITY,
      severity: SecuritySeverity.MEDIUM,
      title: 'Test Security Event',
      description: 'This is a test security event for monitoring validation',
      userId: req.user?.id,
      ipAddress,
      userAgent,
      metadata: {
        test: true,
        timestamp: new Date(),
      },
    });

    return { message: 'Test security event created successfully' };
  }
}
