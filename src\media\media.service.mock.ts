/**
 * Mock implementation of MediaService for testing purposes
 * This prevents tests from uploading files to the actual Supabase storage
 */
import { Injectable } from '@nestjs/common';

@Injectable()
export class MockMediaService {
  /**
   * Mock implementation of file upload that returns a fake URL
   * instead of uploading to Supabase
   * 
   * @param file - The file to "upload"
   * @param folder - The target folder
   * @returns A mock upload result with a fake URL
   */
  async uploadFile(file: Express.Multer.File, folder: string) {
    // Generate a fake URL based on the file name and folder
    const fileName = file.originalname.replace(/\s+/g, '-').toLowerCase();
    const fakeUrl = `https://mock-supabase-storage.com/${folder}/${Date.now()}-${fileName}`;
    
    return {
      url: fakeUrl,
      path: `${folder}/${fileName}`,
      size: file.size,
      mimetype: file.mimetype,
    };
  }

  /**
   * Mock implementation of file deletion that does nothing
   * 
   * @param path - The path of the file to "delete"
   * @returns A success message
   */
  async deleteFile(path: string) {
    return { message: 'File deleted successfully (mock)' };
  }
}
