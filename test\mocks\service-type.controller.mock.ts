import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  Query,
} from '@nestjs/common';
import { ServiceTypeService } from '../../src/immigration/service-type.service';
import {
  CreateServiceTypeDto,
  UpdateServiceTypeDto,
  FilterServiceTypeDto,
} from '../../src/immigration/dto/service-type.dto';

/**
 * Mock implementation of ServiceTypeController for testing
 * This controller doesn't use guards for testing purposes
 */
@Controller('admin/service-types')
export class ServiceTypeController {
  constructor(private readonly serviceTypeService: ServiceTypeService) {}

  @Post()
  async create(@Body() createServiceTypeDto: CreateServiceTypeDto) {
    return this.serviceTypeService.create(createServiceTypeDto);
  }

  @Get()
  async findAll(@Query() filterDto: FilterServiceTypeDto) {
    return this.serviceTypeService.findAll(filterDto);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.serviceTypeService.findOne(id);
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateServiceTypeDto: UpdateServiceTypeDto,
  ) {
    return this.serviceTypeService.update(id, updateServiceTypeDto);
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    return this.serviceTypeService.remove(id);
  }

  @Get(':id/document-requirements')
  async getDocumentRequirements(@Param('id') id: string) {
    return this.serviceTypeService.getDocumentRequirements(id);
  }
}
