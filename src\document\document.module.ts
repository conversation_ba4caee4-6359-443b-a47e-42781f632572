import { Modu<PERSON> } from '@nestjs/common';
import { DocumentController } from './document.controller';
import { DocumentService } from './document.service';
import { PrismaService } from 'src/utils/prisma.service';
import { MediaService } from 'src/media/media.service';
import { SupabaseService } from 'src/utils/supabase.service';
import { JwtService } from '@nestjs/jwt';

@Module({
  controllers: [DocumentController],
  providers: [
    DocumentService,
    PrismaService,
    MediaService,
    SupabaseService,
    JwtService,
  ],
  exports: [DocumentService],
})
export class DocumentModule {}
