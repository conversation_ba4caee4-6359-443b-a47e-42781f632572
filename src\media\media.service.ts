import { BadRequestException, Injectable } from '@nestjs/common';
import { SupabaseService } from 'src/utils/supabase.service';

interface IMedia {
  data: {
    path: string;
    id: string;
    fullPath: string;
  };

  error: any;
}
@Injectable()
export class MediaService {
  private supabase = this.supabaseService.getClient();

  constructor(private supabaseService: SupabaseService) {}

  async uploadFile(file: Express.Multer.File, folder: string) {
    const date = Date.now();
    const fileName = `${folder}/${date}_${file.originalname}`;

    const { data, error } = (await this.supabase.storage
      .from(process.env.BUCKET_NAME)
      .upload(fileName, file.buffer, {
        contentType: file.mimetype,
      })) as IMedia;
    if (error) throw new BadRequestException(error);

    return {
      status: 'OK',
      url: `${data.fullPath}`,
    };
  }
  async uploadFiles(files: Array<Express.Multer.File>, folder: string) {
    if (files.length > 10)
      throw new BadRequestException('Maximum number of files allowed is 10');
    const uploadPromises = files.map(async (file) => {
      const date = Date.now();
      const fileName = `${folder}/${date}_${file.originalname}`;

      const { data, error } = (await this.supabase.storage
        .from(process.env.BUCKET_NAME)
        .upload(fileName, file.buffer, {
          contentType: file.mimetype,
        })) as IMedia;
      if (error) throw new BadRequestException(error);
      return `${data.fullPath}`;
    });

    const results = await Promise.all(uploadPromises);

    return {
      status: 'OK',
      url: results,
    };
  }

  /**
   * Test Supabase connection
   *
   * This method tests the connection to Supabase by listing buckets.
   * It's used for debugging purposes.
   *
   * @returns Information about the Supabase connection
   */
  async testSupabaseConnection() {
    try {
      // Check if environment variables are set
      const supabaseUrl = process.env.SUPABASE_URL;
      const supabaseKey = process.env.SUPABASE_KEY;
      const bucketName = process.env.BUCKET_NAME;

      if (!supabaseUrl || !supabaseKey || !bucketName) {
        return {
          success: false,
          message: 'Supabase environment variables are not properly configured',
          config: {
            supabaseUrl: supabaseUrl ? 'Set' : 'Not set',
            supabaseKey: supabaseKey ? 'Set (masked)' : 'Not set',
            bucketName: bucketName ? bucketName : 'Not set',
          },
        };
      }

      // Test listing buckets
      const { data, error } = await this.supabase.storage.listBuckets();

      if (error) {
        return {
          success: false,
          message: 'Failed to connect to Supabase',
          error: error.message,
        };
      }

      // Check if the bucket exists
      const bucketExists = data.some((bucket) => bucket.name === bucketName);

      return {
        success: true,
        message: 'Successfully connected to Supabase',
        buckets: data.map((bucket) => bucket.name),
        bucketExists,
      };
    } catch (error) {
      return {
        success: false,
        message: 'Error testing Supabase connection',
        error: error.message,
      };
    }
  }
}
