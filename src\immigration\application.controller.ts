import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { ApplicationService } from './application.service';
import {
  CreateApplicationDto,
  UpdateApplicationDto,
  FilterApplicationDto,
  CreateWorkflowStepDto,
  UpdateWorkflowStepDto,
  UpdateWorkflowDto,
  CreateCheckpointCallDto,
  UpdateCheckpointCallDto,
  CreateApplicationQueryDto,
  RespondToQueryDto,
  EnhancedApplicationProgressDto,
  ApplicationResponseDto,
  PaginatedApplicationResponseDto,
  WorkflowStepResponseDto,
} from './dto/application.dto';
import { JwtGuard } from '../guards/jwt.guard';
import { JwtAdmin } from '../guards/jwt.admin.guard';
import { JwtAdminAgent } from '../guards/jwt.admin-agent.guard';

/**
 * Controller for managing applications and workflows
 *
 * Provides endpoints for:
 * - Application CRUD operations
 * - Workflow management
 * - Checkpoint calls
 * - Application queries
 * - Progress tracking
 */
@ApiTags('Applications')
@Controller('applications')
export class ApplicationController {
  constructor(private readonly applicationService: ApplicationService) {}

  /**
   * Create a new application
   */
  @Post()
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new application' })
  @ApiResponse({
    status: 201,
    description: 'Application created successfully.',
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Service type not found.' })
  async create(
    @Body() createApplicationDto: CreateApplicationDto,
    @Request() req,
  ) {
    return this.applicationService.create(req.user.id, createApplicationDto);
  }

  /**
   * Get all applications with filtering
   */
  @Get()
  @UseGuards(JwtAdminAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all applications with filtering (Admin/Agent)',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by status',
  })
  @ApiQuery({
    name: 'serviceTypeId',
    required: false,
    description: 'Filter by service type',
  })
  @ApiQuery({
    name: 'userId',
    required: false,
    description: 'Filter by user (Admin only)',
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  @ApiResponse({
    status: 200,
    description: 'List of applications.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  async findAll(@Query() filterDto: FilterApplicationDto, @Request() req) {
    return this.applicationService.findAll(
      filterDto,
      req.user.role,
      req.user.id,
    );
  }

  /**
   * Get user's own applications
   */
  @Get('my-applications')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get current user's applications" })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by status',
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  @ApiResponse({
    status: 200,
    description: "List of user's applications.",
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async findMyApplications(
    @Query() filterDto: FilterApplicationDto,
    @Request() req,
  ) {
    return this.applicationService.findAll(
      filterDto,
      req.user.role,
      req.user.id,
    );
  }

  /**
   * Get application statistics (Admin only)
   */
  @Get('statistics')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get application statistics (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Application statistics.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  async getStatistics() {
    return this.applicationService.getStatistics();
  }

  /**
   * Get a specific application
   */
  @Get(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get application by ID' })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiResponse({
    status: 200,
    description: 'Application details.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Application not found.' })
  async findOne(@Param('id') id: string, @Request() req) {
    return this.applicationService.findOne(id, req.user.role, req.user.id);
  }

  /**
   * Update an application
   */
  @Patch(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update an application' })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiResponse({
    status: 200,
    description: 'Application updated successfully.',
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Application not found.' })
  async update(
    @Param('id') id: string,
    @Body() updateApplicationDto: UpdateApplicationDto,
    @Request() req,
  ) {
    return this.applicationService.update(
      id,
      updateApplicationDto,
      req.user.role,
      req.user.id,
    );
  }

  /**
   * Delete an application (only DRAFT status)
   */
  @Delete(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete an application (DRAFT only)' })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiResponse({
    status: 200,
    description: 'Application deleted successfully.',
  })
  @ApiResponse({
    status: 400,
    description: 'Cannot delete non-draft application.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Application not found.' })
  async remove(@Param('id') id: string, @Request() req) {
    return this.applicationService.remove(id, req.user.role, req.user.id);
  }

  /**
   * Get application progress
   */
  @Get(':id/progress')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get application progress' })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiResponse({
    status: 200,
    description: 'Application progress information.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Application not found.' })
  async getProgress(@Param('id') id: string, @Request() req) {
    return this.applicationService.getProgress(id, req.user.role, req.user.id);
  }

  /**
   * Advance workflow to next step (Admin only)
   */
  @Patch(':id/workflow')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Advance application workflow (Admin only)' })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiResponse({
    status: 200,
    description: 'Workflow advanced successfully.',
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Application not found.' })
  async advanceWorkflow(
    @Param('id') id: string,
    @Body() updateWorkflowDto: UpdateWorkflowDto,
    @Request() req,
  ) {
    return this.applicationService.advanceWorkflow(
      id,
      updateWorkflowDto,
      req.user.role,
      req.user.id,
    );
  }

  /**
   * Get checkpoint calls for an application
   */
  @Get(':id/checkpoint-calls')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get checkpoint calls for an application' })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiResponse({
    status: 200,
    description: 'List of checkpoint calls.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Application not found.' })
  async getCheckpointCalls(@Param('id') id: string, @Request() req) {
    return this.applicationService.getCheckpointCalls(
      id,
      req.user.role,
      req.user.id,
    );
  }

  /**
   * Create a checkpoint call (Admin only)
   */
  @Post('checkpoint-calls')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a checkpoint call (Admin only)' })
  @ApiResponse({
    status: 201,
    description: 'Checkpoint call created successfully.',
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Application not found.' })
  async createCheckpointCall(
    @Body() createCheckpointCallDto: CreateCheckpointCallDto,
  ) {
    return this.applicationService.createCheckpointCall(
      createCheckpointCallDto,
    );
  }

  /**
   * Update a checkpoint call (Admin only)
   */
  @Patch('checkpoint-calls/:callId')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a checkpoint call (Admin only)' })
  @ApiParam({ name: 'callId', description: 'Checkpoint call ID' })
  @ApiResponse({
    status: 200,
    description: 'Checkpoint call updated successfully.',
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Checkpoint call not found.' })
  async updateCheckpointCall(
    @Param('callId') callId: string,
    @Body() updateCheckpointCallDto: UpdateCheckpointCallDto,
  ) {
    return this.applicationService.updateCheckpointCall(
      callId,
      updateCheckpointCallDto,
    );
  }

  /**
   * Get queries for an application
   */
  @Get(':id/queries')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get queries for an application' })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiResponse({
    status: 200,
    description: 'List of application queries.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Application not found.' })
  async getQueries(@Param('id') id: string, @Request() req) {
    return this.applicationService.getQueries(id, req.user.role, req.user.id);
  }

  /**
   * Create an application query
   */
  @Post('queries')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create an application query' })
  @ApiResponse({
    status: 201,
    description: 'Query created successfully.',
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Application not found.' })
  async createQuery(
    @Body() createApplicationQueryDto: CreateApplicationQueryDto,
    @Request() req,
  ) {
    return this.applicationService.createQuery(
      createApplicationQueryDto,
      req.user.role,
      req.user.id,
    );
  }

  /**
   * Respond to an application query (Admin only)
   */
  @Patch('queries/:queryId/respond')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Respond to an application query (Admin only)' })
  @ApiParam({ name: 'queryId', description: 'Query ID' })
  @ApiResponse({
    status: 200,
    description: 'Query response added successfully.',
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Query not found.' })
  async respondToQuery(
    @Param('queryId') queryId: string,
    @Body() respondToQueryDto: RespondToQueryDto,
  ) {
    return this.applicationService.respondToQuery(queryId, respondToQueryDto);
  }

  /**
   * Get enhanced application progress with detailed step information
   */
  @Get(':id/enhanced-progress')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get enhanced application progress with detailed step information',
    description:
      'Get comprehensive progress information including completed steps, remaining steps, overdue status, and detailed calculations',
  })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiResponse({
    status: 200,
    description: 'Enhanced application progress information.',
    type: EnhancedApplicationProgressDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Application not found.' })
  async getEnhancedProgress(
    @Param('id') id: string,
    @Request() req,
  ): Promise<EnhancedApplicationProgressDto> {
    return this.applicationService.getEnhancedProgress(
      id,
      req.user.role,
      req.user.id,
    );
  }

  /**
   * Get all applications with enhanced filtering and pagination
   */
  @Get('enhanced/all')
  @UseGuards(JwtAdminAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary:
      'Get all applications with enhanced filtering and pagination (Admin/Agent)',
    description:
      'Get applications with comprehensive filtering, pagination, and progress calculations',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by application status',
    enum: ['DRAFT', 'SUBMITTED', 'IN_PROGRESS', 'COMPLETED', 'REJECTED'],
  })
  @ApiQuery({
    name: 'serviceTypeId',
    required: false,
    description: 'Filter by service type ID',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10, max: 100)',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: ['createdAt', 'submissionDate', 'status', 'completionDate'],
    description: 'Sort field (default: createdAt)',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['asc', 'desc'],
    description: 'Sort order (default: desc)',
  })
  @ApiResponse({
    status: 200,
    description: 'Enhanced list of applications with progress information.',
    type: PaginatedApplicationResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  async findAllEnhanced(
    @Query() filterDto: FilterApplicationDto,
    @Request() req,
  ): Promise<PaginatedApplicationResponseDto> {
    return this.applicationService.findAllEnhanced(
      filterDto,
      req.user.role,
      req.user.id,
    );
  }

  /**
   * Create workflow steps for a service type (Admin only)
   */
  @Post('workflow-steps')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Create workflow steps for a service type (Admin only)',
    description:
      'Create new workflow steps that define the process for a specific service type',
  })
  @ApiResponse({
    status: 201,
    description: 'Workflow step created successfully.',
    type: WorkflowStepResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Service type not found.' })
  async createWorkflowStep(
    @Body() createWorkflowStepDto: CreateWorkflowStepDto,
  ): Promise<WorkflowStepResponseDto> {
    return this.applicationService.createWorkflowStep(createWorkflowStepDto);
  }

  /**
   * Update workflow step (Admin only)
   */
  @Patch('workflow-steps/:stepId')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update workflow step (Admin only)',
    description: 'Update an existing workflow step configuration',
  })
  @ApiParam({ name: 'stepId', description: 'Workflow step ID' })
  @ApiResponse({
    status: 200,
    description: 'Workflow step updated successfully.',
    type: WorkflowStepResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Workflow step not found.' })
  async updateWorkflowStep(
    @Param('stepId') stepId: string,
    @Body() updateWorkflowStepDto: UpdateWorkflowStepDto,
  ): Promise<WorkflowStepResponseDto> {
    return this.applicationService.updateWorkflowStep(
      stepId,
      updateWorkflowStepDto,
    );
  }

  /**
   * Delete workflow step (Admin only)
   */
  @Delete('workflow-steps/:stepId')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Delete workflow step (Admin only)',
    description:
      'Delete a workflow step (only if not used by active applications)',
  })
  @ApiParam({ name: 'stepId', description: 'Workflow step ID' })
  @ApiResponse({
    status: 200,
    description: 'Workflow step deleted successfully.',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - step is being used by active applications.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Workflow step not found.' })
  async deleteWorkflowStep(
    @Param('stepId') stepId: string,
  ): Promise<{ message: string }> {
    return this.applicationService.deleteWorkflowStep(stepId);
  }

  /**
   * Get workflow steps for a service type
   */
  @Get('workflow-steps/:serviceTypeId')
  @UseGuards(JwtAdminAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get workflow steps for a service type (Admin/Agent)',
    description:
      'Retrieve all workflow steps configured for a specific service type',
  })
  @ApiParam({ name: 'serviceTypeId', description: 'Service type ID' })
  @ApiResponse({
    status: 200,
    description: 'List of workflow steps for the service type.',
    type: [WorkflowStepResponseDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  async getWorkflowSteps(
    @Param('serviceTypeId') serviceTypeId: string,
  ): Promise<WorkflowStepResponseDto[]> {
    return this.applicationService.getWorkflowSteps(serviceTypeId);
  }
}
