import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { DocumentRequirementService } from './document-requirement.service';
import {
  CreateDocumentRequirementDto,
  UpdateDocumentRequirementDto,
  FilterDocumentRequirementDto,
  DocumentRequirementResponseDto,
} from './dto/document-requirement.dto';
import { JwtAdmin } from '../guards/jwt.admin.guard';

@ApiTags('Document Requirements')
@Controller('document-requirements')
export class DocumentRequirementController {
  private readonly logger = new Logger(DocumentRequirementController.name);

  constructor(
    private readonly documentRequirementService: DocumentRequirementService,
  ) {}

  // ==================== ADMIN ENDPOINTS ====================

  @Post('admin')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new document requirement (Admin only)' })
  @ApiResponse({
    status: 201,
    description: 'The document requirement has been successfully created.',
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  async createAdmin(
    @Body() createDocumentRequirementDto: CreateDocumentRequirementDto,
  ) {
    this.logger.log(
      'Admin creating document requirement:',
      createDocumentRequirementDto,
    );
    return this.documentRequirementService.create(createDocumentRequirementDto);
  }

  @Get('admin')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all document requirements (Admin only)',
    description:
      'Retrieve all document requirements with filtering and pagination',
  })
  @ApiResponse({
    status: 200,
    description: 'List of document requirements.',
    type: [DocumentRequirementResponseDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search in name and description',
  })
  @ApiQuery({
    name: 'category',
    required: false,
    description: 'Filter by category (EMPLOYEE/EMPLOYER)',
  })
  @ApiQuery({
    name: 'required',
    required: false,
    description: 'Filter by required status',
  })
  @ApiQuery({
    name: 'serviceTypeId',
    required: false,
    description: 'Filter by service type ID',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page (default: 10, max: 100)',
  })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sort field' })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    description: 'Sort order (asc/desc)',
  })
  async findAllAdmin(@Query() filterDto: FilterDocumentRequirementDto) {
    this.logger.log(
      'Admin request for document requirements with filters:',
      filterDto,
    );
    return this.documentRequirementService.findAll(filterDto);
  }

  @Get('admin/:id')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a document requirement by ID (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'The document requirement.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Document requirement not found.' })
  async findOneAdmin(@Param('id') id: string) {
    this.logger.log(`Admin request for document requirement: ${id}`);
    return this.documentRequirementService.findOne(id);
  }

  @Put('admin/:id')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a document requirement (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'The document requirement has been successfully updated.',
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Document requirement not found.' })
  async updateAdmin(
    @Param('id') id: string,
    @Body() updateDocumentRequirementDto: UpdateDocumentRequirementDto,
  ) {
    this.logger.log(
      `Admin updating document requirement: ${id}`,
      updateDocumentRequirementDto,
    );
    return this.documentRequirementService.update(
      id,
      updateDocumentRequirementDto,
    );
  }

  @Delete('admin/:id')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a document requirement (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'The document requirement has been successfully deleted.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Document requirement not found.' })
  async removeAdmin(@Param('id') id: string) {
    this.logger.log(`Admin deleting document requirement: ${id}`);
    return this.documentRequirementService.remove(id);
  }
}
