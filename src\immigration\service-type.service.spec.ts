import { Test, TestingModule } from '@nestjs/testing';
import { ServiceTypeService } from './service-type.service';
import { PrismaService } from '../utils/prisma.service';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { CreateServiceTypeDto, UpdateServiceTypeDto, FilterServiceTypeDto } from './dto/service-type.dto';

describe('ServiceTypeService', () => {
  let service: ServiceTypeService;
  let prismaService: PrismaService;

  const mockPrismaService = {
    service_type: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    application: {
      count: jest.fn(),
    },
    document_requirement: {
      findMany: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ServiceTypeService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<ServiceTypeService>(ServiceTypeService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a service type', async () => {
      const createDto: CreateServiceTypeDto = {
        name: 'Test Service',
        description: 'Test Description',
        price: 1000,
      };

      const expectedResult = {
        id: 'test-id',
        ...createDto,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.service_type.create.mockResolvedValue(expectedResult);

      const result = await service.create(createDto);

      expect(result).toEqual(expectedResult);
      expect(mockPrismaService.service_type.create).toHaveBeenCalledWith({
        data: createDto,
      });
    });

    it('should throw BadRequestException if creation fails', async () => {
      const createDto: CreateServiceTypeDto = {
        name: 'Test Service',
        description: 'Test Description',
        price: 1000,
      };

      mockPrismaService.service_type.create.mockRejectedValue(new Error('Database error'));

      await expect(service.create(createDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('findAll', () => {
    it('should return all service types with pagination', async () => {
      const filterDto: FilterServiceTypeDto = {
        page: 1,
        limit: 10,
      };

      const serviceTypes = [
        {
          id: 'test-id-1',
          name: 'Test Service 1',
          description: 'Test Description 1',
          price: 1000,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'test-id-2',
          name: 'Test Service 2',
          description: 'Test Description 2',
          price: 2000,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockPrismaService.service_type.count.mockResolvedValue(2);
      mockPrismaService.service_type.findMany.mockResolvedValue(serviceTypes);

      const result = await service.findAll(filterDto);

      expect(result).toEqual({
        data: serviceTypes,
        total: 2,
        page: 1,
        limit: 10,
        totalPages: 1,
      });
      expect(mockPrismaService.service_type.count).toHaveBeenCalled();
      expect(mockPrismaService.service_type.findMany).toHaveBeenCalledWith({
        where: {},
        skip: 0,
        take: 10,
        orderBy: { name: 'asc' },
      });
    });

    it('should apply search filter', async () => {
      const filterDto: FilterServiceTypeDto = {
        search: 'test',
        page: 1,
        limit: 10,
      };

      mockPrismaService.service_type.count.mockResolvedValue(1);
      mockPrismaService.service_type.findMany.mockResolvedValue([]);

      await service.findAll(filterDto);

      expect(mockPrismaService.service_type.count).toHaveBeenCalledWith({
        where: {
          OR: [
            { name: { contains: 'test', mode: 'insensitive' } },
            { description: { contains: 'test', mode: 'insensitive' } },
          ],
        },
      });
    });

    it('should apply price filters', async () => {
      const filterDto: FilterServiceTypeDto = {
        minPrice: 500,
        maxPrice: 2000,
        page: 1,
        limit: 10,
      };

      mockPrismaService.service_type.count.mockResolvedValue(1);
      mockPrismaService.service_type.findMany.mockResolvedValue([]);

      await service.findAll(filterDto);

      expect(mockPrismaService.service_type.count).toHaveBeenCalledWith({
        where: {
          price: {
            gte: 500,
            lte: 2000,
          },
        },
      });
    });
  });

  describe('findOne', () => {
    it('should return a service type by id', async () => {
      const serviceType = {
        id: 'test-id',
        name: 'Test Service',
        description: 'Test Description',
        price: 1000,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.service_type.findUnique.mockResolvedValue(serviceType);

      const result = await service.findOne('test-id');

      expect(result).toEqual(serviceType);
      expect(mockPrismaService.service_type.findUnique).toHaveBeenCalledWith({
        where: { id: 'test-id' },
      });
    });

    it('should throw NotFoundException if service type not found', async () => {
      mockPrismaService.service_type.findUnique.mockResolvedValue(null);

      await expect(service.findOne('non-existent-id')).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a service type', async () => {
      const updateDto: UpdateServiceTypeDto = {
        name: 'Updated Service',
      };

      const serviceType = {
        id: 'test-id',
        name: 'Test Service',
        description: 'Test Description',
        price: 1000,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const updatedServiceType = {
        ...serviceType,
        name: 'Updated Service',
      };

      mockPrismaService.service_type.findUnique.mockResolvedValue(serviceType);
      mockPrismaService.service_type.update.mockResolvedValue(updatedServiceType);

      const result = await service.update('test-id', updateDto);

      expect(result).toEqual(updatedServiceType);
      expect(mockPrismaService.service_type.findUnique).toHaveBeenCalledWith({
        where: { id: 'test-id' },
      });
      expect(mockPrismaService.service_type.update).toHaveBeenCalledWith({
        where: { id: 'test-id' },
        data: { name: 'Updated Service' },
      });
    });

    it('should throw NotFoundException if service type not found', async () => {
      mockPrismaService.service_type.findUnique.mockResolvedValue(null);

      await expect(service.update('non-existent-id', {})).rejects.toThrow(NotFoundException);
    });
  });

  describe('remove', () => {
    it('should delete a service type', async () => {
      const serviceType = {
        id: 'test-id',
        name: 'Test Service',
        description: 'Test Description',
        price: 1000,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.service_type.findUnique.mockResolvedValue(serviceType);
      mockPrismaService.application.count.mockResolvedValue(0);
      mockPrismaService.service_type.delete.mockResolvedValue(serviceType);

      const result = await service.remove('test-id');

      expect(result).toEqual(serviceType);
      expect(mockPrismaService.service_type.findUnique).toHaveBeenCalledWith({
        where: { id: 'test-id' },
      });
      expect(mockPrismaService.application.count).toHaveBeenCalledWith({
        where: { serviceTypeId: 'test-id' },
      });
      expect(mockPrismaService.service_type.delete).toHaveBeenCalledWith({
        where: { id: 'test-id' },
      });
    });

    it('should throw NotFoundException if service type not found', async () => {
      mockPrismaService.service_type.findUnique.mockResolvedValue(null);

      await expect(service.remove('non-existent-id')).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException if service type has associated applications', async () => {
      const serviceType = {
        id: 'test-id',
        name: 'Test Service',
        description: 'Test Description',
        price: 1000,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.service_type.findUnique.mockResolvedValue(serviceType);
      mockPrismaService.application.count.mockResolvedValue(2);

      await expect(service.remove('test-id')).rejects.toThrow(BadRequestException);
    });
  });

  describe('getDocumentRequirements', () => {
    it('should return document requirements for a service type', async () => {
      const serviceType = {
        id: 'test-id',
        name: 'Test Service',
        description: 'Test Description',
        price: 1000,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const documentRequirements = [
        {
          id: 'doc-req-1',
          name: 'Passport',
          description: 'Valid passport',
          required: true,
          category: 'EMPLOYEE',
          serviceTypeId: 'test-id',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockPrismaService.service_type.findUnique.mockResolvedValue(serviceType);
      mockPrismaService.document_requirement.findMany.mockResolvedValue(documentRequirements);

      const result = await service.getDocumentRequirements('test-id');

      expect(result).toEqual(documentRequirements);
      expect(mockPrismaService.service_type.findUnique).toHaveBeenCalledWith({
        where: { id: 'test-id' },
      });
      expect(mockPrismaService.document_requirement.findMany).toHaveBeenCalledWith({
        where: { serviceTypeId: 'test-id' },
        orderBy: { name: 'asc' },
      });
    });

    it('should throw NotFoundException if service type not found', async () => {
      mockPrismaService.service_type.findUnique.mockResolvedValue(null);

      await expect(service.getDocumentRequirements('non-existent-id')).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});
