/**
 * Document Management Service
 *
 * This service handles all document-related operations including:
 * - Document upload and storage
 * - Document retrieval and filtering
 * - Document verification workflow
 * - Document deletion
 *
 * The service implements role-based access control with different
 * permissions for regular users, agents, and administrators.
 */
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from 'src/utils/prisma.service';
import { MediaService } from 'src/media/media.service';
import {
  DocumentFilterDto,
  UploadDocumentDto,
  VerifyDocumentDto,
  DocumentResponseDto,
  PaginatedDocumentResponseDto,
  BulkDocumentOperationDto,
  BulkVerifyDocumentDto,
} from './dto/document.dto';
import { VerificationStatus } from '@prisma/client';

/**
 * Service responsible for document management operations
 */
@Injectable()
export class DocumentService {
  private readonly logger = new Logger(DocumentService.name);

  /**
   * List of allowed MIME types for document uploads
   * Restricts uploads to PDF and common image formats
   */
  private readonly ALLOWED_MIME_TYPES = [
    'application/pdf',
    'image/jpeg',
    'image/jpg',
    'image/png',
  ];

  /**
   * Get the list of allowed MIME types
   *
   * @returns The list of allowed MIME types for document uploads
   */
  getAllowedMimeTypes(): string[] {
    return [...this.ALLOWED_MIME_TYPES];
  }

  /**
   * Maximum file size for document uploads (10MB)
   */
  private readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

  constructor(
    private prisma: PrismaService,
    private mediaService: MediaService,
  ) {}

  /**
   * Upload a new document
   *
   * This method handles the document upload process:
   * 1. Validates the file type and size
   * 2. Verifies that the user exists
   * 3. Uploads the file to Supabase storage
   * 4. Creates a document record in the database
   *
   * @param userId - The ID of the user uploading the document
   * @param file - The uploaded file (PDF, JPG, PNG)
   * @param dto - Document metadata including name and expiry date
   * @returns The created document object with metadata
   * @throws BadRequestException if the file type or size is invalid
   * @throws NotFoundException if the specified user doesn't exist
   */
  async uploadDocument(
    userId: string,
    file: Express.Multer.File,
    dto: UploadDocumentDto,
  ): Promise<DocumentResponseDto> {
    this.logger.log(`Uploading document for user: ${userId}`, {
      fileName: file.originalname,
      fileSize: file.size,
      mimeType: file.mimetype,
    });

    // Validate file type
    if (!this.ALLOWED_MIME_TYPES.includes(file.mimetype)) {
      throw new BadRequestException(
        `Invalid file type. Allowed types: ${this.ALLOWED_MIME_TYPES.join(', ')}`,
      );
    }

    // Validate file size
    if (file.size > this.MAX_FILE_SIZE) {
      throw new BadRequestException(
        `File size exceeds the limit of ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`,
      );
    }

    // Verify that the user exists
    const userExists = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { id: true },
    });

    if (!userExists) {
      throw new NotFoundException(
        `User with ID ${userId} not found. Please provide a valid user ID.`,
      );
    }

    // Validate related entities if provided
    if (dto.serviceTypeId) {
      const serviceTypeExists = await this.prisma.service_type.findUnique({
        where: { id: dto.serviceTypeId },
        select: { id: true },
      });
      if (!serviceTypeExists) {
        throw new NotFoundException(
          `Service type with ID ${dto.serviceTypeId} not found.`,
        );
      }
    }

    if (dto.applicationId) {
      const applicationExists = await this.prisma.application.findUnique({
        where: { id: dto.applicationId },
        select: { id: true, userId: true },
      });
      if (!applicationExists) {
        throw new NotFoundException(
          `Application with ID ${dto.applicationId} not found.`,
        );
      }
      // Ensure the application belongs to the user
      if (applicationExists.userId !== userId) {
        throw new BadRequestException(
          `Application ${dto.applicationId} does not belong to user ${userId}.`,
        );
      }
    }

    if (dto.documentRequirementId) {
      const documentRequirementExists =
        await this.prisma.document_requirement.findUnique({
          where: { id: dto.documentRequirementId },
          select: { id: true },
        });
      if (!documentRequirementExists) {
        throw new NotFoundException(
          `Document requirement with ID ${dto.documentRequirementId} not found.`,
        );
      }
    }

    // Upload file to Supabase
    const uploadResult = await this.mediaService.uploadFile(
      file,
      'immigration-documents',
    );

    // Create document record in database
    const document = await this.prisma.user_document.create({
      data: {
        documentName: dto.documentName,
        fileUrl: uploadResult.url,
        verificationStatus: VerificationStatus.PENDING,
        expiryDate: dto.expiryDate ? new Date(dto.expiryDate) : null,
        category: dto.category,
        notes: dto.notes,
        userId,
        serviceTypeId: dto.serviceTypeId,
        applicationId: dto.applicationId,
        documentRequirementId: dto.documentRequirementId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        serviceType: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
        application: {
          select: {
            id: true,
            status: true,
            submissionDate: true,
          },
        },
        documentRequirement: {
          select: {
            id: true,
            name: true,
            required: true,
          },
        },
      },
    });

    this.logger.log(`Document uploaded successfully: ${document.id}`);

    return this.mapToResponseDto(document);
  }

  /**
   * Get a document by its ID
   *
   * This method retrieves a document by its ID and checks if the user has access to it.
   * Regular users can only access their own documents, while admins can access any document.
   *
   * @param userId - The ID of the user requesting the document
   * @param documentId - The ID of the document to retrieve
   * @param isAdmin - Whether the user is an admin (defaults to false)
   * @returns The document object if the user has access
   * @throws NotFoundException if the document doesn't exist
   * @throws ForbiddenException if the user doesn't have access to the document
   */
  async getDocumentById(userId: string, documentId: string, isAdmin = false) {
    const document = await this.prisma.user_document.findUnique({
      where: { id: documentId },
    });

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    // Check if user has access to this document
    if (!isAdmin && document.userId !== userId) {
      throw new ForbiddenException('You do not have access to this document');
    }

    return document;
  }

  /**
   * Get documents for a specific user
   *
   * This method retrieves all documents for a specific user with optional filtering.
   * The response includes different information based on the role of the requester.
   *
   * @param userId - The ID of the user whose documents to retrieve
   * @param filters - Optional filters for verification status and document name
   * @param isAdmin - Whether the requester is an admin (defaults to false)
   * @returns A list of documents for the specified user
   */
  async getUserDocuments(
    userId: string,
    filters?: DocumentFilterDto,
    isAdmin = false,
  ) {
    const where = {
      userId,
      ...(filters?.verificationStatus && {
        verificationStatus: filters.verificationStatus,
      }),
      ...(filters?.documentName && {
        documentName: {
          contains: filters.documentName,
          mode: 'insensitive' as any,
        },
      }),
    };

    const documents = await this.prisma.user_document.findMany({
      where,
      orderBy: { uploadDate: 'desc' },
      include: {
        // Include user information if the requester is an admin
        user: isAdmin
          ? {
              select: {
                id: true,
                name: true,
                email: true,
              },
            }
          : undefined,
      },
    });

    return documents;
  }

  /**
   * Get all documents in the system with advanced filtering and pagination
   *
   * This method retrieves all documents in the system with comprehensive filtering options.
   * Admins have access to all document information, while agents have limited access.
   *
   * @param filters - Advanced filters including pagination, date ranges, expiry filters
   * @param isAdmin - Whether the requester is an admin (defaults to false)
   * @returns A paginated list of all documents in the system
   */
  async getAllDocuments(
    filters?: DocumentFilterDto,
    isAdmin = false,
  ): Promise<PaginatedDocumentResponseDto> {
    const {
      verificationStatus,
      documentName,
      category,
      serviceTypeId,
      applicationId,
      documentRequirementId,
      expiryDateFrom,
      expiryDateTo,
      uploadDateFrom,
      uploadDateTo,
      expiringSoonDays,
      expiredOnly,
      page = 1,
      limit = 10,
      sortBy = 'uploadDate',
      sortOrder = 'desc',
    } = filters || {};

    this.logger.log(
      `Getting all documents with filters: ${JSON.stringify(filters)}`,
    );

    // Build where clause
    const where: any = {};

    if (verificationStatus) {
      where.verificationStatus = verificationStatus;
    }

    if (documentName) {
      where.documentName = {
        contains: documentName,
        mode: 'insensitive',
      };
    }

    if (category) {
      where.category = category;
    }

    if (serviceTypeId) {
      where.serviceTypeId = serviceTypeId;
    }

    if (applicationId) {
      where.applicationId = applicationId;
    }

    if (documentRequirementId) {
      where.documentRequirementId = documentRequirementId;
    }

    // Date range filters
    if (expiryDateFrom || expiryDateTo) {
      where.expiryDate = {};
      if (expiryDateFrom) {
        where.expiryDate.gte = new Date(expiryDateFrom);
      }
      if (expiryDateTo) {
        where.expiryDate.lte = new Date(expiryDateTo);
      }
    }

    if (uploadDateFrom || uploadDateTo) {
      where.uploadDate = {};
      if (uploadDateFrom) {
        where.uploadDate.gte = new Date(uploadDateFrom);
      }
      if (uploadDateTo) {
        where.uploadDate.lte = new Date(uploadDateTo);
      }
    }

    // Expiry-based filters
    if (expiredOnly) {
      where.expiryDate = {
        ...where.expiryDate,
        lt: new Date(),
      };
    }

    if (expiringSoonDays) {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + expiringSoonDays);
      where.expiryDate = {
        ...where.expiryDate,
        gte: new Date(),
        lte: futureDate,
      };
    }

    // Pagination
    const skip = (page - 1) * limit;
    const take = Math.min(limit, 100); // Max 100 items per page

    // Sort order
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    try {
      const [documents, total] = await Promise.all([
        this.prisma.user_document.findMany({
          where,
          skip,
          take,
          orderBy,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            serviceType: {
              select: {
                id: true,
                name: true,
                description: true,
              },
            },
            application: {
              select: {
                id: true,
                status: true,
                submissionDate: true,
              },
            },
            documentRequirement: {
              select: {
                id: true,
                name: true,
                required: true,
              },
            },
          },
        }),
        this.prisma.user_document.count({ where }),
      ]);

      const totalPages = Math.ceil(total / take);

      this.logger.log(
        `Found ${documents.length} documents out of ${total} total`,
      );

      // Map documents to response DTOs
      const mappedDocuments = documents.map(this.mapToResponseDto);

      // If the requester is not an admin, limit the information returned
      if (!isAdmin) {
        // Remove sensitive information for agents
        const agentDocuments = mappedDocuments.map((doc) => ({
          ...doc,
          user: {
            id: doc.user?.id,
            name: doc.user?.name,
            // Remove email for agents
          },
          agentAccessLevel: 'read-only',
        }));

        return {
          data: agentDocuments,
          total,
          page,
          limit: take,
          totalPages,
        };
      }

      return {
        data: mappedDocuments,
        total,
        page,
        limit: take,
        totalPages,
      };
    } catch (error) {
      this.logger.error(
        `Failed to fetch documents: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        'Failed to fetch documents: ' + error.message,
      );
    }
  }

  /**
   * Verify a document (approve or reject)
   *
   * This method handles the document verification process.
   * Admins can perform any verification action, while agents have restrictions:
   * - Agents cannot approve documents directly (only set to PENDING or REJECTED)
   * - Rejection requires a reason to be provided
   *
   * @param documentId - The ID of the document to verify
   * @param dto - The verification data (status and optional rejection reason)
   * @param isAdmin - Whether the requester is an admin (defaults to false)
   * @returns The updated document with the new verification status
   * @throws NotFoundException if the document doesn't exist
   * @throws BadRequestException if rejecting without a reason
   * @throws ForbiddenException if an agent tries to approve a document
   */
  async verifyDocument(
    documentId: string,
    dto: VerifyDocumentDto,
    isAdmin = false,
  ) {
    // Check if document exists
    const document = await this.prisma.user_document.findUnique({
      where: { id: documentId },
    });

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    // If rejecting, require a reason
    if (
      dto.verificationStatus === VerificationStatus.REJECTED &&
      !dto.rejectionReason
    ) {
      throw new BadRequestException(
        'Rejection reason is required when rejecting a document',
      );
    }

    // Add restrictions for agents if needed
    if (!isAdmin) {
      // For example, agents might only be allowed to set documents to PENDING or REJECTED
      // but not directly to APPROVED (which might require admin approval)
      if (dto.verificationStatus === VerificationStatus.APPROVED) {
        throw new ForbiddenException(
          'Only administrators can approve documents directly',
        );
      }
    }

    // Update document verification status
    const updatedDocument = await this.prisma.user_document.update({
      where: { id: documentId },
      data: {
        verificationStatus: dto.verificationStatus,
        rejectionReason:
          dto.verificationStatus === VerificationStatus.REJECTED
            ? dto.rejectionReason
            : null,
      },
    });

    return updatedDocument;
  }

  /**
   * Delete a document
   *
   * This method handles document deletion with role-based access control:
   * - Regular users can only delete their own documents
   * - Admins can delete any document
   * - Agents can delete documents except approved ones
   *
   * @param userId - The ID of the user requesting the deletion
   * @param documentId - The ID of the document to delete
   * @param hasAdminAccess - Whether the requester has admin-level access (defaults to false)
   * @param isAdmin - Whether the requester is an admin (defaults to false)
   * @returns A success message if the document was deleted
   * @throws NotFoundException if the document doesn't exist
   * @throws ForbiddenException if the user doesn't have access to delete the document
   */
  async deleteDocument(
    userId: string,
    documentId: string,
    hasAdminAccess = false,
    isAdmin = false,
  ) {
    // Check if document exists
    const document = await this.prisma.user_document.findUnique({
      where: { id: documentId },
    });

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    // Check if user has access to delete this document
    if (!hasAdminAccess && document.userId !== userId) {
      throw new ForbiddenException('You do not have access to this document');
    }

    // Add restrictions for agents if needed
    if (hasAdminAccess && !isAdmin) {
      // For example, agents might only be allowed to delete documents in certain states
      if (document.verificationStatus === VerificationStatus.APPROVED) {
        throw new ForbiddenException(
          'Agents cannot delete approved documents. Please contact an administrator.',
        );
      }
    }

    // Delete document from database
    await this.prisma.user_document.delete({
      where: { id: documentId },
    });

    // Note: We're not deleting the file from Supabase storage here
    // In a production environment, you might want to implement this
    // or use a scheduled job to clean up orphaned files

    return { message: 'Document deleted successfully' };
  }

  /**
   * Bulk verify documents
   * @param dto Bulk verification data
   * @param isAdmin Whether the requester is an admin
   * @returns Results of bulk verification
   */
  async bulkVerifyDocuments(dto: BulkVerifyDocumentDto, isAdmin = false) {
    this.logger.log(`Bulk verifying ${dto.documentIds.length} documents`, {
      status: dto.verificationStatus,
      isAdmin,
    });

    // Check if documents exist
    const documents = await this.prisma.user_document.findMany({
      where: {
        id: { in: dto.documentIds },
      },
      select: { id: true, verificationStatus: true },
    });

    if (documents.length !== dto.documentIds.length) {
      const foundIds = documents.map((d) => d.id);
      const missingIds = dto.documentIds.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(
        `Documents not found: ${missingIds.join(', ')}`,
      );
    }

    // Validate rejection reason if rejecting
    if (
      dto.verificationStatus === VerificationStatus.REJECTED &&
      !dto.rejectionReason
    ) {
      throw new BadRequestException(
        'Rejection reason is required when rejecting documents',
      );
    }

    // Add restrictions for agents
    if (!isAdmin && dto.verificationStatus === VerificationStatus.APPROVED) {
      throw new ForbiddenException(
        'Only administrators can approve documents directly',
      );
    }

    // Perform bulk update
    const updateResult = await this.prisma.user_document.updateMany({
      where: {
        id: { in: dto.documentIds },
      },
      data: {
        verificationStatus: dto.verificationStatus,
        rejectionReason:
          dto.verificationStatus === VerificationStatus.REJECTED
            ? dto.rejectionReason
            : null,
      },
    });

    this.logger.log(
      `Bulk verification completed: ${updateResult.count} documents updated`,
    );

    return {
      message: `Successfully updated ${updateResult.count} documents`,
      updatedCount: updateResult.count,
      status: dto.verificationStatus,
    };
  }

  /**
   * Bulk delete documents
   * @param dto Bulk operation data
   * @param userId User ID for ownership validation
   * @param hasAdminAccess Whether the user has admin access
   * @param isAdmin Whether the user is an admin
   * @returns Results of bulk deletion
   */
  async bulkDeleteDocuments(
    dto: BulkDocumentOperationDto,
    userId: string,
    hasAdminAccess = false,
    isAdmin = false,
  ) {
    this.logger.log(`Bulk deleting ${dto.documentIds.length} documents`, {
      userId,
      hasAdminAccess,
      isAdmin,
    });

    // Check if documents exist and validate access
    const documents = await this.prisma.user_document.findMany({
      where: {
        id: { in: dto.documentIds },
      },
      select: { id: true, userId: true, verificationStatus: true },
    });

    if (documents.length !== dto.documentIds.length) {
      const foundIds = documents.map((d) => d.id);
      const missingIds = dto.documentIds.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(
        `Documents not found: ${missingIds.join(', ')}`,
      );
    }

    // Validate access for each document
    if (!hasAdminAccess) {
      const unauthorizedDocs = documents.filter((doc) => doc.userId !== userId);
      if (unauthorizedDocs.length > 0) {
        throw new ForbiddenException(
          `You do not have access to documents: ${unauthorizedDocs.map((d) => d.id).join(', ')}`,
        );
      }
    }

    // Add restrictions for agents
    if (hasAdminAccess && !isAdmin) {
      const approvedDocs = documents.filter(
        (doc) => doc.verificationStatus === VerificationStatus.APPROVED,
      );
      if (approvedDocs.length > 0) {
        throw new ForbiddenException(
          `Agents cannot delete approved documents: ${approvedDocs.map((d) => d.id).join(', ')}`,
        );
      }
    }

    // Perform bulk deletion
    const deleteResult = await this.prisma.user_document.deleteMany({
      where: {
        id: { in: dto.documentIds },
      },
    });

    this.logger.log(
      `Bulk deletion completed: ${deleteResult.count} documents deleted`,
    );

    return {
      message: `Successfully deleted ${deleteResult.count} documents`,
      deletedCount: deleteResult.count,
    };
  }

  /**
   * Get documents expiring soon
   * @param days Number of days to look ahead
   * @param isAdmin Whether the requester is an admin
   * @returns Documents expiring within the specified days
   */
  async getDocumentsExpiringSoon(
    days: number = 30,
    isAdmin = false,
  ): Promise<PaginatedDocumentResponseDto> {
    this.logger.log(`Getting documents expiring within ${days} days`);

    const now = new Date();
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);

    const where = {
      expiryDate: {
        gte: now,
        lte: futureDate,
      },
    };

    try {
      const [documents, total] = await Promise.all([
        this.prisma.user_document.findMany({
          where,
          orderBy: { expiryDate: 'asc' },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            serviceType: {
              select: {
                id: true,
                name: true,
                description: true,
              },
            },
            application: {
              select: {
                id: true,
                status: true,
                submissionDate: true,
              },
            },
            documentRequirement: {
              select: {
                id: true,
                name: true,
                required: true,
              },
            },
          },
        }),
        this.prisma.user_document.count({ where }),
      ]);

      this.logger.log(
        `Found ${documents.length} documents expiring within ${days} days`,
      );

      const mappedDocuments = documents.map(this.mapToResponseDto);

      // If the requester is not an admin, limit the information returned
      if (!isAdmin) {
        const agentDocuments = mappedDocuments.map((doc) => ({
          ...doc,
          user: {
            id: doc.user?.id,
            name: doc.user?.name,
          },
        }));

        return {
          data: agentDocuments,
          total,
          page: 1,
          limit: total,
          totalPages: 1,
        };
      }

      return {
        data: mappedDocuments,
        total,
        page: 1,
        limit: total,
        totalPages: 1,
      };
    } catch (error) {
      this.logger.error(
        `Failed to fetch expiring documents: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        'Failed to fetch expiring documents: ' + error.message,
      );
    }
  }

  /**
   * Map document to response DTO with expiry calculations
   * @param document Document from database with includes
   * @returns Mapped response DTO
   */
  private mapToResponseDto = (document: any): DocumentResponseDto => {
    const now = new Date();
    const expiryDate = document.expiryDate
      ? new Date(document.expiryDate)
      : null;

    let isExpired = false;
    let daysUntilExpiry: number | undefined;

    if (expiryDate) {
      isExpired = expiryDate < now;
      daysUntilExpiry = Math.ceil(
        (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),
      );
    }

    return {
      id: document.id,
      documentName: document.documentName,
      fileUrl: document.fileUrl,
      uploadDate: document.uploadDate,
      verificationStatus: document.verificationStatus,
      rejectionReason: document.rejectionReason,
      expiryDate: document.expiryDate,
      category: document.category,
      serviceTypeId: document.serviceTypeId,
      applicationId: document.applicationId,
      documentRequirementId: document.documentRequirementId,
      notes: document.notes,
      createdAt: document.createdAt,
      updatedAt: document.updatedAt,
      user: document.user,
      serviceType: document.serviceType,
      application: document.application,
      documentRequirement: document.documentRequirement,
      isExpired,
      daysUntilExpiry,
    };
  };
}
