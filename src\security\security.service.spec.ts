import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../utils/prisma.service';
// Mock the MailerService to avoid template import issues
const mockMailerService = {
  sendEmail: jest.fn(),
};
import { EncryptionService } from './encryption.service';
import { AuditService } from './audit.service';
import {
  SecurityEventType,
  SecuritySeverity,
  ConsentType,
} from '@prisma/client';

describe('Security Services', () => {
  let encryptionService: EncryptionService;
  let auditService: AuditService;
  let prismaService: PrismaService;

  const mockPrismaService = {
    encrypted_field: {
      upsert: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      deleteMany: jest.fn(),
      update: jest.fn(),
    },
    audit_log: {
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      groupBy: jest.fn(),
    },
    security_event: {
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      update: jest.fn(),
      groupBy: jest.fn(),
    },
    failed_auth_attempt: {
      create: jest.fn(),
      count: jest.fn(),
    },
    gdpr_consent: {
      upsert: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      count: jest.fn(),
      groupBy: jest.fn(),
    },
    data_export_request: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
    data_deletion_request: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
      delete: jest.fn(),
    },
    user_document: {
      findMany: jest.fn(),
      deleteMany: jest.fn(),
    },
    application: {
      findMany: jest.fn(),
    },
    notification: {
      findMany: jest.fn(),
    },
    reminder: {
      findMany: jest.fn(),
    },
  };

  const mockConfigService = {
    get: jest.fn().mockReturnValue('test-encryption-key'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EncryptionService,
        AuditService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: 'MailerService',
          useValue: mockMailerService,
        },
      ],
    }).compile();

    encryptionService = module.get<EncryptionService>(EncryptionService);
    auditService = module.get<AuditService>(AuditService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('EncryptionService', () => {
    it('should be defined', () => {
      expect(encryptionService).toBeDefined();
    });

    it('should encrypt and store field data', async () => {
      mockPrismaService.encrypted_field.upsert.mockResolvedValue({
        id: 'test-id',
        entityType: 'user',
        entityId: 'user-123',
        fieldName: 'ssn',
        encryptedValue: 'encrypted-data',
        keyVersion: '1',
        algorithm: 'AES-256-GCM',
      });

      await encryptionService.encryptField(
        'user',
        'user-123',
        'ssn',
        '***********',
      );

      expect(mockPrismaService.encrypted_field.upsert).toHaveBeenCalledWith({
        where: {
          entityType_entityId_fieldName: {
            entityType: 'user',
            entityId: 'user-123',
            fieldName: 'ssn',
          },
        },
        update: expect.objectContaining({
          keyVersion: '1',
          algorithm: 'AES-256-GCM',
        }),
        create: expect.objectContaining({
          entityType: 'user',
          entityId: 'user-123',
          fieldName: 'ssn',
          keyVersion: '1',
          algorithm: 'AES-256-GCM',
        }),
      });
    });

    it('should decrypt field data', async () => {
      const encryptedValue = 'U2FsdGVkX1+test+encrypted+data';
      mockPrismaService.encrypted_field.findUnique.mockResolvedValue({
        id: 'test-id',
        encryptedValue,
      });

      // Mock the decryption to return a test value
      jest
        .spyOn(encryptionService as any, 'decrypt')
        .mockReturnValue('***********');

      const result = await encryptionService.decryptField(
        'user',
        'user-123',
        'ssn',
      );

      expect(result).toBe('***********');
      expect(mockPrismaService.encrypted_field.findUnique).toHaveBeenCalledWith(
        {
          where: {
            entityType_entityId_fieldName: {
              entityType: 'user',
              entityId: 'user-123',
              fieldName: 'ssn',
            },
          },
        },
      );
    });

    it('should check if field is encrypted', async () => {
      mockPrismaService.encrypted_field.findUnique.mockResolvedValue({
        id: 'test-id',
      });

      const result = await encryptionService.isFieldEncrypted(
        'user',
        'user-123',
        'ssn',
      );

      expect(result).toBe(true);
    });
  });

  describe('AuditService', () => {
    it('should be defined', () => {
      expect(auditService).toBeDefined();
    });

    it('should log user actions', async () => {
      mockPrismaService.audit_log.create.mockResolvedValue({
        id: 'audit-123',
        action: 'CREATE',
        entityType: 'user',
        success: true,
      });

      await auditService.logAction({
        action: 'CREATE',
        entityType: 'user',
        entityId: 'user-123',
        userId: 'admin-123',
        ipAddress: '***********',
        success: true,
      });

      expect(mockPrismaService.audit_log.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          action: 'CREATE',
          entityType: 'user',
          entityId: 'user-123',
          userId: 'admin-123',
          ipAddress: '***********',
          success: true,
        }),
      });
    });

    it('should log authentication events', async () => {
      mockPrismaService.audit_log.create.mockResolvedValue({});

      await auditService.logAuthentication({
        action: 'LOGIN',
        userId: 'user-123',
        ipAddress: '***********',
        success: true,
      });

      expect(mockPrismaService.audit_log.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          action: 'LOGIN',
          entityType: 'authentication',
          userId: 'user-123',
          success: true,
        }),
      });
    });

    it('should get audit logs with pagination', async () => {
      const mockLogs = [
        { id: 'log-1', action: 'CREATE', entityType: 'user' },
        { id: 'log-2', action: 'UPDATE', entityType: 'user' },
      ];

      mockPrismaService.audit_log.findMany.mockResolvedValue(mockLogs);
      mockPrismaService.audit_log.count.mockResolvedValue(2);

      const result = await auditService.getAuditLogs({
        page: 1,
        limit: 10,
        userId: 'user-123',
      });

      expect(result).toEqual({
        data: mockLogs,
        total: 2,
        page: 1,
        limit: 10,
        totalPages: 1,
      });
    });
  });
});
