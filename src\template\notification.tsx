import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';

import * as React from 'react';

interface NotificationEmailProps {
  userName?: string;
  message: string;
  notificationType: 'INFO' | 'WARNING' | 'SUCCESS' | 'ERROR';
  actionUrl?: string;
  actionText?: string;
}

export default function NotificationEmail({
  userName = '',
  message = 'You have a new notification',
  notificationType = 'INFO',
  actionUrl,
  actionText = 'View Details',
}: NotificationEmailProps) {
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'SUCCESS':
        return '#10b981';
      case 'WARNING':
        return '#f59e0b';
      case 'ERROR':
        return '#ef4444';
      default:
        return '#3b82f6';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'SUCCESS':
        return '✅';
      case 'WARNING':
        return '⚠️';
      case 'ERROR':
        return '❌';
      default:
        return 'ℹ️';
    }
  };

  return (
    <Html>
      <Head />
      <Preview>{message}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Container style={logoSection}>
            <Link href={process.env.WEBSITE} style={logoLink}>
              <Img
                src="https://supabase.careerireland.com/storage/v1/object/public/careerireland-prod/logo/logo-careerireland.webp"
                width="80"
                height="80"
                alt="Logo"
                style={logoImage}
              />
            </Link>
          </Container>

          <Section style={{
            ...typeIndicator,
            backgroundColor: getTypeColor(notificationType) + '20',
            borderLeft: `4px solid ${getTypeColor(notificationType)}`,
          }}>
            <Text style={{
              ...typeText,
              color: getTypeColor(notificationType),
            }}>
              {getTypeIcon(notificationType)} {notificationType}
            </Text>
          </Section>

          <Heading style={h1}>New Notification</Heading>

          <Text style={text}>{userName ? `Hi ${userName},` : 'Hello,'}</Text>

          <Section style={messageContainer}>
            <Text style={messageText}>{message}</Text>
          </Section>

          {actionUrl && (
            <Section style={buttonContainer}>
              <Button 
                style={{
                  ...button,
                  backgroundColor: getTypeColor(notificationType),
                }} 
                href={actionUrl}
              >
                {actionText}
              </Button>
            </Section>
          )}

          <Text style={text}>
            You can manage your notification preferences in your account settings.
          </Text>

          <Text style={text}>
            Thanks,
            <br />
            The Career Ireland Team
          </Text>
        </Container>
      </Body>
    </Html>
  );
}

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  padding: '0 48px',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '24px auto',
  padding: '48px 24px',
  borderRadius: '8px',
  maxWidth: '500px',
};

const logoLink = {
  display: 'inline-block',
  textDecoration: 'none',
};

const logoSection = {
  textAlign: 'center' as const,
  marginBottom: '32px',
  display: 'flex',
  flexDirection: 'column' as const,
  alignItems: 'center',
  maxWidth: '600px',
};

const logoImage = {
  margin: '0 auto',
  borderRadius: '12px',
  padding: '12px',
  backgroundColor: '#f8fafc',
  objectFit: 'contain' as const,
  transition: 'all 0.2s ease',
};

const h1 = {
  color: '#1f2937',
  fontSize: '24px',
  fontWeight: '600',
  lineHeight: '1.4',
  margin: '0 0 24px',
  textAlign: 'center' as const,
};

const text = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '24px 0',
};

const typeIndicator = {
  padding: '16px 20px',
  borderRadius: '8px',
  margin: '24px 0',
};

const typeText = {
  fontSize: '14px',
  fontWeight: '600',
  margin: '0',
  textTransform: 'uppercase' as const,
  letterSpacing: '0.05em',
};

const messageContainer = {
  background: '#f8fafc',
  borderRadius: '8px',
  padding: '24px',
  margin: '24px 0',
  border: '1px solid #e5e7eb',
};

const messageText = {
  color: '#1f2937',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  borderRadius: '6px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
  margin: '0 auto',
};
