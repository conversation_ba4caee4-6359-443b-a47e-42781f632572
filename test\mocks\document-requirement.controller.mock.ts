import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
} from '@nestjs/common';
import { DocumentRequirementService } from '../../src/immigration/document-requirement.service';
import {
  CreateDocumentRequirementDto,
  UpdateDocumentRequirementDto,
} from '../../src/immigration/dto/document-requirement.dto';

/**
 * Mock implementation of DocumentRequirementController for testing
 * This controller doesn't use guards for testing purposes
 */
@Controller('admin/document-requirements')
export class DocumentRequirementController {
  constructor(
    private readonly documentRequirementService: DocumentRequirementService,
  ) {}

  @Post()
  async create(@Body() createDto: CreateDocumentRequirementDto) {
    return this.documentRequirementService.create(createDto);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.documentRequirementService.findOne(id);
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateDocumentRequirementDto,
  ) {
    return this.documentRequirementService.update(id, updateDto);
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    return this.documentRequirementService.remove(id);
  }
}
