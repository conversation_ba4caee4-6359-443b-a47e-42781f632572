import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { IJWTPayload } from 'src/types';

/**
 * Decorator to extract the user from the request
 * Returns the user object from the JWT token
 */
export const GetUser = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): IJWTPayload => {
    const request = ctx.switchToHttp().getRequest();
    return request.user;
  },
);
