import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { ServiceTypeService } from './service-type.service';
import {
  CreateServiceTypeDto,
  UpdateServiceTypeDto,
  FilterServiceTypeDto,
  ServiceTypeResponseDto,
} from './dto/service-type.dto';
import { JwtAdmin } from '../guards/jwt.admin.guard';
import { JwtGuard } from '../guards/jwt.guard';

@ApiTags('Service Types')
@Controller('service-types')
export class ServiceTypeController {
  private readonly logger = new Logger(ServiceTypeController.name);

  constructor(private readonly serviceTypeService: ServiceTypeService) {}

  // ==================== PUBLIC ENDPOINTS ====================

  @Get('public')
  @ApiOperation({
    summary: 'Get all service types (Public)',
    description:
      'Retrieve all available service types for public viewing with optional filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'List of service types retrieved successfully.',
    type: [ServiceTypeResponseDto],
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search in name and description',
  })
  @ApiQuery({
    name: 'minPrice',
    required: false,
    description: 'Minimum price filter',
  })
  @ApiQuery({
    name: 'maxPrice',
    required: false,
    description: 'Maximum price filter',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page (default: 10, max: 50)',
  })
  async findAllPublic(@Query() filterDto: FilterServiceTypeDto) {
    this.logger.log(
      'Public request for service types with filters:',
      filterDto,
    );
    return this.serviceTypeService.findAll(filterDto);
  }

  @Get('public/:id')
  @ApiOperation({
    summary: 'Get service type by ID (Public)',
    description:
      'Retrieve a specific service type with its document requirements',
  })
  @ApiResponse({
    status: 200,
    description: 'Service type retrieved successfully.',
    type: ServiceTypeResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Service type not found.' })
  async findOnePublic(@Param('id') id: string) {
    this.logger.log(`Public request for service type: ${id}`);
    return this.serviceTypeService.findOneWithRequirements(id);
  }

  // ==================== ADMIN ENDPOINTS ====================

  @Post('admin')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new service type (Admin only)' })
  @ApiResponse({
    status: 201,
    description: 'The service type has been successfully created.',
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  async create(@Body() createServiceTypeDto: CreateServiceTypeDto) {
    return this.serviceTypeService.create(createServiceTypeDto);
  }

  @Get('admin')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all service types (Admin only)',
    description:
      'Retrieve all service types with admin-level details and filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'List of service types with admin details.',
    type: [ServiceTypeResponseDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search in name and description',
  })
  @ApiQuery({
    name: 'minPrice',
    required: false,
    description: 'Minimum price filter',
  })
  @ApiQuery({
    name: 'maxPrice',
    required: false,
    description: 'Maximum price filter',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page (default: 10, max: 100)',
  })
  async findAllAdmin(@Query() filterDto: FilterServiceTypeDto) {
    this.logger.log('Admin request for service types with filters:', filterDto);
    return this.serviceTypeService.findAllWithStats(filterDto);
  }

  @Get('admin/:id')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get a service type by ID (Admin only)',
    description: 'Retrieve a specific service type with admin-level details',
  })
  @ApiResponse({
    status: 200,
    description: 'The service type with admin details.',
    type: ServiceTypeResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Service type not found.' })
  async findOneAdmin(@Param('id') id: string) {
    this.logger.log(`Admin request for service type: ${id}`);
    return this.serviceTypeService.findOneWithStats(id);
  }

  @Put('admin/:id')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update a service type (Admin only)',
    description: 'Update an existing service type with new information',
  })
  @ApiResponse({
    status: 200,
    description: 'The service type has been successfully updated.',
    type: ServiceTypeResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Service type not found.' })
  async updateAdmin(
    @Param('id') id: string,
    @Body() updateServiceTypeDto: UpdateServiceTypeDto,
  ) {
    this.logger.log(`Admin updating service type: ${id}`, updateServiceTypeDto);
    return this.serviceTypeService.update(id, updateServiceTypeDto);
  }

  @Delete('admin/:id')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Delete a service type (Admin only)',
    description: 'Delete a service type if it has no associated applications',
  })
  @ApiResponse({
    status: 200,
    description: 'The service type has been successfully deleted.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Service type has associated applications.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Service type not found.' })
  async removeAdmin(@Param('id') id: string) {
    this.logger.log(`Admin deleting service type: ${id}`);
    return this.serviceTypeService.remove(id);
  }

  @Get('admin/:id/document-requirements')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get document requirements for a service type (Admin only)',
    description:
      'Retrieve all document requirements associated with a specific service type',
  })
  @ApiResponse({
    status: 200,
    description: 'List of document requirements for the service type.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Service type not found.' })
  async getDocumentRequirementsAdmin(@Param('id') id: string) {
    this.logger.log(
      `Admin requesting document requirements for service type: ${id}`,
    );
    return this.serviceTypeService.getDocumentRequirements(id);
  }
}
