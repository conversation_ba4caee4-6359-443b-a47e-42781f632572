import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../utils/prisma.service';
import { EncryptionService } from './encryption.service';
import { AuditService } from './audit.service';
import {
  ConsentType,
  DataExportType,
  ExportStatus,
  DataDeletionType,
  DeletionStatus,
} from '@prisma/client';

/**
 * GDPR Compliance Service
 *
 * Implements GDPR compliance features including:
 * - Consent management
 * - Right to Access (data export)
 * - Right to be Forgotten (data deletion)
 * - Data retention policies
 * - Privacy policy compliance
 *
 * Features:
 * - Comprehensive consent tracking
 * - Automated data export generation
 * - Secure data deletion with audit trails
 * - Data retention policy enforcement
 * - Privacy compliance reporting
 */
@Injectable()
export class GDPRService {
  private readonly logger = new Logger(GDPRService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly encryptionService: EncryptionService,
    private readonly auditService: AuditService,
  ) {}

  /**
   * Record user consent for data processing
   */
  async recordConsent(params: {
    userId: string;
    consentType: ConsentType;
    granted: boolean;
    ipAddress?: string;
    userAgent?: string;
    version?: string;
    metadata?: any;
  }): Promise<void> {
    const {
      userId,
      consentType,
      granted,
      ipAddress,
      userAgent,
      version = '1.0',
      metadata,
    } = params;

    try {
      await this.prisma.gdpr_consent.upsert({
        where: {
          userId_consentType: {
            userId,
            consentType,
          },
        },
        update: {
          granted,
          grantedAt: granted ? new Date() : null,
          revokedAt: !granted ? new Date() : null,
          ipAddress,
          userAgent,
          version,
          metadata: metadata ? JSON.parse(JSON.stringify(metadata)) : null,
        },
        create: {
          userId,
          consentType,
          granted,
          grantedAt: granted ? new Date() : null,
          revokedAt: !granted ? new Date() : null,
          ipAddress,
          userAgent,
          version,
          metadata: metadata ? JSON.parse(JSON.stringify(metadata)) : null,
        },
      });

      await this.auditService.logAction({
        action: granted ? 'CONSENT_GRANTED' : 'CONSENT_REVOKED',
        entityType: 'gdpr_consent',
        entityId: `${userId}-${consentType}`,
        userId,
        ipAddress,
        userAgent,
        newValues: { consentType, granted, version },
        metadata,
      });

      this.logger.log(
        `Consent ${granted ? 'granted' : 'revoked'} for user ${userId}, type: ${consentType}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to record consent: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException('Failed to record consent');
    }
  }

  /**
   * Get user's consent status
   */
  async getUserConsents(userId: string): Promise<
    Array<{
      consentType: ConsentType;
      granted: boolean;
      grantedAt: Date | null;
      revokedAt: Date | null;
      version: string;
    }>
  > {
    const consents = await this.prisma.gdpr_consent.findMany({
      where: { userId },
      select: {
        consentType: true,
        granted: true,
        grantedAt: true,
        revokedAt: true,
        version: true,
      },
    });

    return consents;
  }

  /**
   * Check if user has granted specific consent
   */
  async hasConsent(userId: string, consentType: ConsentType): Promise<boolean> {
    const consent = await this.prisma.gdpr_consent.findUnique({
      where: {
        userId_consentType: {
          userId,
          consentType,
        },
      },
    });

    return consent?.granted || false;
  }

  /**
   * Request data export (Right to Access)
   */
  async requestDataExport(params: {
    userId: string;
    requestType: DataExportType;
    format?: string;
    ipAddress?: string;
    userAgent?: string;
  }): Promise<string> {
    const {
      userId,
      requestType,
      format = 'JSON',
      ipAddress,
      userAgent,
    } = params;

    try {
      // Check if user exists
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Create export request
      const exportRequest = await this.prisma.data_export_request.create({
        data: {
          userId,
          requestType,
          format,
          status: ExportStatus.PENDING,
        },
      });

      await this.auditService.logAction({
        action: 'DATA_EXPORT_REQUESTED',
        entityType: 'data_export_request',
        entityId: exportRequest.id,
        userId,
        ipAddress,
        userAgent,
        newValues: { requestType, format },
      });

      // Process the export asynchronously
      this.processDataExport(exportRequest.id).catch((error) => {
        this.logger.error(
          `Failed to process data export ${exportRequest.id}: ${error.message}`,
        );
      });

      this.logger.log(
        `Data export requested for user ${userId}, type: ${requestType}`,
      );
      return exportRequest.id;
    } catch (error) {
      this.logger.error(
        `Failed to request data export: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException('Failed to request data export');
    }
  }

  /**
   * Get data export status
   */
  async getDataExportStatus(
    requestId: string,
    userId: string,
  ): Promise<{
    id: string;
    status: ExportStatus;
    requestedAt: Date;
    processedAt: Date | null;
    completedAt: Date | null;
    downloadUrl: string | null;
    expiresAt: Date | null;
    fileSize: number | null;
  }> {
    const exportRequest = await this.prisma.data_export_request.findFirst({
      where: {
        id: requestId,
        userId,
      },
    });

    if (!exportRequest) {
      throw new NotFoundException('Export request not found');
    }

    return {
      id: exportRequest.id,
      status: exportRequest.status,
      requestedAt: exportRequest.requestedAt,
      processedAt: exportRequest.processedAt,
      completedAt: exportRequest.completedAt,
      downloadUrl: exportRequest.downloadUrl,
      expiresAt: exportRequest.expiresAt,
      fileSize: exportRequest.fileSize,
    };
  }

  /**
   * Request data deletion (Right to be Forgotten)
   */
  async requestDataDeletion(params: {
    userId: string;
    requestType: DataDeletionType;
    reason?: string;
    ipAddress?: string;
    userAgent?: string;
  }): Promise<string> {
    const { userId, requestType, reason, ipAddress, userAgent } = params;

    try {
      // Check if user exists
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Create deletion request
      const deletionRequest = await this.prisma.data_deletion_request.create({
        data: {
          userId,
          requestType,
          reason,
          status: DeletionStatus.PENDING,
        },
      });

      await this.auditService.logAction({
        action: 'DATA_DELETION_REQUESTED',
        entityType: 'data_deletion_request',
        entityId: deletionRequest.id,
        userId,
        ipAddress,
        userAgent,
        newValues: { requestType, reason },
      });

      // Process the deletion asynchronously
      this.processDataDeletion(deletionRequest.id).catch((error) => {
        this.logger.error(
          `Failed to process data deletion ${deletionRequest.id}: ${error.message}`,
        );
      });

      this.logger.log(
        `Data deletion requested for user ${userId}, type: ${requestType}`,
      );
      return deletionRequest.id;
    } catch (error) {
      this.logger.error(
        `Failed to request data deletion: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException('Failed to request data deletion');
    }
  }

  /**
   * Get data deletion status
   */
  async getDataDeletionStatus(
    requestId: string,
    userId: string,
  ): Promise<{
    id: string;
    status: DeletionStatus;
    requestedAt: Date;
    processedAt: Date | null;
    completedAt: Date | null;
    deletedData: any;
    retainedData: any;
  }> {
    const deletionRequest = await this.prisma.data_deletion_request.findFirst({
      where: {
        id: requestId,
        userId,
      },
    });

    if (!deletionRequest) {
      throw new NotFoundException('Deletion request not found');
    }

    return {
      id: deletionRequest.id,
      status: deletionRequest.status,
      requestedAt: deletionRequest.requestedAt,
      processedAt: deletionRequest.processedAt,
      completedAt: deletionRequest.completedAt,
      deletedData: deletionRequest.deletedData,
      retainedData: deletionRequest.retainedData,
    };
  }

  /**
   * Process data export request
   */
  private async processDataExport(requestId: string): Promise<void> {
    try {
      // Update status to processing
      await this.prisma.data_export_request.update({
        where: { id: requestId },
        data: {
          status: ExportStatus.PROCESSING,
          processedAt: new Date(),
        },
      });

      const exportRequest = await this.prisma.data_export_request.findUnique({
        where: { id: requestId },
        include: { user: true },
      });

      if (!exportRequest) {
        throw new Error('Export request not found');
      }

      // Collect user data based on request type
      const userData = await this.collectUserData(
        exportRequest.userId,
        exportRequest.requestType,
      );

      // Decrypt sensitive fields
      const decryptedData = await this.decryptUserData(userData);

      // Generate export file (in a real implementation, you would upload to secure storage)
      const exportData = {
        exportId: requestId,
        userId: exportRequest.userId,
        requestType: exportRequest.requestType,
        exportedAt: new Date(),
        data: decryptedData,
      };

      const exportJson = JSON.stringify(exportData, null, 2);
      const fileSize = Buffer.byteLength(exportJson, 'utf8');

      // In a real implementation, upload to secure storage and get download URL
      const downloadUrl = `https://secure-storage.example.com/exports/${requestId}.json`;
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

      // Update export request with completion details
      await this.prisma.data_export_request.update({
        where: { id: requestId },
        data: {
          status: ExportStatus.COMPLETED,
          completedAt: new Date(),
          downloadUrl,
          expiresAt,
          fileSize,
        },
      });

      this.logger.log(`Data export completed for request ${requestId}`);
    } catch (error) {
      this.logger.error(
        `Data export failed for request ${requestId}: ${error.message}`,
      );

      await this.prisma.data_export_request.update({
        where: { id: requestId },
        data: {
          status: ExportStatus.FAILED,
          completedAt: new Date(),
        },
      });
    }
  }

  /**
   * Process data deletion request
   */
  private async processDataDeletion(requestId: string): Promise<void> {
    try {
      // Update status to processing
      await this.prisma.data_deletion_request.update({
        where: { id: requestId },
        data: {
          status: DeletionStatus.PROCESSING,
          processedAt: new Date(),
        },
      });

      const deletionRequest =
        await this.prisma.data_deletion_request.findUnique({
          where: { id: requestId },
          include: { user: true },
        });

      if (!deletionRequest) {
        throw new Error('Deletion request not found');
      }

      // Perform data deletion based on request type
      const deletionResult = await this.performDataDeletion(
        deletionRequest.userId,
        deletionRequest.requestType,
      );

      // Update deletion request with completion details
      await this.prisma.data_deletion_request.update({
        where: { id: requestId },
        data: {
          status: DeletionStatus.COMPLETED,
          completedAt: new Date(),
          deletedData: deletionResult.deletedData,
          retainedData: deletionResult.retainedData,
        },
      });

      this.logger.log(`Data deletion completed for request ${requestId}`);
    } catch (error) {
      this.logger.error(
        `Data deletion failed for request ${requestId}: ${error.message}`,
      );

      await this.prisma.data_deletion_request.update({
        where: { id: requestId },
        data: {
          status: DeletionStatus.FAILED,
          completedAt: new Date(),
        },
      });
    }
  }

  /**
   * Collect user data for export
   */
  private async collectUserData(
    userId: string,
    requestType: DataExportType,
  ): Promise<any> {
    const userData: any = {};

    // Always include basic user information
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        createdAt: true,
        updatedAt: true,
      },
    });
    userData.user = user;

    if (
      requestType === DataExportType.FULL ||
      requestType === DataExportType.DOCUMENTS_ONLY
    ) {
      userData.documents = await this.prisma.user_document.findMany({
        where: { userId },
      });
    }

    if (
      requestType === DataExportType.FULL ||
      requestType === DataExportType.APPLICATIONS_ONLY
    ) {
      userData.applications = await this.prisma.application.findMany({
        where: { userId },
        include: {
          workflow: true,
          checkpointCalls: true,
          applicationQuery: true,
        },
      });
    }

    if (
      requestType === DataExportType.FULL ||
      requestType === DataExportType.COMMUNICATIONS_ONLY
    ) {
      userData.notifications = await this.prisma.notification.findMany({
        where: { userId },
      });
      userData.reminders = await this.prisma.reminder.findMany({
        where: { userId },
      });
    }

    if (requestType === DataExportType.FULL) {
      userData.auditLogs = await this.prisma.audit_log.findMany({
        where: { userId },
      });
      userData.consents = await this.prisma.gdpr_consent.findMany({
        where: { userId },
      });
    }

    return userData;
  }

  /**
   * Decrypt sensitive user data for export
   */
  private async decryptUserData(userData: any): Promise<any> {
    // In a real implementation, you would decrypt sensitive fields
    // For now, we'll just return the data as-is
    return userData;
  }

  /**
   * Perform data deletion based on request type
   */
  private async performDataDeletion(
    userId: string,
    requestType: DataDeletionType,
  ): Promise<{ deletedData: any; retainedData: any }> {
    const deletedData: any = {};
    const retainedData: any = {};

    // Note: In a real implementation, you need to be careful about data retention requirements
    // Some data may need to be retained for legal/regulatory reasons

    if (requestType === DataDeletionType.FULL) {
      // Delete user account and all associated data
      // This is a simplified implementation
      deletedData.user = await this.prisma.user.delete({
        where: { id: userId },
      });
    } else {
      // Selective deletion based on request type
      if (requestType === DataDeletionType.DOCUMENTS_ONLY) {
        deletedData.documents = await this.prisma.user_document.deleteMany({
          where: { userId },
        });
      }

      if (requestType === DataDeletionType.PERSONAL_DATA_ONLY) {
        // Delete encrypted personal data
        await this.encryptionService.deleteEncryptedFields('user', userId);
        deletedData.encryptedFields = 'Personal data fields deleted';
      }
    }

    return { deletedData, retainedData };
  }

  /**
   * Get GDPR compliance report
   */
  async getComplianceReport(params: { startDate?: Date; endDate?: Date }) {
    const { startDate, endDate } = params;

    const where: any = {};
    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    const [
      totalConsents,
      grantedConsents,
      revokedConsents,
      exportRequests,
      deletionRequests,
      consentsByType,
    ] = await Promise.all([
      this.prisma.gdpr_consent.count({ where }),
      this.prisma.gdpr_consent.count({ where: { ...where, granted: true } }),
      this.prisma.gdpr_consent.count({ where: { ...where, granted: false } }),
      this.prisma.data_export_request.count({ where }),
      this.prisma.data_deletion_request.count({ where }),
      this.prisma.gdpr_consent.groupBy({
        by: ['consentType'],
        where,
        _count: { consentType: true },
      }),
    ]);

    return {
      totalConsents,
      grantedConsents,
      revokedConsents,
      exportRequests,
      deletionRequests,
      consentRate:
        totalConsents > 0
          ? ((grantedConsents / totalConsents) * 100).toFixed(2)
          : '0.00',
      consentsByType: consentsByType.map((item) => ({
        type: item.consentType,
        count: item._count.consentType,
      })),
    };
  }
}
