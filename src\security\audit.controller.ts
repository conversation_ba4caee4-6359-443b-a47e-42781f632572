import { Controller, Get, Query, UseGuards, Request } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { AuditService } from './audit.service';
import { JwtGuard } from '../guards/jwt.guard';
import { JwtAdminAgent } from '../guards/jwt.admin-agent.guard';

/**
 * Audit Trail Controller
 *
 * Provides endpoints for accessing audit logs and statistics.
 * Users can access their own audit logs, while admins and agents
 * can access system-wide audit information.
 */
@ApiTags('Audit')
@Controller('audit')
@ApiBearerAuth()
export class AuditController {
  constructor(private readonly auditService: AuditService) {}

  /**
   * Get audit logs with filtering (Admin/Agent access)
   */
  @Get('logs')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({ summary: 'Get audit logs with filtering and pagination' })
  @ApiResponse({
    status: 200,
    description: 'Audit logs retrieved successfully',
  })
  @ApiQuery({ name: 'userId', required: false, type: String })
  @ApiQuery({ name: 'entityType', required: false, type: String })
  @ApiQuery({ name: 'entityId', required: false, type: String })
  @ApiQuery({ name: 'action', required: false, type: String })
  @ApiQuery({ name: 'ipAddress', required: false, type: String })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  @ApiQuery({ name: 'success', required: false, type: Boolean })
  @ApiQuery({ name: 'riskScoreMin', required: false, type: Number })
  @ApiQuery({ name: 'riskScoreMax', required: false, type: Number })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async getAuditLogs(
    @Query('userId') userId?: string,
    @Query('entityType') entityType?: string,
    @Query('entityId') entityId?: string,
    @Query('action') action?: string,
    @Query('ipAddress') ipAddress?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('success') success?: boolean,
    @Query('riskScoreMin') riskScoreMin?: number,
    @Query('riskScoreMax') riskScoreMax?: number,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.auditService.getAuditLogs({
      userId,
      entityType,
      entityId,
      action,
      ipAddress,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      success,
      riskScoreMin,
      riskScoreMax,
      page,
      limit,
    });
  }

  /**
   * Get user's own audit logs
   */
  @Get('my-logs')
  @UseGuards(JwtGuard)
  @ApiOperation({ summary: "Get current user's audit logs" })
  @ApiResponse({
    status: 200,
    description: 'User audit logs retrieved successfully',
  })
  @ApiQuery({ name: 'entityType', required: false, type: String })
  @ApiQuery({ name: 'action', required: false, type: String })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async getMyAuditLogs(
    @Request() req: any,
    @Query('entityType') entityType?: string,
    @Query('action') action?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    const userId = req.user?.id;

    return this.auditService.getAuditLogs({
      userId,
      entityType,
      action,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      page,
      limit: Math.min(limit || 50, 100), // Limit user queries to max 100 records
    });
  }

  /**
   * Get audit statistics (Admin/Agent access)
   */
  @Get('statistics')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({ summary: 'Get audit trail statistics' })
  @ApiResponse({
    status: 200,
    description: 'Audit statistics retrieved successfully',
  })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  @ApiQuery({ name: 'userId', required: false, type: String })
  async getAuditStatistics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('userId') userId?: string,
  ) {
    return this.auditService.getAuditStatistics({
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      userId,
    });
  }

  /**
   * Get user's own audit statistics
   */
  @Get('my-statistics')
  @UseGuards(JwtGuard)
  @ApiOperation({ summary: "Get current user's audit statistics" })
  @ApiResponse({
    status: 200,
    description: 'User audit statistics retrieved successfully',
  })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  async getMyAuditStatistics(
    @Request() req: any,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const userId = req.user?.id;

    return this.auditService.getAuditStatistics({
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      userId,
    });
  }

  /**
   * Get high-risk audit events (Admin/Agent access)
   */
  @Get('high-risk')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({ summary: 'Get high-risk audit events' })
  @ApiResponse({
    status: 200,
    description: 'High-risk audit events retrieved successfully',
  })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async getHighRiskEvents(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.auditService.getAuditLogs({
      riskScoreMin: 70, // High risk threshold
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      page,
      limit,
    });
  }

  /**
   * Get failed operations (Admin/Agent access)
   */
  @Get('failed-operations')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({ summary: 'Get failed operations from audit logs' })
  @ApiResponse({
    status: 200,
    description: 'Failed operations retrieved successfully',
  })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async getFailedOperations(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.auditService.getAuditLogs({
      success: false,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      page,
      limit,
    });
  }

  /**
   * Get audit logs for specific entity (Admin/Agent access)
   */
  @Get('entity/:entityType/:entityId')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({ summary: 'Get audit logs for a specific entity' })
  @ApiResponse({
    status: 200,
    description: 'Entity audit logs retrieved successfully',
  })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async getEntityAuditLogs(
    @Query('entityType') entityType: string,
    @Query('entityId') entityId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.auditService.getAuditLogs({
      entityType,
      entityId,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      page,
      limit,
    });
  }

  /**
   * Get authentication audit logs (Admin/Agent access)
   */
  @Get('authentication')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({ summary: 'Get authentication-related audit logs' })
  @ApiResponse({
    status: 200,
    description: 'Authentication audit logs retrieved successfully',
  })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  @ApiQuery({ name: 'success', required: false, type: Boolean })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async getAuthenticationLogs(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('success') success?: boolean,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.auditService.getAuditLogs({
      entityType: 'authentication',
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      success,
      page,
      limit,
    });
  }

  /**
   * Get data access audit logs (Admin/Agent access)
   */
  @Get('data-access')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({ summary: 'Get data access audit logs' })
  @ApiResponse({
    status: 200,
    description: 'Data access audit logs retrieved successfully',
  })
  @ApiQuery({ name: 'userId', required: false, type: String })
  @ApiQuery({ name: 'entityType', required: false, type: String })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async getDataAccessLogs(
    @Query('userId') userId?: string,
    @Query('entityType') entityType?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.auditService.getAuditLogs({
      action: 'DATA_',
      userId,
      entityType,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      page,
      limit,
    });
  }
}
