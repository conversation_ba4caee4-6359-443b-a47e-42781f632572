import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Min,
  Max,
  IsPositive,
  MaxLength,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO for creating a new service type
 */
export class CreateServiceTypeDto {
  @ApiProperty({
    description: 'Name of the service type',
    example: 'Critical Skills Employment Permit',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;

  @ApiProperty({
    description: 'Description of the service type',
    example: 'Permit for highly skilled professionals in eligible occupations',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(1000)
  description: string;

  @ApiProperty({
    description: 'Price of the service in EUR',
    example: 1000,
  })
  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  price: number;
}

/**
 * DTO for updating an existing service type
 */
export class UpdateServiceTypeDto {
  @ApiProperty({
    description: 'Name of the service type',
    example: 'Critical Skills Employment Permit',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Description of the service type',
    example: 'Permit for highly skilled professionals in eligible occupations',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(1000)
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Price of the service in EUR',
    example: 1000,
    required: false,
  })
  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  @IsOptional()
  price?: number;
}

/**
 * DTO for filtering service types
 */
export class FilterServiceTypeDto {
  @ApiPropertyOptional({
    description: 'Search term for name or description',
    example: 'permit',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({
    description: 'Minimum price filter',
    example: 500,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  @IsOptional()
  minPrice?: number;

  @ApiPropertyOptional({
    description: 'Maximum price filter',
    example: 2000,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  @IsOptional()
  maxPrice?: number;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    default: 1,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  @IsOptional()
  page?: number = 1;

  @ApiPropertyOptional({
    description:
      'Items per page for pagination (max 100 for admin, 50 for public)',
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  @IsOptional()
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Sort field',
    example: 'name',
    enum: ['name', 'price', 'createdAt', 'updatedAt'],
  })
  @IsString()
  @IsOptional()
  @IsEnum(['name', 'price', 'createdAt', 'updatedAt'])
  sortBy?: string = 'name';

  @ApiPropertyOptional({
    description: 'Sort order',
    example: 'asc',
    enum: ['asc', 'desc'],
  })
  @IsString()
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'asc';
}

/**
 * Response DTO for service type data
 */
export class ServiceTypeResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the service type',
    example: 'clx1234567890abcdef',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the service type',
    example: 'Critical Skills Employment Permit',
  })
  name: string;

  @ApiProperty({
    description: 'Description of the service type',
    example: 'Permit for highly skilled professionals in eligible occupations',
  })
  description: string;

  @ApiProperty({
    description: 'Price of the service in EUR',
    example: 1000,
  })
  price: number;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Number of document requirements (admin only)',
    example: 5,
  })
  documentRequirementsCount?: number;

  @ApiPropertyOptional({
    description: 'Number of applications using this service type (admin only)',
    example: 12,
  })
  applicationsCount?: number;

  @ApiPropertyOptional({
    description: 'Document requirements for this service type',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        description: { type: 'string' },
        required: { type: 'boolean' },
        category: { type: 'string', enum: ['EMPLOYEE', 'EMPLOYER'] },
      },
    },
  })
  documentRequirements?: any[];
}

/**
 * Paginated response DTO for service types
 */
export class PaginatedServiceTypeResponseDto {
  @ApiProperty({
    description: 'Array of service types',
    type: [ServiceTypeResponseDto],
  })
  data: ServiceTypeResponseDto[];

  @ApiProperty({
    description: 'Total number of service types',
    example: 25,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 3,
  })
  totalPages: number;
}
