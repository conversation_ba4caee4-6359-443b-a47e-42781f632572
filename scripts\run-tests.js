/**
 * Test runner script that ensures tests run with the correct configuration
 * This script sets environment variables to ensure tests don't hit production services
 */

const { spawn } = require('child_process');
const path = require('path');

// Set environment variables for testing
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
process.env.SUPABASE_URL = 'https://mock-supabase-url.com';
process.env.SUPABASE_KEY = 'mock-supabase-key';

// Path to the local Jest binary
const jestBinPath = path.resolve(__dirname, '../node_modules/.bin/jest');

// Run Jest with the correct configuration
const jest = spawn(jestBinPath, process.argv.slice(2), {
  stdio: 'inherit',
  env: process.env,
  shell: true, // Use shell on Windows
});

// Handle process exit
jest.on('exit', (code) => {
  process.exit(code);
});

// Handle process errors
jest.on('error', (err) => {
  console.error('Failed to start Jest:', err);
  process.exit(1);
});
