<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="200" alt="Nest Logo" /></a>
</p>

[circleci-image]: https://img.shields.io/circleci/build/github/nestjs/nest/master?token=abc123def456
[circleci-url]: https://circleci.com/gh/nestjs/nest

  <p align="center">A progressive <a href="http://nodejs.org" target="_blank">Node.js</a> framework for building efficient and scalable server-side applications.</p>
    <p align="center">
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/v/@nestjs/core.svg" alt="NPM Version" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/l/@nestjs/core.svg" alt="Package License" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/dm/@nestjs/common.svg" alt="NPM Downloads" /></a>
<a href="https://circleci.com/gh/nestjs/nest" target="_blank"><img src="https://img.shields.io/circleci/build/github/nestjs/nest/master" alt="CircleCI" /></a>
<a href="https://coveralls.io/github/nestjs/nest?branch=master" target="_blank"><img src="https://coveralls.io/repos/github/nestjs/nest/badge.svg?branch=master#9" alt="Coverage" /></a>
<a href="https://discord.gg/G7Qnnhy" target="_blank"><img src="https://img.shields.io/badge/discord-online-brightgreen.svg" alt="Discord"/></a>
<a href="https://opencollective.com/nest#backer" target="_blank"><img src="https://opencollective.com/nest/backers/badge.svg" alt="Backers on Open Collective" /></a>
<a href="https://opencollective.com/nest#sponsor" target="_blank"><img src="https://opencollective.com/nest/sponsors/badge.svg" alt="Sponsors on Open Collective" /></a>
  <a href="https://paypal.me/kamilmysliwiec" target="_blank"><img src="https://img.shields.io/badge/Donate-PayPal-ff3f59.svg"/></a>
    <a href="https://opencollective.com/nest#sponsor"  target="_blank"><img src="https://img.shields.io/badge/Support%20us-Open%20Collective-41B883.svg" alt="Support us"></a>
  <a href="https://twitter.com/nestframework" target="_blank"><img src="https://img.shields.io/twitter/follow/nestframework.svg?style=social&label=Follow"></a>
</p>
  <!--[![Backers on Open Collective](https://opencollective.com/nest/backers/badge.svg)](https://opencollective.com/nest#backer)
  [![Sponsors on Open Collective](https://opencollective.com/nest/sponsors/badge.svg)](https://opencollective.com/nest#sponsor)-->

## Description

[Nest](https://github.com/nestjs/nest) framework TypeScript starter repository.

## Installation

```bash
$ npm install
```

## Running the app

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

# Task Management

## Task Master

The project includes a task management system to help organize and track development work.

```bash
# Show help and available commands
$ npm run task

# List all tasks
$ npm run task:list

# List pending tasks only
$ npm run task:pending

# List tasks in progress
$ npm run task:progress

# List completed tasks
$ npm run task:completed

# Show detailed task information
$ npm run task show 4

# Start working on a task (checks dependencies)
$ npm run task start 4

# Mark task as completed
$ npm run task complete 4
```

For detailed task implementation guidance, see [Task Implementation Guide](docs/TASK_IMPLEMENTATION_GUIDE.md) and [Task Master Quick Reference](docs/TASK_MASTER_QUICK_REFERENCE.md).

# API Documentation

## Application Management API

The Career Ireland Immigration SaaS platform provides comprehensive APIs for managing immigration applications, workflows, and related communications.

### Quick Reference

For a quick overview of all endpoints, see: [Application API Quick Reference](docs/api/application-api-quick-reference.md)

### Complete Documentation

For detailed API documentation including request/response examples, authentication, and error handling, see: [Application Management API Documentation](docs/api/application-management-api.md)

### Key Features

- **Application Lifecycle Management**: Complete CRUD operations for immigration applications
- **Workflow Automation**: Automatic workflow creation and step-by-step progression
- **Progress Tracking**: Real-time progress calculation with completion estimates
- **Communication System**: Q&A between clients and admins, checkpoint call scheduling
- **Role-based Access Control**: Secure access with USER, AGENT, and ADMIN roles
- **Statistics Dashboard**: Comprehensive application metrics for administrators

### API Endpoints Overview

| Category | Endpoints | Description |
|----------|-----------|-------------|
| **Applications** | 7 endpoints | Create, read, update, delete applications |
| **Progress & Workflow** | 2 endpoints | Track progress and advance workflows |
| **Checkpoint Calls** | 3 endpoints | Schedule and manage client consultations |
| **Queries** | 3 endpoints | Q&A communication system |
| **Workflow Steps** | 2 endpoints | Admin workflow configuration |

### Authentication

All API endpoints require JWT authentication:

```bash
curl -H "Authorization: Bearer <jwt_token>" \
     https://api.careerireland.com/applications
```

### Status Codes

- `200` OK - Successful GET, PATCH operations
- `201` Created - Successful POST operations
- `400` Bad Request - Validation errors
- `401` Unauthorized - Missing/invalid token
- `403` Forbidden - Insufficient permissions
- `404` Not Found - Resource not found

# Testing Guide

## Running Tests

```bash
# Run all tests
$ npm test

# Run tests in watch mode (tests will re-run when files change)
$ npm run test:watch

# Run tests with coverage report
$ npm run test:cov

# Run tests in debug mode
$ npm run test:debug

# Run end-to-end tests
$ npm run test:e2e

# Run a specific test file
$ npm test -- test/document.service.spec.ts

# Run tests matching a specific pattern
$ npm test -- -t "uploadDocument"
```

## Testing Architecture

The Career Ireland Immigration SaaS platform uses a comprehensive testing approach designed to ensure code quality while preventing tests from interacting with production services.

### Key Testing Principles

1. **Isolation**: Tests run in isolation without hitting production databases or external services.
2. **Mocking**: External dependencies are mocked to prevent side effects.
3. **Consistency**: Tests produce consistent results regardless of the environment.
4. **Coverage**: We aim for high test coverage of critical business logic.

### Mock Services

We use mock implementations of services to prevent tests from hitting production resources:

- **MockPrismaService**: An in-memory implementation of the database service.
- **MockMediaService**: A mock implementation that simulates file operations without using Supabase storage.

### Test Setup

Tests are configured using:

1. **jest-setup.ts**: Sets up the test environment with appropriate variables.
2. **run-tests.js**: A custom script that ensures tests run with the correct configuration.

### Example Test

```typescript
describe('DocumentService', () => {
  let service: DocumentService;
  let prismaService: MockPrismaService;
  let mediaService: MockMediaService;

  beforeEach(async () => {
    // Create fresh instances of mock services
    const mockPrismaService = new MockPrismaService();
    const mockMediaService = new MockMediaService();

    // Seed test data
    mockPrismaService.seedTestData();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentService,
        { provide: PrismaService, useValue: mockPrismaService },
        { provide: MediaService, useValue: mockMediaService },
      ],
    }).compile();

    service = module.get<DocumentService>(DocumentService);
    prismaService = module.get<MockPrismaService>(PrismaService);
    mediaService = module.get<MockMediaService>(MediaService);
  });

  it('should upload a document without hitting production services', async () => {
    // Test implementation
  });
});
```

## Writing Tests

When writing tests, follow these guidelines:

1. **Use Mock Services**: Always use mock services instead of real ones.
2. **Clean Up**: Reset test data between tests to prevent test interdependence.
3. **Test Edge Cases**: Include tests for error conditions and edge cases.
4. **Descriptive Names**: Use descriptive test names that explain the expected behavior.
5. **Arrange-Act-Assert**: Structure tests with clear arrangement, action, and assertion phases.

## Test Directory Structure

```
test/
├── jest-setup.ts           # Test environment setup
├── document.service.spec.ts # Tests for document service
├── README.md               # Testing documentation
└── jest-e2e.json          # E2E test configuration
```

## Continuous Integration

Tests are automatically run in the CI pipeline to ensure code quality before deployment. The pipeline:

1. Installs dependencies
2. Runs linting checks
3. Executes unit tests
4. Runs E2E tests
5. Generates coverage reports

For more detailed information about testing specific modules, refer to the test directory README.

## Support

Nest is an MIT-licensed open source project. It can grow thanks to the sponsors and support by the amazing backers. If you'd like to join them, please [read more here](https://docs.nestjs.com/support).

## Stay in touch

- Author - [Kamil Myśliwiec](https://kamilmysliwiec.com)
- Website - [https://nestjs.com](https://nestjs.com/)
- Twitter - [@nestframework](https://twitter.com/nestframework)

## License

Nest is [MIT licensed](LICENSE).
