import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsOptional,
  IsString,
  IsDateString,
  IsUUID,
  IsBoolean,
  IsN<PERSON>ber,
  Min,
  Max,
  Matches,
} from 'class-validator';
import { Type } from 'class-transformer';
import { VerificationStatus, DocCategory } from '@prisma/client';

export class UploadDocumentDto {
  @ApiProperty({
    description: 'Name of the document',
    example: 'Passport',
  })
  @IsString()
  documentName: string;

  @ApiPropertyOptional({
    description: 'Optional expiry date of the document',
    example: '2025-12-31',
  })
  @IsOptional()
  @IsDateString()
  expiryDate?: string;

  @ApiPropertyOptional({
    description:
      'User ID in CUID format (REQUIRED for admin/agent use, must be a valid user ID)',
    example: 'cmb15qlnj0000f5wsl08v5k30',
  })
  @IsOptional() // Still optional in the DTO, but required for admins/agents in the controller
  @IsString({ message: 'userId must be a string' })
  @Matches(/^c[a-z0-9]{24}$/, { message: 'userId must be a valid CUID format' })
  userId?: string;

  @ApiPropertyOptional({
    description: 'Category of the document (EMPLOYEE or EMPLOYER)',
    enum: DocCategory,
    example: DocCategory.EMPLOYEE,
  })
  @IsOptional()
  @IsEnum(DocCategory)
  category?: DocCategory;

  @ApiPropertyOptional({
    description: 'Service type ID this document is related to',
    example: 'clx1234567890abcdef',
  })
  @IsOptional()
  @IsString()
  serviceTypeId?: string;

  @ApiPropertyOptional({
    description: 'Application ID this document is related to',
    example: 'clx1234567890abcdef',
  })
  @IsOptional()
  @IsUUID()
  applicationId?: string;

  @ApiPropertyOptional({
    description: 'Document requirement ID this document fulfills',
    example: 'clx1234567890abcdef',
  })
  @IsOptional()
  @IsUUID()
  documentRequirementId?: string;

  @ApiPropertyOptional({
    description: 'Additional notes about the document',
    example: 'Passport valid for 2 years',
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class VerifyDocumentDto {
  @ApiProperty({
    description: 'Verification status of the document',
    enum: VerificationStatus,
    example: 'APPROVED',
  })
  @IsEnum(VerificationStatus)
  verificationStatus: VerificationStatus;

  @ApiProperty({
    description: 'Reason for rejection (required if status is REJECTED)',
    example: 'Document is not legible',
    required: false,
  })
  @IsOptional()
  @IsString()
  rejectionReason?: string;
}

export class DocumentFilterDto {
  @ApiPropertyOptional({
    description: 'Filter by verification status',
    enum: VerificationStatus,
  })
  @IsOptional()
  @IsEnum(VerificationStatus)
  verificationStatus?: VerificationStatus;

  @ApiPropertyOptional({
    description: 'Filter by document name (partial match)',
  })
  @IsOptional()
  @IsString()
  documentName?: string;

  @ApiPropertyOptional({
    description: 'Filter by document category',
    enum: DocCategory,
  })
  @IsOptional()
  @IsEnum(DocCategory)
  category?: DocCategory;

  @ApiPropertyOptional({
    description: 'Filter by service type ID',
  })
  @IsOptional()
  @IsUUID()
  serviceTypeId?: string;

  @ApiPropertyOptional({
    description: 'Filter by application ID',
  })
  @IsOptional()
  @IsUUID()
  applicationId?: string;

  @ApiPropertyOptional({
    description: 'Filter by document requirement ID',
  })
  @IsOptional()
  @IsUUID()
  documentRequirementId?: string;

  @ApiPropertyOptional({
    description: 'Filter by expiry date range - start date',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString()
  expiryDateFrom?: string;

  @ApiPropertyOptional({
    description: 'Filter by expiry date range - end date',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  expiryDateTo?: string;

  @ApiPropertyOptional({
    description: 'Filter by upload date range - start date',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString()
  uploadDateFrom?: string;

  @ApiPropertyOptional({
    description: 'Filter by upload date range - end date',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  uploadDateTo?: string;

  @ApiPropertyOptional({
    description: 'Filter documents that are expiring soon (within X days)',
    example: 30,
    minimum: 1,
    maximum: 365,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(365)
  @Type(() => Number)
  expiringSoonDays?: number;

  @ApiPropertyOptional({
    description: 'Filter expired documents only',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  expiredOnly?: boolean;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    default: 1,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Items per page for pagination (max 100)',
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Sort field',
    example: 'uploadDate',
    enum: ['uploadDate', 'documentName', 'verificationStatus', 'expiryDate'],
  })
  @IsOptional()
  @IsString()
  @IsEnum(['uploadDate', 'documentName', 'verificationStatus', 'expiryDate'])
  sortBy?: string = 'uploadDate';

  @ApiPropertyOptional({
    description: 'Sort order',
    example: 'desc',
    enum: ['asc', 'desc'],
  })
  @IsOptional()
  @IsString()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';
}

/**
 * Response DTO for document data
 */
export class DocumentResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the document',
    example: 'clx1234567890abcdef',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the document',
    example: 'Passport',
  })
  documentName: string;

  @ApiProperty({
    description: 'URL to access the document file',
    example: 'https://supabase.co/storage/v1/object/public/documents/...',
  })
  fileUrl: string;

  @ApiProperty({
    description: 'Upload timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  uploadDate: Date;

  @ApiProperty({
    description: 'Verification status of the document',
    enum: VerificationStatus,
    example: VerificationStatus.PENDING,
  })
  verificationStatus: VerificationStatus;

  @ApiPropertyOptional({
    description: 'Reason for rejection (if rejected)',
    example: 'Document is not legible',
  })
  rejectionReason?: string;

  @ApiPropertyOptional({
    description: 'Expiry date of the document',
    example: '2025-12-31T00:00:00.000Z',
  })
  expiryDate?: Date;

  @ApiPropertyOptional({
    description: 'Category of the document',
    enum: DocCategory,
    example: DocCategory.EMPLOYEE,
  })
  category?: DocCategory;

  @ApiPropertyOptional({
    description: 'Service type ID this document is related to',
    example: 'clx1234567890abcdef',
  })
  serviceTypeId?: string;

  @ApiPropertyOptional({
    description: 'Application ID this document is related to',
    example: 'clx1234567890abcdef',
  })
  applicationId?: string;

  @ApiPropertyOptional({
    description: 'Document requirement ID this document fulfills',
    example: 'clx1234567890abcdef',
  })
  documentRequirementId?: string;

  @ApiPropertyOptional({
    description: 'Additional notes about the document',
    example: 'Passport valid for 2 years',
  })
  notes?: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'User information (admin only)',
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      email: { type: 'string' },
    },
  })
  user?: any;

  @ApiPropertyOptional({
    description: 'Service type information',
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      description: { type: 'string' },
    },
  })
  serviceType?: any;

  @ApiPropertyOptional({
    description: 'Application information',
    type: 'object',
    properties: {
      id: { type: 'string' },
      status: { type: 'string' },
      submissionDate: { type: 'string' },
    },
  })
  application?: any;

  @ApiPropertyOptional({
    description: 'Document requirement information',
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      required: { type: 'boolean' },
    },
  })
  documentRequirement?: any;

  @ApiPropertyOptional({
    description: 'Whether the document is expired',
    example: false,
  })
  isExpired?: boolean;

  @ApiPropertyOptional({
    description: 'Days until expiry (negative if expired)',
    example: 30,
  })
  daysUntilExpiry?: number;
}

/**
 * Paginated response DTO for documents
 */
export class PaginatedDocumentResponseDto {
  @ApiProperty({
    description: 'Array of documents',
    type: [DocumentResponseDto],
  })
  data: DocumentResponseDto[];

  @ApiProperty({
    description: 'Total number of documents',
    example: 25,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 3,
  })
  totalPages: number;
}

/**
 * Bulk operation DTO for documents
 */
export class BulkDocumentOperationDto {
  @ApiProperty({
    description: 'Array of document IDs to operate on',
    example: ['clx1234567890abcdef', 'clx1234567890abcdeg'],
    type: [String],
  })
  @IsString({ each: true })
  @IsUUID(undefined, { each: true })
  documentIds: string[];
}

/**
 * Bulk verification DTO for documents
 */
export class BulkVerifyDocumentDto extends BulkDocumentOperationDto {
  @ApiProperty({
    description: 'Verification status to apply to all documents',
    enum: VerificationStatus,
    example: VerificationStatus.APPROVED,
  })
  @IsEnum(VerificationStatus)
  verificationStatus: VerificationStatus;

  @ApiPropertyOptional({
    description: 'Reason for rejection (required if status is REJECTED)',
    example: 'Documents are not legible',
  })
  @IsOptional()
  @IsString()
  rejectionReason?: string;
}
