import { Test, TestingModule } from '@nestjs/testing';
import { ImmigrationService } from './immigration.service';
import { PrismaService } from 'src/utils/prisma.service';
import { ImmigrationDto } from './dto/immigration.dto';

describe('ImmigrationService', () => {
  let service: ImmigrationService;
  let prismaService: PrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ImmigrationService,
        {
          provide: PrismaService,
          useValue: {
            immigration_service: {
              create: jest.fn(),
              update: jest.fn(),
              delete: jest.fn(),
              findMany: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    service = module.get<ImmigrationService>(ImmigrationService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create an immigration service', async () => {
      const dto: ImmigrationDto = {
        name: 'Test Immigration Service',
        amount: 100,
        order: 1,
        service: ['service1', 'service2'],
      };

      const expectedResult = {
        id: 'test-id',
        ...dto,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      jest.spyOn(prismaService.immigration_service, 'create').mockResolvedValue(expectedResult);

      const result = await service.create(dto);
      
      expect(prismaService.immigration_service.create).toHaveBeenCalledWith({
        data: dto,
      });
      expect(result).toEqual(expectedResult);
    });
  });

  describe('update', () => {
    it('should update an immigration service', async () => {
      const id = 'test-id';
      const dto: ImmigrationDto = {
        name: 'Updated Immigration Service',
        amount: 200,
        order: 2,
        service: ['service1', 'service2', 'service3'],
      };

      const expectedResult = {
        id,
        ...dto,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      jest.spyOn(prismaService.immigration_service, 'update').mockResolvedValue(expectedResult);

      const result = await service.update(id, dto);
      
      expect(prismaService.immigration_service.update).toHaveBeenCalledWith({
        where: { id },
        data: dto,
      });
      expect(result).toEqual(expectedResult);
    });
  });

  describe('remove', () => {
    it('should delete an immigration service', async () => {
      const id = 'test-id';
      const expectedResult = {
        id,
        name: 'Test Immigration Service',
        amount: 100,
        order: 1,
        service: ['service1', 'service2'],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      jest.spyOn(prismaService.immigration_service, 'delete').mockResolvedValue(expectedResult);

      const result = await service.remove(id);
      
      expect(prismaService.immigration_service.delete).toHaveBeenCalledWith({
        where: { id },
      });
      expect(result).toEqual(expectedResult);
    });
  });

  describe('getAll', () => {
    it('should return all immigration services', async () => {
      const expectedResult = [
        {
          id: 'test-id-1',
          name: 'Test Immigration Service 1',
          amount: 100,
          order: 1,
          service: ['service1', 'service2'],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'test-id-2',
          name: 'Test Immigration Service 2',
          amount: 200,
          order: 2,
          service: ['service3', 'service4'],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      jest.spyOn(prismaService.immigration_service, 'findMany').mockResolvedValue(expectedResult);

      const result = await service.getAll();
      
      expect(prismaService.immigration_service.findMany).toHaveBeenCalledWith({
        orderBy: [{ order: 'asc' }, { createdAt: 'desc' }],
      });
      expect(result).toEqual(expectedResult);
    });
  });
});
