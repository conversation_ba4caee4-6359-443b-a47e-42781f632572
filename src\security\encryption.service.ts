import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as CryptoJS from 'crypto-js';
import { PrismaService } from '../utils/prisma.service';

/**
 * Field-Level Encryption Service
 *
 * Provides transparent encryption/decryption for sensitive data fields.
 * Uses AES-256-GCM encryption with key versioning support.
 *
 * Features:
 * - Transparent field-level encryption
 * - Key rotation support
 * - Secure key management
 * - Audit trail for encrypted data
 */
@Injectable()
export class EncryptionService {
  private readonly logger = new Logger(EncryptionService.name);
  private readonly encryptionKey: string;
  private readonly keyVersion: string = '1';
  private readonly algorithm: string = 'AES-256-GCM';

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
  ) {
    this.encryptionKey =
      this.configService.get<string>('ENCRYPTION_KEY') ||
      this.generateDefaultKey();

    if (!this.configService.get<string>('ENCRYPTION_KEY')) {
      this.logger.warn(
        'ENCRYPTION_KEY not set in environment. Using default key for development.',
      );
    }
  }

  /**
   * Encrypt sensitive data and store in encrypted_field table
   */
  async encryptField(
    entityType: string,
    entityId: string,
    fieldName: string,
    value: string,
  ): Promise<void> {
    try {
      if (!value || value.trim() === '') {
        return;
      }

      const encryptedValue = this.encrypt(value);

      await this.prisma.encrypted_field.upsert({
        where: {
          entityType_entityId_fieldName: {
            entityType,
            entityId,
            fieldName,
          },
        },
        update: {
          encryptedValue,
          keyVersion: this.keyVersion,
          algorithm: this.algorithm,
          updatedAt: new Date(),
        },
        create: {
          entityType,
          entityId,
          fieldName,
          encryptedValue,
          keyVersion: this.keyVersion,
          algorithm: this.algorithm,
        },
      });

      this.logger.log(
        `Encrypted field ${fieldName} for ${entityType}:${entityId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to encrypt field ${fieldName}: ${error.message}`,
        error.stack,
      );
      throw new Error('Encryption failed');
    }
  }

  /**
   * Decrypt sensitive data from encrypted_field table
   */
  async decryptField(
    entityType: string,
    entityId: string,
    fieldName: string,
  ): Promise<string | null> {
    try {
      const encryptedField = await this.prisma.encrypted_field.findUnique({
        where: {
          entityType_entityId_fieldName: {
            entityType,
            entityId,
            fieldName,
          },
        },
      });

      if (!encryptedField) {
        return null;
      }

      const decryptedValue = this.decrypt(encryptedField.encryptedValue);
      return decryptedValue;
    } catch (error) {
      this.logger.error(
        `Failed to decrypt field ${fieldName}: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * Encrypt multiple fields for an entity
   */
  async encryptFields(
    entityType: string,
    entityId: string,
    fields: Record<string, string>,
  ): Promise<void> {
    const promises = Object.entries(fields).map(([fieldName, value]) =>
      this.encryptField(entityType, entityId, fieldName, value),
    );

    await Promise.all(promises);
  }

  /**
   * Decrypt multiple fields for an entity
   */
  async decryptFields(
    entityType: string,
    entityId: string,
    fieldNames: string[],
  ): Promise<Record<string, string | null>> {
    const promises = fieldNames.map(async (fieldName) => ({
      [fieldName]: await this.decryptField(entityType, entityId, fieldName),
    }));

    const results = await Promise.all(promises);
    return results.reduce((acc, curr) => ({ ...acc, ...curr }), {});
  }

  /**
   * Delete encrypted fields for an entity (GDPR compliance)
   */
  async deleteEncryptedFields(
    entityType: string,
    entityId: string,
    fieldNames?: string[],
  ): Promise<void> {
    try {
      const where: any = {
        entityType,
        entityId,
      };

      if (fieldNames && fieldNames.length > 0) {
        where.fieldName = { in: fieldNames };
      }

      await this.prisma.encrypted_field.deleteMany({ where });

      this.logger.log(`Deleted encrypted fields for ${entityType}:${entityId}`);
    } catch (error) {
      this.logger.error(
        `Failed to delete encrypted fields: ${error.message}`,
        error.stack,
      );
      throw new Error('Failed to delete encrypted data');
    }
  }

  /**
   * Core encryption method using AES-256-GCM
   */
  private encrypt(text: string): string {
    try {
      const encrypted = CryptoJS.AES.encrypt(
        text,
        this.encryptionKey,
      ).toString();
      return encrypted;
    } catch (error) {
      this.logger.error(`Encryption failed: ${error.message}`);
      throw new Error('Encryption failed');
    }
  }

  /**
   * Core decryption method using AES-256-GCM
   */
  private decrypt(encryptedText: string): string {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedText, this.encryptionKey);
      const decrypted = bytes.toString(CryptoJS.enc.Utf8);

      if (!decrypted) {
        throw new Error('Decryption resulted in empty string');
      }

      return decrypted;
    } catch (error) {
      this.logger.error(`Decryption failed: ${error.message}`);
      throw new Error('Decryption failed');
    }
  }

  /**
   * Generate a default encryption key for development
   */
  private generateDefaultKey(): string {
    return 'dev-encryption-key-change-in-production-' + Date.now();
  }

  /**
   * Check if a field is encrypted
   */
  async isFieldEncrypted(
    entityType: string,
    entityId: string,
    fieldName: string,
  ): Promise<boolean> {
    const encryptedField = await this.prisma.encrypted_field.findUnique({
      where: {
        entityType_entityId_fieldName: {
          entityType,
          entityId,
          fieldName,
        },
      },
    });

    return !!encryptedField;
  }

  /**
   * Get encryption metadata for an entity
   */
  async getEncryptionMetadata(
    entityType: string,
    entityId: string,
  ): Promise<
    Array<{
      fieldName: string;
      keyVersion: string;
      algorithm: string;
      createdAt: Date;
      updatedAt: Date;
    }>
  > {
    const encryptedFields = await this.prisma.encrypted_field.findMany({
      where: {
        entityType,
        entityId,
      },
      select: {
        fieldName: true,
        keyVersion: true,
        algorithm: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return encryptedFields;
  }

  /**
   * Rotate encryption key for all encrypted fields
   * This is a maintenance operation that should be run periodically
   */
  async rotateEncryptionKey(newKey: string, newVersion: string): Promise<void> {
    this.logger.log('Starting encryption key rotation...');

    try {
      const allEncryptedFields = await this.prisma.encrypted_field.findMany();

      for (const field of allEncryptedFields) {
        // Decrypt with old key
        const decryptedValue = this.decrypt(field.encryptedValue);

        // Encrypt with new key
        const newEncryptedValue = CryptoJS.AES.encrypt(
          decryptedValue,
          newKey,
        ).toString();

        // Update the field
        await this.prisma.encrypted_field.update({
          where: { id: field.id },
          data: {
            encryptedValue: newEncryptedValue,
            keyVersion: newVersion,
            updatedAt: new Date(),
          },
        });
      }

      this.logger.log(
        `Successfully rotated encryption key for ${allEncryptedFields.length} fields`,
      );
    } catch (error) {
      this.logger.error(`Key rotation failed: ${error.message}`, error.stack);
      throw new Error('Key rotation failed');
    }
  }
}
