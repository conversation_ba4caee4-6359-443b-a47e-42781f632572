import { Modu<PERSON> } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { NotificationController } from './notification.controller';
import { NotificationService } from './notification.service';
import { PrismaService } from '../utils/prisma.service';
import { MailerService } from '../mailer/mailer.service';

/**
 * Notification Module
 * Handles all notification-related functionality including CRUD operations,
 * real-time notifications, and email integration
 */
@Module({
  controllers: [NotificationController],
  providers: [NotificationService, PrismaService, MailerService, JwtService],
  exports: [NotificationService],
})
export class NotificationModule {}
