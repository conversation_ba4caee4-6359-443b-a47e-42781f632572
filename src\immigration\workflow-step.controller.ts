import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { ApplicationService } from './application.service';
import { CreateWorkflowStepDto } from './dto/application.dto';
import { JwtAdmin } from '../guards/jwt.admin.guard';

/**
 * Controller for managing workflow steps (Admin only)
 * 
 * Provides endpoints for:
 * - Creating workflow steps for service types
 * - Getting workflow steps for service types
 */
@ApiTags('Workflow Steps')
@Controller('admin/workflow-steps')
@UseGuards(JwtAdmin)
@ApiBearerAuth()
export class WorkflowStepController {
  constructor(private readonly applicationService: ApplicationService) {}

  /**
   * Create a workflow step for a service type
   */
  @Post()
  @ApiOperation({ summary: 'Create a workflow step (Admin only)' })
  @ApiResponse({
    status: 201,
    description: 'Workflow step created successfully.',
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Service type not found.' })
  async create(@Body() createWorkflowStepDto: CreateWorkflowStepDto) {
    return this.applicationService.createWorkflowStep(createWorkflowStepDto);
  }

  /**
   * Get workflow steps for a service type
   */
  @Get('service-type/:serviceTypeId')
  @ApiOperation({ summary: 'Get workflow steps for a service type (Admin only)' })
  @ApiParam({ name: 'serviceTypeId', description: 'Service type ID' })
  @ApiResponse({
    status: 200,
    description: 'List of workflow steps.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  async getWorkflowSteps(@Param('serviceTypeId') serviceTypeId: string) {
    return this.applicationService.getWorkflowSteps(serviceTypeId);
  }
}
