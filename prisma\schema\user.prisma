model user {
  id                   String                     @id @default(cuid())
  name                 String
  email                String                     @unique
  emailVerified        Boolean?                   @default(false)
  image                String?
  password             String?
  createdAt            DateTime                   @default(now())
  updatedAt            DateTime                   @updatedAt
  provider             Provider
  reviews              review[]
  services             user_mentor_service[]
  packages             user_package[]
  immigration_services user_immigration_service[]
  training             user_training[]
  comments             comment[]
  // New Immigration System relationships
  documents            user_document[]
  applications         application[]
  notifications        notification[]
  reminders            reminder[]

  // Security and Audit relationships
  auditLogs            audit_log[]
  securityEvents       security_event[]
  gdprConsents         gdpr_consent[]
  dataExportRequests   data_export_request[]
  dataDeletionRequests data_deletion_request[]
}

model user_mentor_service {
  id              String   @id @default(cuid())
  amount          Int
  status          String
  progress        Status   @default(Pending)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  user            user?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  userId          String?
  mentor_services service? @relation(fields: [serviceId], references: [id], onDelete: SetNull)
  serviceId       String?
}

model guest_mentor_service {
  id              String   @id @default(cuid())
  amount          Int
  status          String
  name            String
  email           String
  mobile_no       String
  progress        Status   @default(Pending)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  mentor_services service? @relation(fields: [serviceId], references: [id], onDelete: SetNull)
  serviceId       String?
}

model user_package {
  id        String    @id @default(cuid())
  amount    Int
  status    String
  progress  Status    @default(Pending)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  user      user?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  userId    String?
  package   packages? @relation(fields: [packageId], references: [id], onDelete: SetNull)
  packageId String?
}

model guest_package {
  id        String    @id @default(cuid())
  amount    Int
  status    String
  name      String
  email     String
  mobile_no String
  progress  Status    @default(Pending)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  package   packages? @relation(fields: [packageId], references: [id], onDelete: SetNull)
  packageId String?
}

model user_immigration_service {
  id                    String               @id @default(cuid())
  amount                Int
  status                String
  progress              Status               @default(Pending)
  createdAt             DateTime             @default(now())
  updatedAt             DateTime             @updatedAt
  user                  user?                @relation(fields: [userId], references: [id], onDelete: SetNull)
  userId                String?
  immigration_service   immigration_service? @relation(fields: [immigration_serviceId], references: [id], onDelete: SetNull)
  immigration_serviceId String?
}

model guest_immigration_service {
  id                    String               @id @default(cuid())
  amount                Int
  status                String
  name                  String
  email                 String
  mobile_no             String
  progress              Status               @default(Pending)
  createdAt             DateTime             @default(now())
  updatedAt             DateTime             @updatedAt
  immigration_service   immigration_service? @relation(fields: [immigration_serviceId], references: [id], onDelete: SetNull)
  immigration_serviceId String?
}

model user_training {
  id         String    @id @default(cuid())
  amount     Int
  status     String
  progress   Status    @default(Pending)
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  user       user?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  userId     String?
  training   training? @relation(fields: [trainingId], references: [id], onDelete: SetNull)
  trainingId String?
}

model guest_training {
  id         String    @id @default(cuid())
  amount     Int
  status     String
  name       String
  email      String
  mobile_no  String
  progress   Status    @default(Pending)
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  training   training? @relation(fields: [trainingId], references: [id], onDelete: SetNull)
  trainingId String?
}
