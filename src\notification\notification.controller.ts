import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
  Req,
  ForbiddenException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { NotificationService } from './notification.service';
import {
  CreateNotificationDto,
  UpdateNotificationReadDto,
  NotificationFilterDto,
  BulkNotificationDto,
  NotificationResponseDto,
  NotificationListResponseDto,
} from './dto/notification.dto';
import { JwtGuard } from '../guards/jwt.guard';
import { JwtAdmin } from '../guards/jwt.admin.guard';
import { JwtAdminAgent } from '../guards/jwt.admin-agent.guard';
import { GetUser } from '../decorator/user.decorator';
import { IJWTPayload } from '../types/auth';

/**
 * Controller for managing user notifications
 * Provides endpoints for CRUD operations on notifications
 */
@ApiTags('notifications')
@Controller('api/notifications')
@UseGuards(JwtGuard)
@ApiBearerAuth()
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  /**
   * Create a new notification (Admin/Agent only)
   * This endpoint allows admins and agents to create notifications for users
   */
  @Post()
  @UseGuards(JwtAdminAgent)
  @ApiOperation({
    summary: 'Create a new notification (Admin/Agent only)',
    description:
      'Create a notification for a specific user. Only admins and agents can create notifications.',
  })
  @ApiResponse({
    status: 201,
    description: 'Notification created successfully',
    type: NotificationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  async createNotification(
    @Body() createNotificationDto: CreateNotificationDto,
  ): Promise<NotificationResponseDto> {
    return this.notificationService.createNotification(createNotificationDto);
  }

  /**
   * Get notifications for a specific user
   * Users can only access their own notifications, admins/agents can access any user's notifications
   */
  @Get('user/:userId')
  @ApiOperation({
    summary: 'Get notifications for a user',
    description:
      'Retrieve paginated notifications for a specific user with optional filtering.',
  })
  @ApiParam({
    name: 'userId',
    description: 'The ID of the user to get notifications for',
    example: 'clx1234567890abcdef',
  })
  @ApiQuery({
    name: 'type',
    required: false,
    description: 'Filter by notification type',
    enum: ['INFO', 'WARNING', 'SUCCESS', 'ERROR'],
  })
  @ApiQuery({
    name: 'read',
    required: false,
    description: 'Filter by read status',
    type: Boolean,
  })
  @ApiQuery({
    name: 'fromDate',
    required: false,
    description: 'Filter notifications created after this date (ISO string)',
    example: '2024-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'toDate',
    required: false,
    description: 'Filter notifications created before this date (ISO string)',
    example: '2024-12-31T23:59:59.999Z',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page (max 100)',
    example: 10,
  })
  @ApiResponse({
    status: 200,
    description: 'Notifications retrieved successfully',
    type: NotificationListResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Cannot access other user's notifications",
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  async getUserNotifications(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Query() filters: NotificationFilterDto,
    @GetUser() currentUser: IJWTPayload,
    @Req() request: any,
  ): Promise<NotificationListResponseDto> {
    // Users can only access their own notifications unless they are admin/agent
    const userRole = request.userRole;
    if (
      currentUser.id !== userId &&
      userRole !== 'admin' &&
      userRole !== 'agent'
    ) {
      throw new ForbiddenException("Cannot access other user's notifications");
    }

    return this.notificationService.getUserNotifications(userId, filters);
  }

  /**
   * Mark a notification as read or unread
   */
  @Put(':id/read')
  @ApiOperation({
    summary: 'Update notification read status',
    description:
      'Mark a notification as read or unread. Users can only update their own notifications.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the notification to update',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 200,
    description: 'Notification read status updated successfully',
    type: NotificationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 404,
    description: 'Notification not found or does not belong to user',
  })
  async updateNotificationReadStatus(
    @Param('id', ParseUUIDPipe) notificationId: string,
    @Body() updateDto: UpdateNotificationReadDto,
    @GetUser() user: IJWTPayload,
  ): Promise<NotificationResponseDto> {
    return this.notificationService.updateNotificationReadStatus(
      notificationId,
      user.id,
      updateDto,
    );
  }

  /**
   * Delete a notification
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete a notification',
    description:
      'Delete a specific notification. Users can only delete their own notifications.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the notification to delete',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 204,
    description: 'Notification deleted successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 404,
    description: 'Notification not found or does not belong to user',
  })
  async deleteNotification(
    @Param('id', ParseUUIDPipe) notificationId: string,
    @GetUser() user: IJWTPayload,
  ): Promise<void> {
    await this.notificationService.deleteNotification(notificationId, user.id);
  }

  /**
   * Mark multiple notifications as read
   */
  @Put('bulk/read')
  @ApiOperation({
    summary: 'Mark multiple notifications as read',
    description: 'Mark multiple notifications as read in a single request.',
  })
  @ApiResponse({
    status: 200,
    description: 'Notifications marked as read successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Successfully marked 5 notifications as read',
        },
        count: { type: 'number', example: 5 },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description:
      'Invalid input data or some notifications do not belong to user',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  async markMultipleAsRead(
    @Body() bulkDto: BulkNotificationDto,
    @GetUser() user: IJWTPayload,
  ): Promise<{ message: string; count: number }> {
    return this.notificationService.markMultipleAsRead(user.id, bulkDto);
  }

  /**
   * Delete multiple notifications
   */
  @Delete('bulk')
  @ApiOperation({
    summary: 'Delete multiple notifications',
    description: 'Delete multiple notifications in a single request.',
  })
  @ApiResponse({
    status: 200,
    description: 'Notifications deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Successfully deleted 5 notifications',
        },
        count: { type: 'number', example: 5 },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description:
      'Invalid input data or some notifications do not belong to user',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  async deleteMultipleNotifications(
    @Body() bulkDto: BulkNotificationDto,
    @GetUser() user: IJWTPayload,
  ): Promise<{ message: string; count: number }> {
    return this.notificationService.deleteMultipleNotifications(
      user.id,
      bulkDto,
    );
  }

  /**
   * Get unread notification count for current user
   */
  @Get('unread/count')
  @ApiOperation({
    summary: 'Get unread notification count',
    description: 'Get the count of unread notifications for the current user.',
  })
  @ApiResponse({
    status: 200,
    description: 'Unread count retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        count: { type: 'number', example: 5 },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  async getUnreadCount(
    @GetUser() user: IJWTPayload,
  ): Promise<{ count: number }> {
    return this.notificationService.getUnreadCount(user.id);
  }
}
