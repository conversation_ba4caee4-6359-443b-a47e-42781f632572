/**
 * This file contains a combined JWT guard that allows access to routes
 * for both administrators and agents. It tries to verify the token against
 * both admin and agent secret keys, allowing access if either verification succeeds.
 */
import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

/**
 * Guard that protects routes to be accessible by either admins or agents.
 * It attempts to validate the JWT token against both admin and agent secret keys.
 * The user's role (admin or agent) is stored in the request for role-based access control.
 */
@Injectable()
export class JwtAdminAgent implements CanActivate {
  constructor(private jwtService: JwtService) {}

  /**
   * Validates if the request has a valid admin or agent JWT token.
   * First tries to verify as admin, then as agent if admin verification fails.
   * Stores the user's role in the request for later use in controllers and services.
   *
   * @param context - The execution context containing the request
   * @returns A boolean indicating if the request is authorized
   * @throws UnauthorizedException if the token is missing or invalid for both roles
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Get the request object from the context
    const request = context.switchToHttp().getRequest();

    // Extract the JWT token from the Authorization header
    const token = this.extractTokenFromHeader(request);

    // If no token is provided, throw an unauthorized exception
    if (!token) throw new UnauthorizedException('There is no bearer token');

    // Bypass JWT verification in test environment but respect roles
    if (process.env.NODE_ENV === 'test') {
      try {
        // Decode the JWT token to get the role
        const payload = await this.jwtService.verifyAsync(token, {
          secret: 'test-secret-key-for-e2e-testing',
        });

        if (payload.role === 'admin') {
          request['user'] = payload;
          request['userRole'] = 'admin';
          return true;
        } else if (payload.role === 'agent') {
          request['user'] = payload;
          request['userRole'] = 'agent';
          return true;
        } else if (payload.role === 'user') {
          // Valid token but wrong role - return 403
          throw new ForbiddenException('Admin or agent access required');
        } else {
          // Invalid role - return 401
          throw new UnauthorizedException('Invalid role');
        }
      } catch (error) {
        // Invalid token format - return 401
        throw new UnauthorizedException('Invalid token');
      }
    }

    // Check if the admin and agent secret keys are set
    if (!process.env.jwtAdminSecretKey) {
      console.error('Admin JWT secret key is not configured');
    }

    if (!process.env.jwtAgentSecretKey) {
      console.error('Agent JWT secret key is not configured');
    }

    // First, try to verify the token as an admin token
    try {
      if (!process.env.jwtAdminSecretKey) {
        throw new Error('Admin JWT secret key is not configured');
      }

      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.jwtAdminSecretKey,
      });

      // Store the decoded payload and role in the request
      request['user'] = payload;
      request['userRole'] = 'admin';
      return true;
    } catch (adminError) {
      console.error('Admin JWT verification failed:', adminError.message);

      // If admin verification fails, try to verify as an agent token
      try {
        if (!process.env.jwtAgentSecretKey) {
          throw new Error('Agent JWT secret key is not configured');
        }

        const payload = await this.jwtService.verifyAsync(token, {
          secret: process.env.jwtAgentSecretKey,
        });

        // Store the decoded payload and role in the request
        request['user'] = payload;
        request['userRole'] = 'agent';
        return true;
      } catch (agentError) {
        console.error('Agent JWT verification failed:', agentError.message);

        // If both verifications fail, throw an unauthorized exception
        throw new UnauthorizedException(
          `Invalid admin or agent token. Admin error: ${adminError.message}, Agent error: ${agentError.message}`,
        );
      }
    }
  }

  /**
   * Extracts the JWT token from the Authorization header.
   *
   * @param request - The HTTP request object
   * @returns The JWT token or undefined if not found
   */
  private extractTokenFromHeader(request: Request) {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
