import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../utils/prisma.service';
import { MailerService } from '../mailer/mailer.service';
import {
  CreateReminderDto,
  UpdateReminderStatusDto,
  UpdateReminderDto,
  ReminderFilterDto,
  BulkReminderDto,
  ReminderResponseDto,
  ReminderListResponseDto,
} from './dto/reminder.dto';
import { ReminderStatus } from '@prisma/client';

/**
 * Service for managing reminders
 * Handles CRUD operations for user reminders with filtering and pagination
 */
@Injectable()
export class ReminderService {
  private readonly logger = new Logger(ReminderService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly mailerService: MailerService,
  ) {}

  /**
   * Create a new reminder for a user
   * @param createReminderDto - Data for creating the reminder
   * @returns The created reminder
   */
  async createReminder(
    createReminderDto: CreateReminderDto,
  ): Promise<ReminderResponseDto> {
    try {
      this.logger.log(`Creating reminder for user ${createReminderDto.userId}`);

      // Verify user exists
      const user = await this.prisma.user.findUnique({
        where: { id: createReminderDto.userId },
      });

      if (!user) {
        throw new NotFoundException(
          `User with ID ${createReminderDto.userId} not found`,
        );
      }

      // Verify application exists if provided
      if (createReminderDto.applicationId) {
        const application = await this.prisma.application.findUnique({
          where: { id: createReminderDto.applicationId },
        });

        if (!application) {
          throw new NotFoundException(
            `Application with ID ${createReminderDto.applicationId} not found`,
          );
        }

        // Verify application belongs to the user
        if (application.userId !== createReminderDto.userId) {
          throw new BadRequestException(
            'Application does not belong to the specified user',
          );
        }
      }

      // Verify document exists if provided
      if (createReminderDto.documentId) {
        const document = await this.prisma.user_document.findUnique({
          where: { id: createReminderDto.documentId },
        });

        if (!document) {
          throw new NotFoundException(
            `Document with ID ${createReminderDto.documentId} not found`,
          );
        }

        // Verify document belongs to the user
        if (document.userId !== createReminderDto.userId) {
          throw new BadRequestException(
            'Document does not belong to the specified user',
          );
        }
      }

      const reminder = await this.prisma.reminder.create({
        data: {
          message: createReminderDto.message,
          dueDate: new Date(createReminderDto.dueDate),
          userId: createReminderDto.userId,
          applicationId: createReminderDto.applicationId,
          documentId: createReminderDto.documentId,
        },
      });

      this.logger.log(`Reminder created with ID: ${reminder.id}`);

      // Send email reminder asynchronously (don't wait for it to complete)
      this.sendEmailReminder(user, reminder).catch((emailError) => {
        this.logger.error(
          `Failed to send email reminder: ${emailError.message}`,
          emailError.stack,
        );
        // Don't throw the error - reminder creation should succeed even if email fails
      });

      return this.addOverdueFlag(reminder);
    } catch (error) {
      this.logger.error(
        `Failed to create reminder: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get reminders for a specific user with filtering and pagination
   * @param userId - The user ID to get reminders for
   * @param filters - Filter and pagination options
   * @returns Paginated list of reminders
   */
  async getUserReminders(
    userId: string,
    filters: ReminderFilterDto,
  ): Promise<ReminderListResponseDto> {
    try {
      this.logger.log(
        `Getting reminders for user ${userId} with filters:`,
        filters,
      );

      // Verify user exists
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Build where clause for filtering
      const where: any = {
        userId,
      };

      if (filters.status) {
        where.status = filters.status;
      }

      if (filters.applicationId) {
        where.applicationId = filters.applicationId;
      }

      if (filters.documentId) {
        where.documentId = filters.documentId;
      }

      if (filters.fromDate || filters.toDate) {
        where.dueDate = {};
        if (filters.fromDate) {
          where.dueDate.gte = new Date(filters.fromDate);
        }
        if (filters.toDate) {
          where.dueDate.lte = new Date(filters.toDate);
        }
      }

      // Calculate pagination
      const page = Math.max(1, filters.page || 1);
      const limit = Math.max(1, Math.min(100, filters.limit || 10)); // Max 100 items per page
      const skip = (page - 1) * limit;

      // Get total count for pagination
      const total = await this.prisma.reminder.count({ where });

      // Get reminders with pagination
      const reminders = await this.prisma.reminder.findMany({
        where,
        orderBy: { dueDate: 'asc' },
        skip,
        take: limit,
        include: {
          application: {
            select: {
              id: true,
              status: true,
              serviceType: {
                select: {
                  name: true,
                },
              },
            },
          },
          document: {
            select: {
              id: true,
              documentName: true,
              verificationStatus: true,
            },
          },
        },
      });

      // Get pending count
      const pendingCount = await this.prisma.reminder.count({
        where: {
          userId,
          status: ReminderStatus.PENDING,
        },
      });

      // Get overdue count
      const overdueCount = await this.prisma.reminder.count({
        where: {
          userId,
          status: ReminderStatus.OVERDUE,
        },
      });

      const totalPages = Math.ceil(total / limit);

      // Add overdue flags to reminders
      const remindersWithFlags = reminders.map((reminder) =>
        this.addOverdueFlag(reminder),
      );

      this.logger.log(
        `Retrieved ${reminders.length} reminders for user ${userId}`,
      );

      return {
        reminders: remindersWithFlags,
        total,
        page,
        limit,
        totalPages,
        pendingCount,
        overdueCount,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get reminders for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Update reminder status
   * @param reminderId - The reminder ID to update
   * @param userId - The user ID (for authorization)
   * @param updateDto - The update data
   * @returns The updated reminder
   */
  async updateReminderStatus(
    reminderId: string,
    userId: string,
    updateDto: UpdateReminderStatusDto,
  ): Promise<ReminderResponseDto> {
    try {
      this.logger.log(
        `Updating reminder ${reminderId} status for user ${userId}`,
      );

      // Find the reminder and verify ownership
      const existingReminder = await this.prisma.reminder.findFirst({
        where: {
          id: reminderId,
          userId,
        },
      });

      if (!existingReminder) {
        throw new NotFoundException(
          `Reminder with ID ${reminderId} not found or does not belong to user ${userId}`,
        );
      }

      const updatedReminder = await this.prisma.reminder.update({
        where: { id: reminderId },
        data: { status: updateDto.status },
      });

      this.logger.log(
        `Reminder ${reminderId} status updated to ${updateDto.status}`,
      );
      return this.addOverdueFlag(updatedReminder);
    } catch (error) {
      this.logger.error(
        `Failed to update reminder ${reminderId} status: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Update reminder details
   * @param reminderId - The reminder ID to update
   * @param userId - The user ID (for authorization)
   * @param updateDto - The update data
   * @returns The updated reminder
   */
  async updateReminder(
    reminderId: string,
    userId: string,
    updateDto: UpdateReminderDto,
  ): Promise<ReminderResponseDto> {
    try {
      this.logger.log(`Updating reminder ${reminderId} for user ${userId}`);

      // Find the reminder and verify ownership
      const existingReminder = await this.prisma.reminder.findFirst({
        where: {
          id: reminderId,
          userId,
        },
      });

      if (!existingReminder) {
        throw new NotFoundException(
          `Reminder with ID ${reminderId} not found or does not belong to user ${userId}`,
        );
      }

      const updateData: any = {};
      if (updateDto.message !== undefined)
        updateData.message = updateDto.message;
      if (updateDto.dueDate !== undefined)
        updateData.dueDate = new Date(updateDto.dueDate);
      if (updateDto.status !== undefined) updateData.status = updateDto.status;

      const updatedReminder = await this.prisma.reminder.update({
        where: { id: reminderId },
        data: updateData,
      });

      this.logger.log(`Reminder ${reminderId} updated successfully`);
      return this.addOverdueFlag(updatedReminder);
    } catch (error) {
      this.logger.error(
        `Failed to update reminder ${reminderId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Delete a reminder
   * @param reminderId - The reminder ID to delete
   * @param userId - The user ID (for authorization)
   * @returns Success message
   */
  async deleteReminder(
    reminderId: string,
    userId: string,
  ): Promise<{ message: string }> {
    try {
      this.logger.log(`Deleting reminder ${reminderId} for user ${userId}`);

      // Find the reminder and verify ownership
      const existingReminder = await this.prisma.reminder.findFirst({
        where: {
          id: reminderId,
          userId,
        },
      });

      if (!existingReminder) {
        throw new NotFoundException(
          `Reminder with ID ${reminderId} not found or does not belong to user ${userId}`,
        );
      }

      await this.prisma.reminder.delete({
        where: { id: reminderId },
      });

      this.logger.log(`Reminder ${reminderId} deleted successfully`);
      return { message: 'Reminder deleted successfully' };
    } catch (error) {
      this.logger.error(
        `Failed to delete reminder ${reminderId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Mark multiple reminders as completed
   * @param userId - The user ID
   * @param bulkDto - The reminder IDs to mark as completed
   * @returns Success message with count
   */
  async markMultipleAsCompleted(
    userId: string,
    bulkDto: BulkReminderDto,
  ): Promise<{ message: string; count: number }> {
    try {
      this.logger.log(
        `Marking ${bulkDto.reminderIds.length} reminders as completed for user ${userId}`,
      );

      // Verify all reminders belong to the user
      const existingReminders = await this.prisma.reminder.findMany({
        where: {
          id: { in: bulkDto.reminderIds },
          userId,
        },
        select: { id: true },
      });

      if (existingReminders.length !== bulkDto.reminderIds.length) {
        throw new BadRequestException(
          'Some reminders do not exist or do not belong to the user',
        );
      }

      const result = await this.prisma.reminder.updateMany({
        where: {
          id: { in: bulkDto.reminderIds },
          userId,
        },
        data: { status: ReminderStatus.COMPLETED },
      });

      this.logger.log(
        `Marked ${result.count} reminders as completed for user ${userId}`,
      );
      return {
        message: `Successfully marked ${result.count} reminders as completed`,
        count: result.count,
      };
    } catch (error) {
      this.logger.error(
        `Failed to mark reminders as completed for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get overdue reminders for all users (for background processing)
   * @returns Array of overdue reminders
   */
  async getOverdueReminders(): Promise<ReminderResponseDto[]> {
    try {
      const now = new Date();
      const overdueReminders = await this.prisma.reminder.findMany({
        where: {
          dueDate: { lt: now },
          status: ReminderStatus.PENDING,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return overdueReminders.map((reminder) => this.addOverdueFlag(reminder));
    } catch (error) {
      this.logger.error(
        `Failed to get overdue reminders: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Update overdue reminders status
   * @returns Count of updated reminders
   */
  async updateOverdueReminders(): Promise<{ count: number }> {
    try {
      const now = new Date();
      const result = await this.prisma.reminder.updateMany({
        where: {
          dueDate: { lt: now },
          status: ReminderStatus.PENDING,
        },
        data: { status: ReminderStatus.OVERDUE },
      });

      this.logger.log(`Updated ${result.count} reminders to overdue status`);
      return { count: result.count };
    } catch (error) {
      this.logger.error(
        `Failed to update overdue reminders: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Send email reminder to user
   * @param user - User object with email and name
   * @param reminder - Reminder object
   * @private
   */
  private async sendEmailReminder(user: any, reminder: any): Promise<void> {
    try {
      if (!user.email) {
        this.logger.warn(
          `User ${user.id} has no email address, skipping email reminder`,
        );
        return;
      }

      const now = new Date();
      const isOverdue = reminder.dueDate < now;

      await this.mailerService.sendReminderEmail({
        to: user.email,
        userName: user.name || user.email,
        message: reminder.message,
        dueDate: reminder.dueDate.toISOString(),
        isOverdue,
        actionUrl: process.env.FRONTEND_URL
          ? `${process.env.FRONTEND_URL}/reminders`
          : undefined,
        actionText: 'View Reminders',
      });

      this.logger.log(
        `Email reminder sent to ${user.email} for reminder ${reminder.id}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send email reminder: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Send overdue reminder digest emails to users
   * @param userOverdueMap - Map of user ID to overdue reminders
   * @returns Promise that resolves when all emails are sent
   */
  async sendOverdueReminderDigests(
    userOverdueMap: Map<string, any[]>,
  ): Promise<void> {
    try {
      this.logger.log(
        `Sending overdue reminder digests to ${userOverdueMap.size} users`,
      );

      const emailPromises = Array.from(userOverdueMap.entries()).map(
        async ([userId, reminders]) => {
          try {
            const user = await this.prisma.user.findUnique({
              where: { id: userId },
              select: { id: true, name: true, email: true },
            });

            if (!user || !user.email) {
              this.logger.warn(
                `User ${userId} has no email address, skipping digest`,
              );
              return;
            }

            const overdueReminders = reminders.map((reminder) => ({
              message: reminder.message,
              dueDate: reminder.dueDate.toISOString(),
              daysPastDue: Math.ceil(
                (new Date().getTime() - reminder.dueDate.getTime()) /
                  (1000 * 60 * 60 * 24),
              ),
              applicationName: reminder.application?.serviceType?.name,
              documentName: reminder.document?.documentName,
            }));

            await this.mailerService.sendOverdueReminderDigest({
              to: user.email,
              userName: user.name || user.email,
              overdueReminders,
              dashboardUrl: process.env.FRONTEND_URL
                ? `${process.env.FRONTEND_URL}/dashboard`
                : undefined,
            });

            this.logger.log(
              `Overdue digest sent to ${user.email} with ${overdueReminders.length} reminders`,
            );
          } catch (error) {
            this.logger.error(
              `Failed to send overdue digest to user ${userId}: ${error.message}`,
              error.stack,
            );
          }
        },
      );

      await Promise.allSettled(emailPromises);
      this.logger.log('Completed sending overdue reminder digests');
    } catch (error) {
      this.logger.error(
        `Failed to send overdue reminder digests: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Add overdue flag to reminder based on current date
   * @param reminder - The reminder object
   * @returns Reminder with overdue flag
   */
  private addOverdueFlag(reminder: any): ReminderResponseDto {
    const now = new Date();
    const isOverdue =
      reminder.dueDate < now && reminder.status === ReminderStatus.PENDING;

    return {
      ...reminder,
      isOverdue,
    };
  }
}
