// Immigration Service System Models

// 1. ServiceType Model
model service_type {
  id                   String                 @id @default(cuid())
  name                 String
  description          String
  price                Decimal
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt
  documentRequirements document_requirement[]
  workflowSteps        workflow_step[]
  applications         application[]
  documents            user_document[]
}

// 2. DocumentRequirement Model
model document_requirement {
  id            String          @id @default(cuid())
  name          String
  description   String
  required      Boolean         @default(true)
  category      DocCategory
  serviceType   service_type    @relation(fields: [serviceTypeId], references: [id], onDelete: Cascade)
  serviceTypeId String
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  documents     user_document[]
}

// 3. UserDocument Model
model user_document {
  id                     String               @id @default(cuid())
  documentName           String
  fileUrl                String
  uploadDate             DateTime             @default(now())
  verificationStatus     VerificationStatus   @default(PENDING)
  rejectionReason        String?
  expiryDate             DateTime?
  category               DocCategory?
  notes                  String?
  user                   user                 @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId                 String
  serviceType            service_type?        @relation(fields: [serviceTypeId], references: [id], onDelete: SetNull)
  serviceTypeId          String?
  application            application?         @relation(fields: [applicationId], references: [id], onDelete: SetNull)
  applicationId          String?
  documentRequirement    document_requirement? @relation(fields: [documentRequirementId], references: [id], onDelete: SetNull)
  documentRequirementId  String?
  createdAt              DateTime             @default(now())
  updatedAt              DateTime             @updatedAt
  reminders              reminder[]
}

// 4. Application Model
model application {
  id               String                @id @default(cuid())
  status           ApplicationStatus     @default(DRAFT)
  submissionDate   DateTime?
  completionDate   DateTime?
  user             user                  @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId           String
  serviceType      service_type          @relation(fields: [serviceTypeId], references: [id])
  serviceTypeId    String
  createdAt        DateTime              @default(now())
  updatedAt        DateTime              @updatedAt
  workflow         application_workflow?
  checkpointCalls  checkpoint_call[]
  applicationQuery application_query[]
  reminders        reminder[]
  documents        user_document[]
}

// 5. ApplicationWorkflow Model
model application_workflow {
  id            String         @id @default(cuid())
  currentStep   Int            @default(1)
  status        WorkflowStatus @default(NOT_STARTED)
  startDate     DateTime       @default(now())
  lastUpdated   DateTime       @updatedAt
  application   application    @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  applicationId String         @unique
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
}

// 6. WorkflowStep Model
model workflow_step {
  id                String       @id @default(cuid())
  name              String
  description       String
  order             Int
  estimatedDuration Int // in days
  serviceType       service_type @relation(fields: [serviceTypeId], references: [id], onDelete: Cascade)
  serviceTypeId     String
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @updatedAt
}

// 7. CheckpointCall Model
model checkpoint_call {
  id             String         @id @default(cuid())
  scheduledDate  DateTime
  completedDate  DateTime?
  notes          String?
  status         CallStatus     @default(SCHEDULED)
  application    application    @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  applicationId  String
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
}

// 8. ApplicationQuery Model
model application_query {
  id            String      @id @default(cuid())
  queryText     String
  responseText  String?
  queryDate     DateTime    @default(now())
  responseDate  DateTime?
  status        QueryStatus @default(PENDING)
  application   application @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  applicationId String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
}

// 9. Notification Model
model notification {
  id        String           @id @default(cuid())
  message   String
  type      NotificationType @default(INFO)
  read      Boolean          @default(false)
  user      user             @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
}

// 10. Reminder Model
model reminder {
  id            String        @id @default(cuid())
  message       String
  dueDate       DateTime
  status        ReminderStatus @default(PENDING)
  user          user           @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId        String
  application   application?   @relation(fields: [applicationId], references: [id], onDelete: SetNull)
  applicationId String?
  document      user_document? @relation(fields: [documentId], references: [id], onDelete: SetNull)
  documentId    String?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
}

// Enums for the models
enum DocCategory {
  EMPLOYEE
  EMPLOYER
}

enum VerificationStatus {
  PENDING
  APPROVED
  REJECTED
}

enum ApplicationStatus {
  DRAFT
  SUBMITTED
  IN_PROGRESS
  COMPLETED
  REJECTED
}

enum WorkflowStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  BLOCKED
}

enum CallStatus {
  SCHEDULED
  COMPLETED
  MISSED
  RESCHEDULED
}

enum QueryStatus {
  PENDING
  RESPONDED
  CLOSED
}

enum NotificationType {
  INFO
  WARNING
  SUCCESS
  ERROR
}

enum ReminderStatus {
  PENDING
  COMPLETED
  OVERDUE
}
