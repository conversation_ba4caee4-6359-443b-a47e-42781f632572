import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, Controller, Get, UseGuards } from '@nestjs/common';
import * as request from 'supertest';
import { AppController } from '../src/app.controller';
import { AppService } from '../src/app.service';
import { JwtGuard } from '../src/guards/jwt.guard';
import { JwtService } from '@nestjs/jwt';

/**
 * Simple test for authentication behavior
 *
 * This test verifies that endpoints protected by JwtGuard return 401 Unauthorized
 * when accessed without authentication.
 */
describe('Authentication (e2e)', () => {
  let app: INestApplication;

  // Create a simple controller with protected and public endpoints
  @Controller('test')
  class TestController {
    @Get('protected')
    @UseGuards(JwtGuard)
    getProtected() {
      return { message: 'This is protected' };
    }

    @Get('public')
    getPublic() {
      return { message: 'This is public' };
    }
  }

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [AppController, TestController],
      providers: [
        AppService,
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
            verify: jest.fn(),
            verifyAsync: jest
              .fn()
              .mockRejectedValue(new Error('Invalid token')),
          },
        },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('/ (GET) - public endpoint should be accessible without authentication', () => {
    return request(app.getHttpServer())
      .get('/')
      .expect(200)
      .expect('Welcome to Careerireland api');
  });

  it('/test/protected (GET) - protected endpoint should return 401 without authentication', () => {
    return request(app.getHttpServer()).get('/test/protected').expect(401);
  });
});
