import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
  Req,
  ForbiddenException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { ReminderService } from './reminder.service';
import {
  CreateReminderDto,
  UpdateReminderStatusDto,
  UpdateReminderDto,
  ReminderFilterDto,
  BulkReminderDto,
  ReminderResponseDto,
  ReminderListResponseDto,
} from './dto/reminder.dto';
import { JwtGuard } from '../guards/jwt.guard';
import { JwtAdmin } from '../guards/jwt.admin.guard';
import { JwtAdminAgent } from '../guards/jwt.admin-agent.guard';
import { GetUser } from '../decorator/user.decorator';
import { IJWTPayload } from '../types/auth';
import { ReminderStatus } from '@prisma/client';

/**
 * Controller for managing user reminders
 * Provides endpoints for CRUD operations on reminders
 */
@ApiTags('reminders')
@Controller('api/reminders')
@UseGuards(JwtGuard)
@ApiBearerAuth()
export class ReminderController {
  constructor(private readonly reminderService: ReminderService) {}

  /**
   * Create a new reminder (Admin/Agent only)
   * This endpoint allows admins and agents to create reminders for users
   */
  @Post()
  @UseGuards(JwtAdminAgent)
  @ApiOperation({
    summary: 'Create a new reminder (Admin/Agent only)',
    description:
      'Create a reminder for a specific user. Only admins and agents can create reminders.',
  })
  @ApiResponse({
    status: 201,
    description: 'Reminder created successfully',
    type: ReminderResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'User, application, or document not found',
  })
  async createReminder(
    @Body() createReminderDto: CreateReminderDto,
  ): Promise<ReminderResponseDto> {
    return this.reminderService.createReminder(createReminderDto);
  }

  /**
   * Get reminders for a specific user
   * Users can only access their own reminders, admins/agents can access any user's reminders
   */
  @Get('user/:userId')
  @ApiOperation({
    summary: 'Get reminders for a user',
    description:
      'Retrieve paginated reminders for a specific user with optional filtering.',
  })
  @ApiParam({
    name: 'userId',
    description: 'The ID of the user to get reminders for',
    example: 'clx1234567890abcdef',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by reminder status',
    enum: ['PENDING', 'COMPLETED', 'OVERDUE'],
  })
  @ApiQuery({
    name: 'fromDate',
    required: false,
    description: 'Filter reminders due after this date (ISO string)',
    example: '2024-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'toDate',
    required: false,
    description: 'Filter reminders due before this date (ISO string)',
    example: '2024-12-31T23:59:59.999Z',
  })
  @ApiQuery({
    name: 'applicationId',
    required: false,
    description: 'Filter by application ID',
    example: 'clx1234567890abcdef',
  })
  @ApiQuery({
    name: 'documentId',
    required: false,
    description: 'Filter by document ID',
    example: 'clx1234567890abcdef',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page (max 100)',
    example: 10,
  })
  @ApiResponse({
    status: 200,
    description: 'Reminders retrieved successfully',
    type: ReminderListResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Cannot access other user's reminders",
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  async getUserReminders(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Query() filters: ReminderFilterDto,
    @GetUser() currentUser: IJWTPayload,
    @Req() request: any,
  ): Promise<ReminderListResponseDto> {
    // Users can only access their own reminders unless they are admin/agent
    const userRole = request.userRole;
    if (
      currentUser.id !== userId &&
      userRole !== 'admin' &&
      userRole !== 'agent'
    ) {
      throw new ForbiddenException("Cannot access other user's reminders");
    }

    return this.reminderService.getUserReminders(userId, filters);
  }

  /**
   * Update reminder status (mark as completed)
   */
  @Put(':id/complete')
  @ApiOperation({
    summary: 'Mark reminder as completed',
    description:
      'Mark a reminder as completed. Users can only update their own reminders.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the reminder to update',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 200,
    description: 'Reminder status updated successfully',
    type: ReminderResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 404,
    description: 'Reminder not found or does not belong to user',
  })
  async markReminderAsCompleted(
    @Param('id', ParseUUIDPipe) reminderId: string,
    @GetUser() user: IJWTPayload,
  ): Promise<ReminderResponseDto> {
    return this.reminderService.updateReminderStatus(reminderId, user.id, {
      status: ReminderStatus.COMPLETED,
    });
  }

  /**
   * Update reminder status
   */
  @Put(':id/status')
  @ApiOperation({
    summary: 'Update reminder status',
    description:
      'Update the status of a reminder. Users can only update their own reminders.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the reminder to update',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 200,
    description: 'Reminder status updated successfully',
    type: ReminderResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 404,
    description: 'Reminder not found or does not belong to user',
  })
  async updateReminderStatus(
    @Param('id', ParseUUIDPipe) reminderId: string,
    @Body() updateDto: UpdateReminderStatusDto,
    @GetUser() user: IJWTPayload,
  ): Promise<ReminderResponseDto> {
    return this.reminderService.updateReminderStatus(
      reminderId,
      user.id,
      updateDto,
    );
  }

  /**
   * Update reminder details (Admin/Agent only)
   */
  @Put(':id')
  @UseGuards(JwtAdminAgent)
  @ApiOperation({
    summary: 'Update reminder details (Admin/Agent only)',
    description:
      'Update reminder message, due date, or status. Only admins and agents can update reminder details.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the reminder to update',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 200,
    description: 'Reminder updated successfully',
    type: ReminderResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Reminder not found',
  })
  async updateReminder(
    @Param('id', ParseUUIDPipe) reminderId: string,
    @Body() updateDto: UpdateReminderDto,
    @GetUser() user: IJWTPayload,
  ): Promise<ReminderResponseDto> {
    // For admin/agent, we need to get the reminder's userId
    // For now, we'll use the current user's ID, but this should be enhanced
    return this.reminderService.updateReminder(reminderId, user.id, updateDto);
  }

  /**
   * Delete a reminder
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete a reminder',
    description:
      'Delete a specific reminder. Users can only delete their own reminders.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the reminder to delete',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 204,
    description: 'Reminder deleted successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 404,
    description: 'Reminder not found or does not belong to user',
  })
  async deleteReminder(
    @Param('id', ParseUUIDPipe) reminderId: string,
    @GetUser() user: IJWTPayload,
  ): Promise<void> {
    await this.reminderService.deleteReminder(reminderId, user.id);
  }

  /**
   * Mark multiple reminders as completed
   */
  @Put('bulk/complete')
  @ApiOperation({
    summary: 'Mark multiple reminders as completed',
    description: 'Mark multiple reminders as completed in a single request.',
  })
  @ApiResponse({
    status: 200,
    description: 'Reminders marked as completed successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Successfully marked 5 reminders as completed',
        },
        count: { type: 'number', example: 5 },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data or some reminders do not belong to user',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  async markMultipleAsCompleted(
    @Body() bulkDto: BulkReminderDto,
    @GetUser() user: IJWTPayload,
  ): Promise<{ message: string; count: number }> {
    return this.reminderService.markMultipleAsCompleted(user.id, bulkDto);
  }

  /**
   * Get overdue reminders (Admin only)
   */
  @Get('overdue')
  @UseGuards(JwtAdmin)
  @ApiOperation({
    summary: 'Get all overdue reminders (Admin only)',
    description:
      'Retrieve all overdue reminders across all users. Only admins can access this endpoint.',
  })
  @ApiResponse({
    status: 200,
    description: 'Overdue reminders retrieved successfully',
    type: [ReminderResponseDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async getOverdueReminders(): Promise<ReminderResponseDto[]> {
    return this.reminderService.getOverdueReminders();
  }
}
